package com.paytm.recharges.bff.utils;

import com.aerospike.client.AerospikeClient;
import com.aerospike.client.Bin;
import com.aerospike.client.Key;
import com.aerospike.client.Operation;
import com.aerospike.client.Record;
import com.aerospike.client.policy.WritePolicy;
import com.paytm.recharges.bff.constants.Constants;
import com.paytm.recharges.bff.constants.InterstitialConstants;
import com.paytm.recharges.bff.datalayer.cache.ServiceConfigCache;
import com.paytm.recharges.bff.datalayer.dto.request.InterstitialRequest;
import com.paytm.recharges.bff.datalayer.dto.response.ApiResponse;
import com.paytm.recharges.bff.datalayer.model.CacheData;
import com.paytm.recharges.bff.monitor.MetricsAgent;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.paytm.recharges.bff.constants.Constants.INTERSTITIAL_SEEN_COUNT_BIN;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class InterstitialUtilsTest {

    @Mock
    private AerospikeClient aerospikeClient;

    @Mock
    private MetricsAgent metricsAgent;

    @InjectMocks
    private InterstitialUtils interstitialUtils;

    private InterstitialRequest mockRequest;

    @BeforeEach
    void setUp() {
        mockRequest = new InterstitialRequest();
        mockRequest.setCustomerId(123456L);
        mockRequest.setRechargeNumber("9876543210");
        mockRequest.setOperator("Airtel");
        mockRequest.setService("mobile");
        mockRequest.setDueDate("2023-04-15");
        mockRequest.setPlanBucket("UNLIMITED");
    }

    @Test
    void updateInterstitialCache_ShouldUpdateAllCounters() {
        // Arrange
        Record mockRecord = Mockito.mock(Record.class);
        when(mockRecord.getInt(INTERSTITIAL_SEEN_COUNT_BIN)).thenReturn(1);
        when(mockRecord.getTimeToLive()).thenReturn(86400);
        
        when(aerospikeClient.operate(any(WritePolicy.class), any(Key.class), any(Operation.class), any(Operation.class)))
            .thenReturn(mockRecord);
        
        try (MockedStatic<ServiceConfigCache> mockedServiceConfigCache = Mockito.mockStatic(ServiceConfigCache.class);
             MockedStatic<DateUtil> mockedDateUtil = Mockito.mockStatic(DateUtil.class)) {
            
            ServiceConfigCache mockCache = mock(ServiceConfigCache.class);
            mockedServiceConfigCache.when(ServiceConfigCache::getInstance).thenReturn(mockCache);
            
            when(mockCache.getInteger(Constants.COOL_OFF_DAYS_KEY)).thenReturn(3);
            
            mockedDateUtil.when(DateUtil::getEndOfMonthTTL).thenReturn(2592000L); // 30 days
            mockedDateUtil.when(DateUtil::getEndOfDayTTL).thenReturn(86400L); // 1 day
            mockedDateUtil.when(() -> DateUtil.getTTLFromDate(eq("2023-04-15"), eq(Constants.DUE_DATE_FORMAT)))
                .thenReturn(1209600); // 14 days
            
            // Act
            List<CacheData> result = interstitialUtils.updateInterstitialCache(mockRequest);
            
            // Assert
            assertNotNull(result);
            assertEquals(4, result.size());
            
            // Verify monthly counter
            verify(aerospikeClient, times(4)).operate(any(WritePolicy.class), any(Key.class), any(Operation.class), any(Operation.class));
            verify(metricsAgent, times(4)).incrementEventCount(Constants.INTERSTITIAL_AEROSPIKE_UPDATE_COUNT);
        }
    }

    @Test
    void updateCounterAndLog_ShouldReturnCacheData() {
        // Arrange
        String keySuffix = "123456_APR";
        int expiration = 2592000; // 30 days
        
        Record mockRecord = Mockito.mock(Record.class);
        when(mockRecord.getInt(INTERSTITIAL_SEEN_COUNT_BIN)).thenReturn(3);
        when(mockRecord.getTimeToLive()).thenReturn(expiration);
        
        when(aerospikeClient.operate(any(WritePolicy.class), any(Key.class), any(Operation.class), any(Operation.class)))
            .thenReturn(mockRecord);
        
        // Act
        CacheData result = interstitialUtils.updateCounterAndLog(keySuffix, expiration);
        
        // Assert
        assertNotNull(result);
        assertEquals(keySuffix, result.getKey());
        assertEquals(Integer.valueOf(3), result.getSeenCount());
        assertEquals(Integer.valueOf(expiration), result.getTTL());
        
        verify(aerospikeClient).operate(any(WritePolicy.class), any(Key.class), any(Operation.class), any(Operation.class));
        verify(metricsAgent).incrementEventCount(Constants.INTERSTITIAL_AEROSPIKE_UPDATE_COUNT);
    }

    @Test
    void updateCounterAndLog_WhenRecordIsNull_ShouldReturnCacheDataWithNullValues() {
        // Arrange
        String keySuffix = "123456_APR";
        int expiration = 2592000; // 30 days
        
        when(aerospikeClient.operate(any(WritePolicy.class), any(Key.class), any(Operation.class), any(Operation.class)))
            .thenReturn(null);
        
        // Act
        CacheData result = interstitialUtils.updateCounterAndLog(keySuffix, expiration);
        
        // Assert
        assertNotNull(result);
        assertEquals(keySuffix, result.getKey());
        assertNull(result.getSeenCount());
        assertNull(result.getTTL());
        
        verify(aerospikeClient).operate(any(WritePolicy.class), any(Key.class), any(Operation.class), any(Operation.class));
        verify(metricsAgent).incrementEventCount(Constants.INTERSTITIAL_AEROSPIKE_UPDATE_COUNT);
    }

    @Test
    void buildBillKeyFromRequest_ShouldReturnCorrectFormat() {
        // Act
        String result = InterstitialUtils.buildBillKeyFromRequest(mockRequest);
        
        // Assert
        String expected = "123456_9876543210_Airtel_mobile_2023-04-15_UNLIMITED";
        assertEquals(expected, result);
    }

    @Test
    void createSuccessResponse_ShouldReturnCorrectApiResponse() {
        // Act
        ApiResponse response = InterstitialUtils.createSuccessResponse();
        
        // Assert
        assertEquals(Constants.INTERSTITIAL_IMPRESSION_SUCCESS_STATUS, response.getStatus());
        assertEquals(Constants.INTERSTITIAL_IMPRESSION_SUCCESS_CODE, response.getStatusCode());
        assertEquals(Constants.INTERSTITIAL_IMPRESSION_SUCCESS_MSG, response.getMessage());
    }

    @Test
    void createFailureResponse_ShouldReturnCorrectApiResponse() {
        // Act
        ApiResponse response = InterstitialUtils.createFailureResponse();
        
        // Assert
        assertEquals(Constants.INTERSTITIAL_IMPRESSION_FAILURE_STATUS, response.getStatus());
        assertEquals(Constants.INTERSTITIAL_IMPRESSION_FAILURE_CODE, response.getStatusCode());
        assertEquals(Constants.INTERSTITIAL_IMPRESSION_FAILURE_MSG, response.getMessage());
    }

    @Test
    void createKafkaSuccessResponse_ShouldReturnCorrectApiResponse() {
        // Act
        ApiResponse response = InterstitialUtils.createKafkaSuccessResponse();
        
        // Assert
        assertEquals(Constants.INTERSTITIAL_KAFKA_SUCCESS_STATUS, response.getStatus());
        assertEquals(Constants.INTERSTITIAL_KAFKA_SUCCESS_CODE, response.getStatusCode());
        assertEquals(Constants.INTERSTITIAL_KAFKA_SUCCESS_MSG, response.getMessage());
    }

    @Test
    void generateMonthKey_ShouldReturnCorrectFormat() {
        // Arrange
        Long customerId = 123456L;
        
        try (MockedStatic<DateUtil> mockedDateUtil = Mockito.mockStatic(DateUtil.class)) {
            mockedDateUtil.when(DateUtil::getCurrentMonth).thenReturn("APR");
            
            // Act
            String result = InterstitialUtils.generateMonthKey(customerId);
            
            // Assert
            assertEquals("123456_APR", result);
        }
    }

    @Test
    void generateDayKey_ShouldReturnCorrectFormat() {
        // Arrange
        Long customerId = 123456L;
        
        try (MockedStatic<DateUtil> mockedDateUtil = Mockito.mockStatic(DateUtil.class)) {
            mockedDateUtil.when(DateUtil::getCurrentDayOfTheMonth).thenReturn("15APR");
            
            // Act
            String result = InterstitialUtils.generateDayKey(customerId);
            
            // Assert
            assertEquals("123456_15APR", result);
        }
    }
}