package com.paytm.recharges.bff.util;

import org.junit.jupiter.api.Test;
import com.paytm.recharges.bff.config.properties.AES256Configurations;
import com.paytm.recharges.bff.exceptions.AES256Exception;
import com.paytm.recharges.bff.monitor.MetricsAgent;
import com.paytm.recharges.bff.utils.AESUtil;
import org.junit.jupiter.api.BeforeEach;
import static com.paytm.recharges.bff.constants.Constants.AES256_ENCRYPTION_ERROR;
import static com.paytm.recharges.bff.constants.Constants.AES256_DECRYPTION_ERROR;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class AESUtilTest {

	private AES256Configurations aes256Configurations;
	private MetricsAgent metricsAgent;
	private AESUtil aesUtil;

	@BeforeEach
	public void setUp() {
		aes256Configurations = mock(AES256Configurations.class);
		metricsAgent = mock(MetricsAgent.class);
		aesUtil = new AESUtil(aes256Configurations, metricsAgent);
	}

	@Test
	void testEncryptSuccess() throws AES256Exception {
		String text = "Hello, World!";
		String key = "1234567890123456";
		String ivHex = "12345678901234567890123456789012";

		when(aes256Configurations.getEncryptionPassword()).thenReturn(key);
		when(aes256Configurations.getEncryptionIvHex()).thenReturn(ivHex);

		String encryptedText = aesUtil.encrypt(text);
		assertNotNull(encryptedText);
	}

	@Test
	void testEncryptWithInvalidKey() {
		String text = "Hello, World!";
		String key = "invalid_key";
		String ivHex = "12345678901234567890123456789012";

		when(aes256Configurations.getEncryptionPassword()).thenReturn(key);
		when(aes256Configurations.getEncryptionIvHex()).thenReturn(ivHex);

		AES256Exception exception = assertThrows(AES256Exception.class, () -> aesUtil.encrypt(text));
		assertEquals("Error in encrypting the request", exception.getMessage());
		verify(metricsAgent, times(1)).incrementEventCount(AES256_ENCRYPTION_ERROR);
	}

	@Test
	void testStaticEncryptSuccess() {
		String text = "Hello, World!";
		String key = "1234567890123456";
		String ivHex = "12345678901234567890123456789012";

		String encryptedText = AESUtil.encrypt(text, key, ivHex);
		assertNotNull(encryptedText);
	}

	@Test
	void testStaticEncryptWithInvalidKey() {
		String text = "Hello, World!";
		String key = "invalid_key";
		String ivHex = "12345678901234567890123456789012";

		AES256Exception exception = assertThrows(AES256Exception.class, () -> AESUtil.encrypt(text, key, ivHex));
		assertEquals("Encryption failed", exception.getMessage());
	}

	@Test
	void testDecryptSuccess() throws AES256Exception {
		String originalText = "Hello, World!";
		String key = "1234567890123456";
		String ivHex = "12345678901234567890123456789012";

		when(aes256Configurations.getEncryptionPassword()).thenReturn(key);
		when(aes256Configurations.getEncryptionIvHex()).thenReturn(ivHex);

		String encryptedText = aesUtil.encrypt(originalText);
		String decryptedText = aesUtil.decrypt(encryptedText);

		assertNotNull(decryptedText);
		assertEquals(originalText, decryptedText);
	}

	@Test
	void testDecryptWithNullInput() throws AES256Exception {
		String nullText = null;
		String decryptedText = aesUtil.decrypt(nullText);
		assertNull(decryptedText);
	}

	@Test
	void testDecryptWithInvalidKey() {
		String encryptedText = "SomeEncryptedText";
		String invalidKey = "invalid_key";
		String ivHex = "12345678901234567890123456789012";

		when(aes256Configurations.getEncryptionPassword()).thenReturn(invalidKey);
		when(aes256Configurations.getEncryptionIvHex()).thenReturn(ivHex);

		AES256Exception exception = assertThrows(AES256Exception.class, () -> aesUtil.decrypt(encryptedText));
		assertEquals("Error in decrypting the request", exception.getMessage());
		verify(metricsAgent, times(1)).incrementEventCount(AES256_DECRYPTION_ERROR);
	}

	@Test
	void testStaticDecryptSuccess() {
		String originalText = "Hello, World!";
		String key = "1234567890123456";
		String ivHex = "12345678901234567890123456789012";

		String encryptedText = AESUtil.encrypt(originalText, key, ivHex);
		String decryptedText = AESUtil.decrypt(encryptedText, key, ivHex);

		assertNotNull(decryptedText);
		assertEquals(originalText, decryptedText);
	}

	@Test
	void testStaticDecryptWithInvalidInput() {
		String invalidEncryptedText = "InvalidBase64Text";
		String key = "1234567890123456";
		String ivHex = "12345678901234567890123456789012";

		AES256Exception exception = assertThrows(AES256Exception.class, 
			() -> AESUtil.decrypt(invalidEncryptedText, key, ivHex));
		assertEquals("Decryption failed", exception.getMessage());
	}

	@Test
	void testStaticDecryptWithInvalidKey() {
		String encryptedText = "ValidBase64EncodedText";
		String invalidKey = "invalid_key";
		String ivHex = "12345678901234567890123456789012";

		AES256Exception exception = assertThrows(AES256Exception.class, 
			() -> AESUtil.decrypt(encryptedText, invalidKey, ivHex));
		assertEquals("Decryption failed", exception.getMessage());
	}

	@Test
	void testEndToEndEncryptionDecryption() throws AES256Exception {
		String originalText = "This is a test message with special chars !@#$%^&*()";
		String key = "1234567890123456";
		String ivHex = "12345678901234567890123456789012";

		when(aes256Configurations.getEncryptionPassword()).thenReturn(key);
		when(aes256Configurations.getEncryptionIvHex()).thenReturn(ivHex);

		String encryptedText = aesUtil.encrypt(originalText);
		String decryptedText = aesUtil.decrypt(encryptedText);

		assertNotNull(encryptedText);
		assertNotNull(decryptedText);
		assertEquals(originalText, decryptedText);
	}
}
