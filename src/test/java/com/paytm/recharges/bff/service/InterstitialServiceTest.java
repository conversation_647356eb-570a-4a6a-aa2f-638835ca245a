package com.paytm.recharges.bff.service;

import com.paytm.recharges.bff.config.properties.InterstitialKafkaConfig;
import com.paytm.recharges.bff.constants.Constants;
import com.paytm.recharges.bff.datalayer.cache.ServiceConfigCache;
import com.paytm.recharges.bff.datalayer.dto.request.InterstitialRequest;
import com.paytm.recharges.bff.datalayer.dto.response.ApiResponse;
import com.paytm.recharges.bff.datalayer.model.CacheData;
import com.paytm.recharges.bff.monitor.MetricsAgent;
import com.paytm.recharges.bff.utils.InterstitialUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;
import java.util.ArrayList;
import java.util.List;

class InterstitialServiceTest {

    @Mock
    private InterstitialUtils interstitialUtils;
    @Mock
    private GenericKafkaProducer interstitialKafkaProducer;
    @Mock
    private InterstitialKafkaConfig interstitialKafkaConfig;
    @Mock
    private MetricsAgent metricsAgent;

    @InjectMocks
    private InterstitialService interstitialService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        // Reset ServiceConfigCache singleton for each test if needed
        ServiceConfigCache.getInstance().setFeatureConfigMap(new java.util.HashMap<>());
    }

    @Test
    void testPushInterstitialKafkaRetry_success() {
        InterstitialRequest request = new InterstitialRequest();
        request.setRetryCount(1);
        when(interstitialKafkaConfig.getTopicName30mRetry()).thenReturn("topic30m");
        doNothing().when(interstitialKafkaProducer).sendInterstitialMessage(anyString(), eq("topic30m"));

        ApiResponse response = interstitialService.pushInterstitialKafkaRetry(request);

        assertEquals(Constants.INTERSTITIAL_KAFKA_SUCCESS_STATUS, response.getStatus());
        assertEquals(Constants.INTERSTITIAL_KAFKA_SUCCESS_CODE, response.getStatusCode());
        assertEquals(Constants.INTERSTITIAL_KAFKA_SUCCESS_MSG, response.getMessage());
    }

    @Test
    void testPushInterstitialKafkaRetry_failure() {
        InterstitialRequest request = new InterstitialRequest();
        request.setRetryCount(0);
        when(interstitialKafkaConfig.getTopicName15sRetry()).thenReturn("topic15s");
        doThrow(new RuntimeException("Kafka error")).when(interstitialKafkaProducer).sendInterstitialMessage(anyString(), eq("topic15s"));

        ApiResponse response = interstitialService.pushInterstitialKafkaRetry(request);

        assertEquals(Constants.INTERSTITIAL_IMPRESSION_FAILURE_STATUS, response.getStatus());
        assertEquals(Constants.INTERSTITIAL_IMPRESSION_FAILURE_CODE, response.getStatusCode());
        assertEquals(Constants.INTERSTITIAL_IMPRESSION_FAILURE_MSG, response.getMessage());
        verify(metricsAgent, times(1)).incrementEventCount(Constants.INTERSTITIAL_RETRY_KAFKA_ERROR);
    }

    @Test
    void testRegisterInterstitialImpression_success() {
        InterstitialRequest request = new InterstitialRequest();
        request.setCustomerId(123L);
        java.util.HashMap<String, Object> config = new java.util.HashMap<>();
        config.put(Constants.INTERSTITIAL_RETRY_COUNT_KEY, 2);
        ServiceConfigCache.getInstance().setFeatureConfigMap(config);
        
        List<CacheData> cacheDataList = new ArrayList<>();
        when(interstitialUtils.updateInterstitialCache(request)).thenReturn(cacheDataList);
        when(interstitialKafkaConfig.getTopicNameDwh()).thenReturn("dwh-topic");
        doNothing().when(interstitialKafkaProducer).sendInterstitialMessage(anyString(), eq("dwh-topic"));

        ApiResponse response = interstitialService.registerInterstitialImpression(request);

        assertEquals(Constants.INTERSTITIAL_IMPRESSION_SUCCESS_STATUS, response.getStatus());
        verify(interstitialKafkaProducer, times(1)).sendInterstitialMessage(anyString(), eq("dwh-topic"));
    }

    @Test
    void testRegisterInterstitialImpression_failureAndKafkaSuccess() {
        InterstitialRequest request = new InterstitialRequest();
        request.setCustomerId(123L);
        java.util.HashMap<String, Object> config = new java.util.HashMap<>();
        config.put(Constants.INTERSTITIAL_RETRY_COUNT_KEY, 2);
        ServiceConfigCache.getInstance().setFeatureConfigMap(config);
        
        doThrow(new RuntimeException("Aerospike error")).when(interstitialUtils).updateInterstitialCache(request);
        when(interstitialKafkaConfig.getTopicName15sRetry()).thenReturn("topic15s");
        when(interstitialKafkaConfig.getTopicNameDwh()).thenReturn("dwh-topic");
        doNothing().when(interstitialKafkaProducer).sendInterstitialMessage(anyString(), anyString());

        ApiResponse response = interstitialService.registerInterstitialImpression(request);

        assertEquals(Constants.INTERSTITIAL_KAFKA_SUCCESS_STATUS, response.getStatus());
        verify(metricsAgent, times(2)).incrementEventCount(Constants.INTERSTITIAL_CACHE_UPDATE_ERROR,
            Constants.CUSTOMER_ID + ":123");
        verify(interstitialKafkaProducer, times(1)).sendInterstitialMessage(anyString(), eq("topic15s"));
        verify(interstitialKafkaProducer, times(1)).sendInterstitialMessage(anyString(), eq("dwh-topic"));
    }

    @Test
    void testRegisterInterstitialImpression_partialFailure() {
        InterstitialRequest request = new InterstitialRequest();
        request.setCustomerId(123L);
        java.util.HashMap<String, Object> config = new java.util.HashMap<>();
        config.put(Constants.INTERSTITIAL_RETRY_COUNT_KEY, 2);
        ServiceConfigCache.getInstance().setFeatureConfigMap(config);
        
        List<CacheData> cacheDataList = new ArrayList<>();
        doThrow(new RuntimeException("Aerospike error"))
                .doReturn(cacheDataList)
                .when(interstitialUtils).updateInterstitialCache(request);
        when(interstitialKafkaConfig.getTopicNameDwh()).thenReturn("dwh-topic");
        doNothing().when(interstitialKafkaProducer).sendInterstitialMessage(anyString(), eq("dwh-topic"));

        ApiResponse response = interstitialService.registerInterstitialImpression(request);

        assertEquals(Constants.INTERSTITIAL_IMPRESSION_SUCCESS_STATUS, response.getStatus());
        verify(metricsAgent, times(1)).incrementEventCount(Constants.INTERSTITIAL_CACHE_UPDATE_ERROR, 
            Constants.CUSTOMER_ID + ":123");
        verify(interstitialKafkaProducer, times(1)).sendInterstitialMessage(anyString(), eq("dwh-topic"));
    }

    @Test
    void testRegisterInterstitialImpression_defaultRetryCount() {
        InterstitialRequest request = new InterstitialRequest();
        request.setCustomerId(123L);
        // No config set, should use default retry count
        
        List<CacheData> cacheDataList = new ArrayList<>();
        when(interstitialUtils.updateInterstitialCache(request)).thenReturn(cacheDataList);
        when(interstitialKafkaConfig.getTopicNameDwh()).thenReturn("dwh-topic");
        doNothing().when(interstitialKafkaProducer).sendInterstitialMessage(anyString(), eq("dwh-topic"));

        ApiResponse response = interstitialService.registerInterstitialImpression(request);

        assertEquals(Constants.INTERSTITIAL_IMPRESSION_SUCCESS_STATUS, response.getStatus());
        verify(interstitialKafkaProducer, times(1)).sendInterstitialMessage(anyString(), eq("dwh-topic"));
    }

    @Test
    void testRegisterInterstitialImpression_dwhKafkaFailure() {
        InterstitialRequest request = new InterstitialRequest();
        request.setCustomerId(123L);
        java.util.HashMap<String, Object> config = new java.util.HashMap<>();
        config.put(Constants.INTERSTITIAL_RETRY_COUNT_KEY, 2);
        ServiceConfigCache.getInstance().setFeatureConfigMap(config);
        
        List<CacheData> cacheDataList = new ArrayList<>();
        when(interstitialUtils.updateInterstitialCache(request)).thenReturn(cacheDataList);
        when(interstitialKafkaConfig.getTopicNameDwh()).thenReturn("dwh-topic");
        doThrow(new RuntimeException("DWH Kafka error")).when(interstitialKafkaProducer)
            .sendInterstitialMessage(anyString(), eq("dwh-topic"));

        ApiResponse response = interstitialService.registerInterstitialImpression(request);

        assertEquals(Constants.INTERSTITIAL_IMPRESSION_SUCCESS_STATUS, response.getStatus());
        verify(metricsAgent, times(1)).incrementEventCount("interstitial_dwh_kafka_error");
    }
}

