package com.paytm.recharges.bff.service.impl;

import com.paytm.recharges.bff.constants.Constants;
import com.paytm.recharges.bff.constants.InterstitialConstants;
import com.paytm.recharges.bff.datalayer.cache.CVRProductCache;
import com.paytm.recharges.bff.datalayer.cache.ServiceConfigCache;
import com.paytm.recharges.bff.datalayer.dto.InterstitialDataRecord;
import com.paytm.recharges.bff.datalayer.dto.response.BillObject;
import com.paytm.recharges.bff.datalayer.dto.response.HomeReminderResponse;
import com.paytm.recharges.bff.datalayer.dto.response.SagaRecentResponse;
import com.paytm.recharges.bff.datalayer.model.Product;
import com.paytm.recharges.bff.repository.InterstitialRepository;
import com.paytm.recharges.bff.service.RecentLocalisationManager;
import com.paytm.recharges.bff.utils.DateUtil;
import com.paytm.recharges.bff.utils.InterstitialUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class InterstitialManagerImplTest {

    @InjectMocks
    private InterstitialManagerImpl interstitialManager;

    @Mock
    private InterstitialRepository interstitialRepository;

    @Mock
    private RecentLocalisationManager localisationManager;

    @Mock
    private ServiceConfigCache serviceConfigCache;

    @Mock
    private CVRProductCache cvrProductCache;

    private Product mockProduct;
    private SagaRecentResponse mockSagaResponse;
    private InterstitialDataRecord mockInterstitialRecord;

    @BeforeEach
    void setUp() {
        mockProduct = new Product();
        mockProduct.setProductId(123L);
        mockProduct.setService("MOBILE");
        mockProduct.setOperator("Airtel");
        mockProduct.setPayType("PREPAID");
        mockProduct.setCircle("Delhi");

        mockSagaResponse = new SagaRecentResponse();
        mockSagaResponse.setPid("123");
        mockSagaResponse.setCustomerId(456L);
        mockSagaResponse.setRechargeNumber1("9876543210");
        mockSagaResponse.setIsPrepaid(true);
        mockSagaResponse.setIsFullBill(true);
        mockSagaResponse.setIsDwhSmsParsingManual(false);

        mockInterstitialRecord = new InterstitialDataRecord();
        mockInterstitialRecord.setSeenCount(1);
    }

    @Test
    void testGetEligibleInterstitalExistingImpression_NoEnabledServices() {
        try (MockedStatic<ServiceConfigCache> serviceConfigMock = mockStatic(ServiceConfigCache.class)) {
            serviceConfigMock.when(ServiceConfigCache::getInstance).thenReturn(serviceConfigCache);
            when(serviceConfigCache.getList(InterstitialConstants.INTERSTITIAL_DISPLAY_SERVICE))
                    .thenReturn(Collections.emptyList());

            ArrayList<SagaRecentResponse> responses = new ArrayList<>();
            responses.add(mockSagaResponse);

            Map<String, InterstitialDataRecord> result = 
                interstitialManager.getEligibleInterstitalExistingImpression(responses);

            assertTrue(result.isEmpty());
        }
    }

    @Test
    void testGetEligibleInterstitalExistingImpression_WithEligibleResponses() {
        try (MockedStatic<ServiceConfigCache> serviceConfigMock = mockStatic(ServiceConfigCache.class);
             MockedStatic<CVRProductCache> cvrProductMock = mockStatic(CVRProductCache.class);
             MockedStatic<DateUtil> dateUtilMock = mockStatic(DateUtil.class)) {

            // Setup mocks
            serviceConfigMock.when(ServiceConfigCache::getInstance).thenReturn(serviceConfigCache);
            cvrProductMock.when(CVRProductCache::getInstance).thenReturn(cvrProductCache);

            when(serviceConfigCache.getList(InterstitialConstants.INTERSTITIAL_DISPLAY_SERVICE))
                    .thenReturn(Arrays.asList("mobile"));
            when(cvrProductCache.getProductDetails(123L)).thenReturn(mockProduct);

            // Setup saga response with bill
            BillObject bill = new BillObject();
            bill.setBillDueDate("2024-12-31 23:59:59");
            bill.setExpiryDate("2024-12-25 23:59:59");
            bill.setPlan_bucket("2G");
            mockSagaResponse.setBill(bill);

            // Mock date utilities
            Date futureDate = new Date(System.currentTimeMillis() + 86400000); // Tomorrow
            dateUtilMock.when(() -> DateUtil.stringToDate(anyString(), anyString()))
                    .thenReturn(futureDate);

            // Setup repository response
            Map<String, InterstitialDataRecord> repositoryResponse = new HashMap<>();
            repositoryResponse.put("test_key", mockInterstitialRecord);
            when(interstitialRepository.getInterstitialData(anyList())).thenReturn(repositoryResponse);

            ArrayList<SagaRecentResponse> responses = new ArrayList<>();
            responses.add(mockSagaResponse);

            Map<String, InterstitialDataRecord> result =
                interstitialManager.getEligibleInterstitalExistingImpression(responses);

            assertNotNull(result);
            verify(interstitialRepository).getInterstitialData(anyList());
        }
    }

    @Test
    void testGetEligibleInterstitalExistingImpression_RepositoryException() {
        try (MockedStatic<ServiceConfigCache> serviceConfigMock = mockStatic(ServiceConfigCache.class);
             MockedStatic<CVRProductCache> cvrProductMock = mockStatic(CVRProductCache.class);
             MockedStatic<DateUtil> dateUtilMock = mockStatic(DateUtil.class)) {

            serviceConfigMock.when(ServiceConfigCache::getInstance).thenReturn(serviceConfigCache);
            cvrProductMock.when(CVRProductCache::getInstance).thenReturn(cvrProductCache);

            when(serviceConfigCache.getList(InterstitialConstants.INTERSTITIAL_DISPLAY_SERVICE))
                    .thenReturn(Arrays.asList("mobile"));
            when(cvrProductCache.getProductDetails(123L)).thenReturn(mockProduct);

            // Setup saga response with bill
            BillObject bill = new BillObject();
            bill.setBillDueDate("2024-12-31 23:59:59");
            bill.setExpiryDate("2024-12-25 23:59:59");
            mockSagaResponse.setBill(bill);

            Date futureDate = new Date(System.currentTimeMillis() + 86400000);
            dateUtilMock.when(() -> DateUtil.stringToDate(anyString(), anyString()))
                    .thenReturn(futureDate);

            when(interstitialRepository.getInterstitialData(anyList()))
                    .thenThrow(new RuntimeException("Database error"));

            ArrayList<SagaRecentResponse> responses = new ArrayList<>();
            responses.add(mockSagaResponse);

            Map<String, InterstitialDataRecord> result =
                interstitialManager.getEligibleInterstitalExistingImpression(responses);

            assertNull(result);
        }
    }

    @Test
    void testGetEligibleInterstitalExistingImpression_NullRepositoryResponse() {
        try (MockedStatic<ServiceConfigCache> serviceConfigMock = mockStatic(ServiceConfigCache.class);
             MockedStatic<CVRProductCache> cvrProductMock = mockStatic(CVRProductCache.class);
             MockedStatic<DateUtil> dateUtilMock = mockStatic(DateUtil.class)) {

            serviceConfigMock.when(ServiceConfigCache::getInstance).thenReturn(serviceConfigCache);
            cvrProductMock.when(CVRProductCache::getInstance).thenReturn(cvrProductCache);

            when(serviceConfigCache.getList(InterstitialConstants.INTERSTITIAL_DISPLAY_SERVICE))
                    .thenReturn(Arrays.asList("mobile"));
            when(cvrProductCache.getProductDetails(123L)).thenReturn(mockProduct);

            // Setup saga response with bill
            BillObject bill = new BillObject();
            bill.setBillDueDate("2024-12-31 23:59:59");
            bill.setExpiryDate("2024-12-25 23:59:59");
            mockSagaResponse.setBill(bill);

            Date futureDate = new Date(System.currentTimeMillis() + 86400000);
            dateUtilMock.when(() -> DateUtil.stringToDate(anyString(), anyString()))
                    .thenReturn(futureDate);

            when(interstitialRepository.getInterstitialData(anyList())).thenReturn(null);

            ArrayList<SagaRecentResponse> responses = new ArrayList<>();
            responses.add(mockSagaResponse);

            Map<String, InterstitialDataRecord> result =
                interstitialManager.getEligibleInterstitalExistingImpression(responses);

            assertNull(result);
        }
    }

    @Test
    void testGetEligibleInterstitalExistingImpression_ServiceNotEnabled() {
        try (MockedStatic<ServiceConfigCache> serviceConfigMock = mockStatic(ServiceConfigCache.class);
             MockedStatic<CVRProductCache> cvrProductMock = mockStatic(CVRProductCache.class)) {

            serviceConfigMock.when(ServiceConfigCache::getInstance).thenReturn(serviceConfigCache);
            cvrProductMock.when(CVRProductCache::getInstance).thenReturn(cvrProductCache);

            when(serviceConfigCache.getList(InterstitialConstants.INTERSTITIAL_DISPLAY_SERVICE))
                    .thenReturn(Arrays.asList("dth")); // Different service
            when(cvrProductCache.getProductDetails(123L)).thenReturn(mockProduct);

            ArrayList<SagaRecentResponse> responses = new ArrayList<>();
            responses.add(mockSagaResponse);

            Map<String, InterstitialDataRecord> result =
                interstitialManager.getEligibleInterstitalExistingImpression(responses);

            assertEquals(result, null);
            verify(interstitialRepository, never()).getInterstitialData(anyList());
        }
    }

    @Test
    void testGetEligibleInterstitalExistingImpression_MobilePrepaidExpiryNotTodayOrTomorrow() {
        try (MockedStatic<ServiceConfigCache> serviceConfigMock = mockStatic(ServiceConfigCache.class);
             MockedStatic<CVRProductCache> cvrProductMock = mockStatic(CVRProductCache.class);
             MockedStatic<DateUtil> dateUtilMock = mockStatic(DateUtil.class)) {

            serviceConfigMock.when(ServiceConfigCache::getInstance).thenReturn(serviceConfigCache);
            cvrProductMock.when(CVRProductCache::getInstance).thenReturn(cvrProductCache);

            when(serviceConfigCache.getList(InterstitialConstants.INTERSTITIAL_DISPLAY_SERVICE))
                    .thenReturn(Arrays.asList("mobile"));
            when(cvrProductCache.getProductDetails(123L)).thenReturn(mockProduct);

            // Setup saga response with bill - expiry date is far in future
            BillObject bill = new BillObject();
            bill.setBillDueDate("2024-12-31 23:59:59");
            bill.setExpiryDate("2024-12-20 23:59:59"); // Not today or tomorrow
            mockSagaResponse.setBill(bill);

            Date futureDate = new Date(System.currentTimeMillis() + 86400000);
            dateUtilMock.when(() -> DateUtil.stringToDate(anyString(), anyString()))
                    .thenReturn(futureDate);

            ArrayList<SagaRecentResponse> responses = new ArrayList<>();
            responses.add(mockSagaResponse);

            Map<String, InterstitialDataRecord> result =
                interstitialManager.getEligibleInterstitalExistingImpression(responses);

            assertNotNull(result);
            assertTrue(result.size() > 0);
            verify(interstitialRepository).getInterstitialData(anyList());
        }
    }

    @Test
    void testGetEligibleInterstitalExistingImpression_AutomaticCardNotFailed() {
        try (MockedStatic<ServiceConfigCache> serviceConfigMock = mockStatic(ServiceConfigCache.class);
             MockedStatic<CVRProductCache> cvrProductMock = mockStatic(CVRProductCache.class);
             MockedStatic<DateUtil> dateUtilMock = mockStatic(DateUtil.class)) {

            serviceConfigMock.when(ServiceConfigCache::getInstance).thenReturn(serviceConfigCache);
            cvrProductMock.when(CVRProductCache::getInstance).thenReturn(cvrProductCache);

            when(serviceConfigCache.getList(InterstitialConstants.INTERSTITIAL_DISPLAY_SERVICE))
                    .thenReturn(Arrays.asList("mobile"));
            when(cvrProductCache.getProductDetails(123L)).thenReturn(mockProduct);

            // Setup saga response with automatic state = 1 and not failed
            BillObject bill = new BillObject();
            bill.setBillDueDate("2024-12-31 23:59:59");
            bill.setExpiryDate("2024-12-25 23:59:59");
            mockSagaResponse.setBill(bill);
            mockSagaResponse.setAutomaticState(1);
            mockSagaResponse.setEventState("RECHARGE_AUTOMATIC_SUCCESS"); // Not failure

            Date futureDate = new Date(System.currentTimeMillis() + 86400000);
            dateUtilMock.when(() -> DateUtil.stringToDate(anyString(), anyString()))
                    .thenReturn(futureDate);

            ArrayList<SagaRecentResponse> responses = new ArrayList<>();
            responses.add(mockSagaResponse);

            Map<String, InterstitialDataRecord> result =
                interstitialManager.getEligibleInterstitalExistingImpression(responses);

            assertEquals(result, null);
            verify(interstitialRepository, never()).getInterstitialData(anyList());
        }
    }

    @Test
    void testGetEligibleInterstitalExistingImpression_ManualSmsParsing() {
        try (MockedStatic<ServiceConfigCache> serviceConfigMock = mockStatic(ServiceConfigCache.class);
             MockedStatic<CVRProductCache> cvrProductMock = mockStatic(CVRProductCache.class);
             MockedStatic<DateUtil> dateUtilMock = mockStatic(DateUtil.class)) {

            serviceConfigMock.when(ServiceConfigCache::getInstance).thenReturn(serviceConfigCache);
            cvrProductMock.when(CVRProductCache::getInstance).thenReturn(cvrProductCache);

            when(serviceConfigCache.getList(InterstitialConstants.INTERSTITIAL_DISPLAY_SERVICE))
                    .thenReturn(Arrays.asList("mobile"));
            when(cvrProductCache.getProductDetails(123L)).thenReturn(mockProduct);

            // Setup saga response with manual SMS parsing
            BillObject bill = new BillObject();
            bill.setBillDueDate("2024-12-31 23:59:59");
            bill.setExpiryDate("2024-12-25 23:59:59");
            mockSagaResponse.setBill(bill);
            mockSagaResponse.setIsDwhSmsParsingManual(true);

            Date futureDate = new Date(System.currentTimeMillis() + 86400000);
            dateUtilMock.when(() -> DateUtil.stringToDate(anyString(), anyString()))
                    .thenReturn(futureDate);

            ArrayList<SagaRecentResponse> responses = new ArrayList<>();
            responses.add(mockSagaResponse);

            Map<String, InterstitialDataRecord> result =
                interstitialManager.getEligibleInterstitalExistingImpression(responses);

            assertEquals(result, null);
            verify(interstitialRepository, never()).getInterstitialData(anyList());
        }
    }

    @Test
    void testGetEligibleInterstitalExistingImpression_NotFullBill() {
        try (MockedStatic<ServiceConfigCache> serviceConfigMock = mockStatic(ServiceConfigCache.class);
             MockedStatic<CVRProductCache> cvrProductMock = mockStatic(CVRProductCache.class);
             MockedStatic<DateUtil> dateUtilMock = mockStatic(DateUtil.class)) {

            serviceConfigMock.when(ServiceConfigCache::getInstance).thenReturn(serviceConfigCache);
            cvrProductMock.when(CVRProductCache::getInstance).thenReturn(cvrProductCache);

            when(serviceConfigCache.getList(InterstitialConstants.INTERSTITIAL_DISPLAY_SERVICE))
                    .thenReturn(Arrays.asList("mobile"));
            when(cvrProductCache.getProductDetails(123L)).thenReturn(mockProduct);

            // Setup saga response with not full bill
            BillObject bill = new BillObject();
            bill.setBillDueDate("2024-12-31 23:59:59");
            bill.setExpiryDate("2024-12-25 23:59:59");
            mockSagaResponse.setBill(bill);
            mockSagaResponse.setIsFullBill(false);

            Date futureDate = new Date(System.currentTimeMillis() + 86400000);
            dateUtilMock.when(() -> DateUtil.stringToDate(anyString(), anyString()))
                    .thenReturn(futureDate);

            ArrayList<SagaRecentResponse> responses = new ArrayList<>();
            responses.add(mockSagaResponse);

            Map<String, InterstitialDataRecord> result =
                interstitialManager.getEligibleInterstitalExistingImpression(responses);

            assertEquals(result, null);
            verify(interstitialRepository, never()).getInterstitialData(anyList());
        }
    }

    @Test
    void testGetEligibleInterstitalExistingImpression_BillDueDateInPast() {
        try (MockedStatic<ServiceConfigCache> serviceConfigMock = mockStatic(ServiceConfigCache.class);
             MockedStatic<CVRProductCache> cvrProductMock = mockStatic(CVRProductCache.class);
             MockedStatic<DateUtil> dateUtilMock = mockStatic(DateUtil.class)) {

            serviceConfigMock.when(ServiceConfigCache::getInstance).thenReturn(serviceConfigCache);
            cvrProductMock.when(CVRProductCache::getInstance).thenReturn(cvrProductCache);

            when(serviceConfigCache.getList(InterstitialConstants.INTERSTITIAL_DISPLAY_SERVICE))
                    .thenReturn(Arrays.asList("mobile"));
            when(cvrProductCache.getProductDetails(123L)).thenReturn(mockProduct);

            // Setup saga response with past due date
            BillObject bill = new BillObject();
            bill.setBillDueDate("2024-01-01 23:59:59"); // Past date
            bill.setExpiryDate("2024-12-25 23:59:59");
            mockSagaResponse.setBill(bill);

            Date pastDate = new Date(System.currentTimeMillis() - 86400000); // Yesterday
            dateUtilMock.when(() -> DateUtil.stringToDate(anyString(), anyString()))
                    .thenReturn(pastDate);

            ArrayList<SagaRecentResponse> responses = new ArrayList<>();
            responses.add(mockSagaResponse);

            Map<String, InterstitialDataRecord> result =
                interstitialManager.getEligibleInterstitalExistingImpression(responses);

            assertEquals(result, null);
            verify(interstitialRepository, never()).getInterstitialData(anyList());
        }
    }

    @Test
    void testGetEligibleInterstitalExistingImpression_MultipleEligibleResponses() {
        try (MockedStatic<ServiceConfigCache> serviceConfigMock = mockStatic(ServiceConfigCache.class);
             MockedStatic<CVRProductCache> cvrProductMock = mockStatic(CVRProductCache.class);
             MockedStatic<DateUtil> dateUtilMock = mockStatic(DateUtil.class)) {

            serviceConfigMock.when(ServiceConfigCache::getInstance).thenReturn(serviceConfigCache);
            cvrProductMock.when(CVRProductCache::getInstance).thenReturn(cvrProductCache);

            when(serviceConfigCache.getList(InterstitialConstants.INTERSTITIAL_DISPLAY_SERVICE))
                    .thenReturn(Arrays.asList("mobile", "dth"));
            when(cvrProductCache.getProductDetails(123L)).thenReturn(mockProduct);
            when(cvrProductCache.getProductDetails(456L)).thenReturn(mockProduct);

            // Setup first saga response
            BillObject bill1 = new BillObject();
            bill1.setBillDueDate("2024-12-31 23:59:59");
            bill1.setExpiryDate("2024-12-25 23:59:59");
            bill1.setPlan_bucket("2G");
            mockSagaResponse.setBill(bill1);

            // Setup second saga response
            SagaRecentResponse mockSagaResponse2 = new SagaRecentResponse();
            mockSagaResponse2.setPid("456");
            mockSagaResponse2.setCustomerId(789L);
            mockSagaResponse2.setRechargeNumber1("9876543211");
            mockSagaResponse2.setIsPrepaid(true);
            mockSagaResponse2.setIsFullBill(true);
            mockSagaResponse2.setIsDwhSmsParsingManual(false);

            BillObject bill2 = new BillObject();
            bill2.setBillDueDate("2024-12-31 23:59:59");
            bill2.setExpiryDate("2024-12-25 23:59:59");
            bill2.setPlan_bucket("3G");
            mockSagaResponse2.setBill(bill2);

            // Mock date utilities
            Date futureDate = new Date(System.currentTimeMillis() + 86400000);
            dateUtilMock.when(() -> DateUtil.stringToDate(anyString(), anyString()))
                    .thenReturn(futureDate);

            // Setup repository response
            Map<String, InterstitialDataRecord> repositoryResponse = new HashMap<>();
            repositoryResponse.put("456_9876543210_airtel_mobile_2024-12-31_2g", mockInterstitialRecord);
            repositoryResponse.put("789_9876543211_airtel_mobile_2024-12-31_3g", mockInterstitialRecord);
            when(interstitialRepository.getInterstitialData(anyList())).thenReturn(repositoryResponse);

            ArrayList<SagaRecentResponse> responses = new ArrayList<>();
            responses.add(mockSagaResponse);
            responses.add(mockSagaResponse2);

            Map<String, InterstitialDataRecord> result =
                interstitialManager.getEligibleInterstitalExistingImpression(responses);

            assertNotNull(result);
            assertTrue(result.size() > 0);
            verify(interstitialRepository).getInterstitialData(anyList());
        }
    }

    @Test
    void testGetEligibleInterstitalExistingImpression_EmptyResponsesList() {
        try (MockedStatic<ServiceConfigCache> serviceConfigMock = mockStatic(ServiceConfigCache.class)) {
            serviceConfigMock.when(ServiceConfigCache::getInstance).thenReturn(serviceConfigCache);
            when(serviceConfigCache.getList(InterstitialConstants.INTERSTITIAL_DISPLAY_SERVICE))
                    .thenReturn(Arrays.asList("mobile"));

            ArrayList<SagaRecentResponse> responses = new ArrayList<>();

            Map<String, InterstitialDataRecord> result =
                interstitialManager.getEligibleInterstitalExistingImpression(responses);

            assertEquals(result, null);
            verify(interstitialRepository, never()).getInterstitialData(anyList());
        }
    }

    @Test
    void testCheckAndAddInterstitialDetails_NotEligible() {
        try (MockedStatic<CVRProductCache> cvrProductMock = mockStatic(CVRProductCache.class);
             MockedStatic<ServiceConfigCache> serviceConfigMock = mockStatic(ServiceConfigCache.class)) {

            cvrProductMock.when(CVRProductCache::getInstance).thenReturn(cvrProductCache);
            serviceConfigMock.when(ServiceConfigCache::getInstance).thenReturn(serviceConfigCache);

            when(cvrProductCache.getProductDetails(123L)).thenReturn(mockProduct);
            when(serviceConfigCache.getInteger(InterstitialConstants.INTERSTITIAL_DISPLAY_PER_BILL))
                    .thenReturn(3);

            HashMap<String, Object> frequentOrder = new HashMap<>();
            frequentOrder.put("customerId", "456");
            frequentOrder.put(Constants.RECENT_PRODUCT_ID, "123");

            HomeReminderResponse reminderResponse = new HomeReminderResponse();
            Map<String, InterstitialDataRecord> impressionMap = new HashMap<>();

            // Bill already shown maximum times
            InterstitialDataRecord billRecord = new InterstitialDataRecord();
            billRecord.setSeenCount(3);
            String billKey = "456__airtel_mobile_null_null";
            impressionMap.put(billKey, billRecord);

            int result = interstitialManager.checkAndAddInterstitialDetails(
                    frequentOrder, reminderResponse, impressionMap);

            assertEquals(3, result);
            assertNull(reminderResponse.getCustomProperties());
        }
    }

//    @Test
//    void testCheckAndAddInterstitialDetails_Exception() {
//        try (MockedStatic<CVRProductCache> cvrProductMock = mockStatic(CVRProductCache.class)) {
//            cvrProductMock.when(CVRProductCache::getInstance).thenReturn(cvrProductCache);
//            when(cvrProductCache.getProductDetails(123L)).thenThrow(new RuntimeException("Cache error"));
//
//            HashMap<String, Object> frequentOrder = new HashMap<>();
//            frequentOrder.put("customerId", "456");
//            frequentOrder.put(Constants.RECENT_PRODUCT_ID, "123");
//
//            HomeReminderResponse reminderResponse = new HomeReminderResponse();
//            Map<String, InterstitialDataRecord> impressionMap = new HashMap<>();
//
//            int result = interstitialManager.checkAndAddInterstitialDetails(
//                    frequentOrder, reminderResponse, impressionMap);
//
//            assertEquals(0, result);
//        }
//    }

//    @Test
//    void testGenerateBillKey_SagaRecentResponse() {
//        try (MockedStatic<DateUtil> dateUtilMock = mockStatic(DateUtil.class)) {
//            dateUtilMock.when(() -> DateUtil.stringDateFormat(anyString(), anyString(), anyString()))
//                    .thenReturn("2024-12-31");
//
//            SagaRecentResponse sagaRecentResponse = new SagaRecentResponse();
//            BillObject bill = sagaRecentResponse.getBill();
//            bill.setBillDueDate("2024-12-31 23:59:59");
//            bill.setPlan_bucket("2G_PLAN");
//            mockSagaResponse.setBill(bill);
//
//            String result = interstitialManager.generateBillKey(mockSagaResponse, mockProduct);
//
//            assertTrue(result.contains("456"));
//            assertTrue(result.contains("9876543210"));
//            assertTrue(result.contains("airtel"));
//            assertTrue(result.contains("mobile"));
//            assertTrue(result.contains("2024-12-31"));
//            assertTrue(result.contains("2g_plan"));
//        }
//    }

    @Test
    void testGenerateBillKey_MapInput() {
        try (MockedStatic<DateUtil> dateUtilMock = mockStatic(DateUtil.class)) {
            dateUtilMock.when(() -> DateUtil.stringDateFormat(anyString(), anyString(), anyString()))
                    .thenReturn("2024-12-31");

            Map<String, Object> frequentOrder = new HashMap<>();
            frequentOrder.put("customerId", "789");

            // Setup configuration_local
            HashMap<String, Object> configLocal = new HashMap<>();
            configLocal.put(Constants.RECHARGE_NUMBER, "1234567890");
            frequentOrder.put(Constants.CONFIGURATION_LOCAL.toLowerCase(Locale.ROOT), configLocal);

            // Setup bills
            HashMap<String, Object> bill = new HashMap<>();
            bill.put("due_date", "2024-12-31");
            bill.put("plan_bucket", "3G");
            ArrayList<HashMap<String, Object>> bills = new ArrayList<>();
            bills.add(bill);
            frequentOrder.put("bills", bills);

            String result = interstitialManager.generateBillKey(frequentOrder, mockProduct);

            assertTrue(result.contains("789"));
            assertTrue(result.contains("1234567890"));
            assertTrue(result.contains("airtel"));
            assertTrue(result.contains("mobile"));
            assertTrue(result.contains("2024-12-31"));
            assertTrue(result.contains("3g"));
        }
    }

    @Test
    void testGenerateBillKey_UnsupportedInputType() {
        String unsupportedInput = "invalid input";

        assertThrows(IllegalArgumentException.class, () -> {
            interstitialManager.generateBillKey(unsupportedInput, mockProduct);
        });
    }

    @Test
    void testCheckAndGetEligibleInterstitial_FeatureDisabled() {
        try (MockedStatic<ServiceConfigCache> serviceConfigMock = mockStatic(ServiceConfigCache.class)) {
            serviceConfigMock.when(ServiceConfigCache::getInstance).thenReturn(serviceConfigCache);
            when(serviceConfigCache.getBoolean(InterstitialConstants.INTERSTITIAL_DISPLAY_FLAG))
                    .thenReturn(false);

            int result = interstitialManager.checkAndGetEligibleInterstitial(123L);

            assertEquals(0, result);
        }
    }

    @Test
    void testCheckAndGetEligibleInterstitial_NullCustomerId() {
        try (MockedStatic<ServiceConfigCache> serviceConfigMock = mockStatic(ServiceConfigCache.class)) {
            serviceConfigMock.when(ServiceConfigCache::getInstance).thenReturn(serviceConfigCache);
            when(serviceConfigCache.getBoolean(InterstitialConstants.INTERSTITIAL_DISPLAY_FLAG))
                    .thenReturn(true);

            int result = interstitialManager.checkAndGetEligibleInterstitial(null);

            assertEquals(0, result);
        }
    }

    @Test
    void testCheckAndGetEligibleInterstitial_InvalidLimits() {
        try (MockedStatic<ServiceConfigCache> serviceConfigMock = mockStatic(ServiceConfigCache.class)) {
            serviceConfigMock.when(ServiceConfigCache::getInstance).thenReturn(serviceConfigCache);
            when(serviceConfigCache.getBoolean(InterstitialConstants.INTERSTITIAL_DISPLAY_FLAG))
                    .thenReturn(true);
            when(serviceConfigCache.getInteger(InterstitialConstants.INTERSTITIAL_DISPLAY_PER_MONTH))
                    .thenReturn(null);
            when(serviceConfigCache.getInteger(InterstitialConstants.INTERSTITIAL_DISPLAY_PER_DAY))
                    .thenReturn(5);

            int result = interstitialManager.checkAndGetEligibleInterstitial(123L);

            assertEquals(0, result);
        }
    }

    @Test
    void testCheckAndGetEligibleInterstitial_WithinLimits() {
        try (MockedStatic<ServiceConfigCache> serviceConfigMock = mockStatic(ServiceConfigCache.class);
             MockedStatic<InterstitialUtils> utilsMock = mockStatic(InterstitialUtils.class)) {

            serviceConfigMock.when(ServiceConfigCache::getInstance).thenReturn(serviceConfigCache);
            when(serviceConfigCache.getBoolean(InterstitialConstants.INTERSTITIAL_DISPLAY_FLAG))
                    .thenReturn(true);
            when(serviceConfigCache.getInteger(InterstitialConstants.INTERSTITIAL_DISPLAY_PER_MONTH))
                    .thenReturn(10);
            when(serviceConfigCache.getInteger(InterstitialConstants.INTERSTITIAL_DISPLAY_PER_DAY))
                    .thenReturn(3);

            utilsMock.when(() -> InterstitialUtils.generateMonthKey(123L))
                    .thenReturn("123_03_2024");
            utilsMock.when(() -> InterstitialUtils.generateDayKey(123L))
                    .thenReturn("123_15_03_2024");

            Map<String, InterstitialDataRecord> repositoryResponse = new HashMap<>();
            InterstitialDataRecord monthRecord = new InterstitialDataRecord();
            monthRecord.setSeenCount(2);
            InterstitialDataRecord dayRecord = new InterstitialDataRecord();
            dayRecord.setSeenCount(1);

            repositoryResponse.put("123_03_2024", monthRecord);
            repositoryResponse.put("123_15_03_2024", dayRecord);

            when(interstitialRepository.getInterstitialData(anyList())).thenReturn(repositoryResponse);

            int result = interstitialManager.checkAndGetEligibleInterstitial(123L);

            assertEquals(2, result); // dailyLimit - dailyCount = 3 - 1 = 2
        }
    }

    @Test
    void testCheckAndGetEligibleInterstitial_ExceededLimits() {
        try (MockedStatic<ServiceConfigCache> serviceConfigMock = mockStatic(ServiceConfigCache.class);
             MockedStatic<InterstitialUtils> utilsMock = mockStatic(InterstitialUtils.class)) {

            serviceConfigMock.when(ServiceConfigCache::getInstance).thenReturn(serviceConfigCache);
            when(serviceConfigCache.getBoolean(InterstitialConstants.INTERSTITIAL_DISPLAY_FLAG))
                    .thenReturn(true);
            when(serviceConfigCache.getInteger(InterstitialConstants.INTERSTITIAL_DISPLAY_PER_MONTH))
                    .thenReturn(10);
            when(serviceConfigCache.getInteger(InterstitialConstants.INTERSTITIAL_DISPLAY_PER_DAY))
                    .thenReturn(3);

            utilsMock.when(() -> InterstitialUtils.generateMonthKey(123L))
                    .thenReturn("123_03_2024");
            utilsMock.when(() -> InterstitialUtils.generateDayKey(123L))
                    .thenReturn("123_15_03_2024");

            Map<String, InterstitialDataRecord> repositoryResponse = new HashMap<>();
            InterstitialDataRecord monthRecord = new InterstitialDataRecord();
            monthRecord.setSeenCount(2);
            InterstitialDataRecord dayRecord = new InterstitialDataRecord();
            dayRecord.setSeenCount(3); // Reached daily limit

            repositoryResponse.put("123_03_2024", monthRecord);
            repositoryResponse.put("123_15_03_2024", dayRecord);

            when(interstitialRepository.getInterstitialData(anyList())).thenReturn(repositoryResponse);

            int result = interstitialManager.checkAndGetEligibleInterstitial(123L);

            assertEquals(0, result);
        }
    }

    @Test
    void testCheckAndGetEligibleInterstitial_RepositoryException() {
        try (MockedStatic<ServiceConfigCache> serviceConfigMock = mockStatic(ServiceConfigCache.class);
             MockedStatic<InterstitialUtils> utilsMock = mockStatic(InterstitialUtils.class)) {

            serviceConfigMock.when(ServiceConfigCache::getInstance).thenReturn(serviceConfigCache);
            when(serviceConfigCache.getBoolean(InterstitialConstants.INTERSTITIAL_DISPLAY_FLAG))
                    .thenReturn(true);
            when(serviceConfigCache.getInteger(InterstitialConstants.INTERSTITIAL_DISPLAY_PER_MONTH))
                    .thenReturn(10);
            when(serviceConfigCache.getInteger(InterstitialConstants.INTERSTITIAL_DISPLAY_PER_DAY))
                    .thenReturn(3);

            utilsMock.when(() -> InterstitialUtils.generateMonthKey(123L))
                    .thenReturn("123_03_2024");
            utilsMock.when(() -> InterstitialUtils.generateDayKey(123L))
                    .thenReturn("123_15_03_2024");

            when(interstitialRepository.getInterstitialData(anyList()))
                    .thenThrow(new RuntimeException("Database error"));

            int result = interstitialManager.checkAndGetEligibleInterstitial(123L);

            assertEquals(3, result);
        }
    }

    @Test
    void testCheckAndGetEligibleInterstitial_NullRepositoryResponse() {
        try (MockedStatic<ServiceConfigCache> serviceConfigMock = mockStatic(ServiceConfigCache.class);
             MockedStatic<InterstitialUtils> utilsMock = mockStatic(InterstitialUtils.class)) {

            serviceConfigMock.when(ServiceConfigCache::getInstance).thenReturn(serviceConfigCache);
            when(serviceConfigCache.getBoolean(InterstitialConstants.INTERSTITIAL_DISPLAY_FLAG))
                    .thenReturn(true);
            when(serviceConfigCache.getInteger(InterstitialConstants.INTERSTITIAL_DISPLAY_PER_MONTH))
                    .thenReturn(10);
            when(serviceConfigCache.getInteger(InterstitialConstants.INTERSTITIAL_DISPLAY_PER_DAY))
                    .thenReturn(3);

            utilsMock.when(() -> InterstitialUtils.generateMonthKey(123L))
                    .thenReturn("123_03_2024");
            utilsMock.when(() -> InterstitialUtils.generateDayKey(123L))
                    .thenReturn("123_15_03_2024");

            when(interstitialRepository.getInterstitialData(anyList())).thenReturn(null);

            int result = interstitialManager.checkAndGetEligibleInterstitial(123L);

            assertEquals(3, result); // Should default to 0 counts, so dailyLimit = 3
        }
    }

    @Test
    void testGetHeader_WithNullAmount() {
        HomeReminderResponse reminderResponse = new HomeReminderResponse();
        reminderResponse.setAmount(null);

        HashMap<String, Object> frequentOrder = new HashMap<>();
        frequentOrder.put("customerId", "456");
        frequentOrder.put(Constants.RECENT_PRODUCT_ID, "123");

        Map<String, InterstitialDataRecord> impressionMap = new HashMap<>();

        try (MockedStatic<CVRProductCache> cvrProductMock = mockStatic(CVRProductCache.class);
             MockedStatic<ServiceConfigCache> serviceConfigMock = mockStatic(ServiceConfigCache.class)) {

            cvrProductMock.when(CVRProductCache::getInstance).thenReturn(cvrProductCache);
            serviceConfigMock.when(ServiceConfigCache::getInstance).thenReturn(serviceConfigCache);

            when(cvrProductCache.getProductDetails(123L)).thenReturn(mockProduct);
            when(serviceConfigCache.getInteger(InterstitialConstants.INTERSTITIAL_DISPLAY_PER_BILL))
                    .thenReturn(3);

            int result = interstitialManager.checkAndAddInterstitialDetails(
                    frequentOrder, reminderResponse, impressionMap);

            assertEquals(0, result);
            assertNull(reminderResponse.getCustomProperties());
        }
    }

    @Test
    void testGetDisplayMessage_WithNullDueDate() {
        HomeReminderResponse reminderResponse = new HomeReminderResponse();
        reminderResponse.setAmount(100.0);
        reminderResponse.setDueDate(null);

        HashMap<String, Object> frequentOrder = new HashMap<>();
        frequentOrder.put("customerId", "456");
        frequentOrder.put(Constants.RECENT_PRODUCT_ID, "123");

        Map<String, InterstitialDataRecord> impressionMap = new HashMap<>();

        try (MockedStatic<CVRProductCache> cvrProductMock = mockStatic(CVRProductCache.class);
             MockedStatic<ServiceConfigCache> serviceConfigMock = mockStatic(ServiceConfigCache.class)) {

            cvrProductMock.when(CVRProductCache::getInstance).thenReturn(cvrProductCache);
            serviceConfigMock.when(ServiceConfigCache::getInstance).thenReturn(serviceConfigCache);

            when(cvrProductCache.getProductDetails(123L)).thenReturn(mockProduct);
            when(serviceConfigCache.getInteger(InterstitialConstants.INTERSTITIAL_DISPLAY_PER_BILL))
                    .thenReturn(3);

            int result = interstitialManager.checkAndAddInterstitialDetails(
                    frequentOrder, reminderResponse, impressionMap);

            assertEquals(0, result);
            assertNull(reminderResponse.getCustomProperties());
        }
    }
}
