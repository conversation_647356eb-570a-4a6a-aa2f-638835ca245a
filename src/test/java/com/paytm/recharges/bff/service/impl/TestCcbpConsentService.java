package com.paytm.recharges.bff.service.impl;

import com.paytm.recharges.bff.client.CcbpConsentClient;
import com.paytm.recharges.bff.constants.Constants;
import com.paytm.recharges.bff.datalayer.dto.request.BulkConsentRequestItem;
import com.paytm.recharges.bff.datalayer.dto.request.ConsentRequest;
import com.paytm.recharges.bff.datalayer.dto.response.ConsentResponse;
import com.paytm.recharges.bff.datalayer.dto.response.RechargeItem;
import com.paytm.recharges.bff.datalayer.model.ConsentObject;
import com.paytm.recharges.bff.datalayer.model.ConsentUniqueKey;
import com.paytm.recharges.bff.utils.SavedCardUtil;
import com.paytm.recharges.bff.monitor.MetricsAgent;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.junit.jupiter.api.DisplayName;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class TestCcbpConsentService {

    private SavedCardUtil savedCardUtil;
    private ExecutorService ccbpConsentExecutor;
    private MetricsAgent metricsAgent;
    private CcbpConsentClient ccbpConsentClient;

    private CcbpConsentService ccbpConsentService;

    private RechargeItem item;
    private Long customerId;
    private ConsentObject consentObject;
    private ConsentRequest consentRequest;

    @BeforeEach
    public void setUp() {

        MockitoAnnotations.openMocks(this);
        savedCardUtil = mock(SavedCardUtil.class);
        metricsAgent = mock(MetricsAgent.class);
        ccbpConsentClient = mock(CcbpConsentClient.class);

        ccbpConsentExecutor = new ThreadPoolExecutor(1, 1, 10L, TimeUnit.SECONDS, new LinkedBlockingQueue<Runnable>(1));

        ccbpConsentService = new CcbpConsentService(savedCardUtil, ccbpConsentExecutor, ccbpConsentClient, metricsAgent);
        ccbpConsentService.setRequestTimeout(10000);
        ccbpConsentService.setBulkConsentRequestTimeout(10000);

        item = new RechargeItem();
        Map<String, Object> configuration = new HashMap<>();
        configuration.put(Constants.RECHARGE_NUMBER_5, "1234");
        configuration.put(Constants.RECHARGE_NUMBER_6, "**********");
        item.setConfiguration(configuration);
        item.setProductId(123L);
        customerId = 123456L;
        consentObject = new ConsentObject(
                new ConsentUniqueKey("**********_1234", customerId, "idfc", "financial services"),
                1,
                (byte) 0,
                null,
                null
        );
        consentRequest = new ConsentRequest(customerId, "financial services", "**********", "1234", "idfc");
    }

    @Test
    void testGetConsentRequest() {
        when(savedCardUtil.getBankName(anyLong())).thenReturn("IDFC");

        ConsentRequest result = ccbpConsentService.getConsentRequest(item, customerId);
        assertNotNull(result);
        assertEquals("idfc", result.getOperator());
        assertEquals("financial services", result.getService());
    }

    @Test
    void testHasConsent_Success() {
        ConsentResponse consentResponse = mock(ConsentResponse.class);
        when(ccbpConsentClient.getConsent(any())).thenReturn(consentResponse);
        ConsentResponse response = ccbpConsentService.hasConsent(consentRequest);
        assertNotNull(response);
        verify(metricsAgent).incrementEventCount(Constants.HAS_CONSENT_API_HIT);
        verify(metricsAgent).recordExecutionTimeOfEvent(eq(Constants.HAS_CONSENT_API_LATENCY), anyLong());
    }

    @Test
    void testCreateOrUpdateConsent_Success() {
        ConsentResponse consentResponse = mock(ConsentResponse.class);
        when(ccbpConsentClient.createConsent(any())).thenReturn(consentResponse);
        ConsentResponse response = ccbpConsentService.createOrUpdateConsent(consentRequest);
        assertNotNull(response);
        verify(metricsAgent).incrementEventCount(Constants.CREATE_CONSENT_API_HIT);
        verify(metricsAgent).recordExecutionTimeOfEvent(eq(Constants.CREATE_CONSENT_API_LATENCY), anyLong());
    }

    @Test
    void testHasConsent_Timeout() {
        ccbpConsentService.setRequestTimeout(0);
        ConsentResponse response = ccbpConsentService.hasConsent(consentRequest);
        assertNull(response);
        verify(metricsAgent).incrementEventCount(Constants.HAS_CONSENT_API_HIT);
        verify(metricsAgent).incrementEventCount(Constants.HAS_CONSENT_API_TIMEOUT_ERROR);
    }

    @Test
    void testCreateOrUpdateConsent_Timeout() {
        ccbpConsentService.setRequestTimeout(0);
        ConsentResponse response = ccbpConsentService.createOrUpdateConsent(consentRequest);
        assertNull(response);
        verify(metricsAgent).incrementEventCount(Constants.CREATE_CONSENT_API_TIMEOUT_ERROR);
    }

    @Test
    void testCreateOrUpdateBulkConsent_Success() {
        when(ccbpConsentClient.createBulkConsent(any(), anyString())).thenReturn(new ResponseEntity<>(new Object(), HttpStatus.OK));
        BulkConsentRequestItem item1 = new BulkConsentRequestItem("**********", "1234", "idfc");
        ResponseEntity<Object> response = ccbpConsentService.createOrUpdateBulkConsent(List.of(item1), "123456");
        assertNotNull(response);
        verify(metricsAgent).incrementEventCount(Constants.BULK_CREATE_CONSENT_API_HIT);
        verify(metricsAgent).recordExecutionTimeOfEvent(eq(Constants.CREATE_BULK_CONSENT_API_LATENCY), anyLong());
    }

    @Test
    void testCreateOrUpdateBulkConsent_Timeout() {
        ccbpConsentService.setBulkConsentRequestTimeout(0);
        when(ccbpConsentClient.createBulkConsent(any(), anyString())).thenReturn(new ResponseEntity<>(new Object(), HttpStatus.OK));
        BulkConsentRequestItem item1 = new BulkConsentRequestItem("**********", "1234", "idfc");
        ResponseEntity<Object> response = ccbpConsentService.createOrUpdateBulkConsent(List.of(item1), "123456");
        assertNotNull(response);
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        verify(metricsAgent).incrementEventCount(Constants.BULK_CREATE_CONSENT_API_ERROR);
    }

    @Test
    void testDeleteConsent() {
        boolean result = ccbpConsentService.deleteConsent(consentObject);
        assertFalse(result);
    }

    @Test
    void testGetConsentRequest_WithEmptyBankName() {
        when(savedCardUtil.getBankName(anyLong())).thenReturn("");
        ConsentRequest result = ccbpConsentService.getConsentRequest(item, customerId);
        assertNotNull(result);
        assertEquals("", result.getOperator());
        assertEquals("financial services", result.getService());
    }
    @Test
    void testHasConsent_ClientException() {
        when(ccbpConsentClient.getConsent(any())).thenThrow(new RuntimeException("Client error"));
        ConsentResponse response = ccbpConsentService.hasConsent(consentRequest);
        assertNull(response);
        verify(metricsAgent).incrementEventCount(Constants.HAS_CONSENT_API_HIT);
        verify(metricsAgent).incrementEventCount(Constants.HAS_CONSENT_API_ERROR);
    }
    @Test
    void testCreateOrUpdateConsent_ClientException() {
        when(ccbpConsentClient.createConsent(any())).thenThrow(new RuntimeException("Client error"));
        ConsentResponse response = ccbpConsentService.createOrUpdateConsent(consentRequest);
        assertNull(response);
        verify(metricsAgent).incrementEventCount(Constants.CREATE_CONSENT_API_HIT);
        verify(metricsAgent).incrementEventCount(Constants.CREATE_CONSENT_API_ERROR);
    }
    @Test
    void testCreateOrUpdateBulkConsent_ClientException() {
        when(ccbpConsentClient.createBulkConsent(any(), anyString())).thenThrow(new RuntimeException("Client error"));
        BulkConsentRequestItem item1 = new BulkConsentRequestItem("**********", "1234", "idfc");
        ResponseEntity<Object> response = ccbpConsentService.createOrUpdateBulkConsent(List.of(item1), "123456");
        assertNotNull(response);
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        verify(metricsAgent).incrementEventCount(Constants.BULK_CREATE_CONSENT_API_HIT);
        verify(metricsAgent).incrementEventCount(Constants.BULK_CREATE_CONSENT_API_ERROR);
    }
    @Test
    @DisplayName("Test hasConsent with null response from client")
    void testHasConsent_NullResponse() {
        when(ccbpConsentClient.getConsent(any())).thenReturn(null);
        ConsentResponse response = ccbpConsentService.hasConsent(consentRequest);
        assertNull(response);
        verify(metricsAgent).incrementEventCount(Constants.HAS_CONSENT_API_HIT);
    }
    @Test
    @DisplayName("Test createOrUpdateConsent with null response from client")
    void testCreateOrUpdateConsent_NullResponse() {
        when(ccbpConsentClient.createConsent(any())).thenReturn(null);
        ConsentResponse response = ccbpConsentService.createOrUpdateConsent(consentRequest);
        assertNull(response);
        verify(metricsAgent).incrementEventCount(Constants.CREATE_CONSENT_API_HIT);
    }
    @Test
    @DisplayName("Test setting request timeout")
    void testSetRequestTimeout() {
        int newTimeout = 5000;
        ccbpConsentService.setRequestTimeout(newTimeout);
        // Force a timeout to verify the new timeout is used
        ccbpConsentService.setRequestTimeout(0);
        ConsentResponse response = ccbpConsentService.hasConsent(consentRequest);
        assertNull(response);
        verify(metricsAgent).incrementEventCount(Constants.HAS_CONSENT_API_TIMEOUT_ERROR);
        // Reset timeout for other tests
        ccbpConsentService.setRequestTimeout(10000);
    }
    @Test
    @DisplayName("Test setting bulk consent request timeout")
    void testSetBulkConsentRequestTimeout() {
        int newTimeout = 5000;
        ccbpConsentService.setBulkConsentRequestTimeout(newTimeout);
        // Force a timeout to verify the new timeout is used
        ccbpConsentService.setBulkConsentRequestTimeout(0);
        BulkConsentRequestItem item1 = new BulkConsentRequestItem("**********", "1234", "idfc");
        ResponseEntity<Object> response = ccbpConsentService.createOrUpdateBulkConsent(List.of(item1), "123456");
        assertNotNull(response);
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        verify(metricsAgent).incrementEventCount(Constants.BULK_CREATE_CONSENT_API_ERROR);
        // Reset timeout for other tests
        ccbpConsentService.setBulkConsentRequestTimeout(10000);
    }
}