package com.paytm.recharges.bff.test;

import static org.junit.Assert.assertEquals;

import java.lang.reflect.Field;
import java.util.HashMap;

import com.paytm.recharges.bff.service.RecentLocalisationManager;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import com.paytm.recharges.bff.service.LocalisationManager;

@RunWith(MockitoJUnitRunner.class)
public class TestRecentLocalisationManager extends TestBaseRecharge {


    @InjectMocks
    private RecentLocalisationManager localisationManager;

    @Mock
    private com.paytm.recharges.bff.monitor.MetricsAgent metricsAgent;

    @Before
    public void setup() throws Exception {
        MockitoAnnotations.openMocks(this);
        // Inject the mock into the private field
        Field field = localisationManager.getClass().getDeclaredField("metricsAgent");
        field.setAccessible(true);
        field.set(localisationManager, metricsAgent);
    }

    @Test
    public void TestGetMessage()
    {
        String[] keyComponents = new String[1] ;
        Boolean findSimilarMatch= true;
        HashMap<String,String> payload=new HashMap<>();
        String localMessage=localisationManager.getMessage(keyComponents,findSimilarMatch,payload);
        assertEquals(localMessage.length(),0);
    }

}
