package com.paytm.recharges.bff.test;

import com.paytm.recharges.bff.client.GenericRestClient;
import com.paytm.recharges.bff.client.PolicyRuleEngineClient;
import com.paytm.recharges.bff.config.properties.PolicyRuleEngineProperties;
import com.paytm.recharges.bff.datalayer.cache.CVRProductCache;
import com.paytm.recharges.bff.datalayer.dto.request.PolicyRuleEngineFeeRequest;
import com.paytm.recharges.bff.datalayer.dto.request.RechargeVerifyRequest;
import com.paytm.recharges.bff.datalayer.dto.response.ApplyFeePayload;
import com.paytm.recharges.bff.datalayer.model.Product;
import com.paytm.recharges.bff.service.impl.CheckoutCommonService;
import com.paytm.recharges.bff.service.impl.PolicyRuleEngineServiceImpl;
import com.paytm.recharges.bff.service.impl.RechargeHelperService;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.paytm.recharges.bff.constants.Constants.PLATFORMFEE_RULE_ENGINE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class TestPolicyRuleEngineService extends TestCheckoutCommon{
    @Mock
    private RechargeHelperService rechargeHelperService;

    @Mock
    private GenericRestClient genericRestClient;

    @Mock
    private CheckoutCommonService checkoutCommonService;

    @Mock
    private PolicyRuleEngineProperties policyRuleEngineProperties;

    @Mock
    private PolicyRuleEngineClient policyRuleEngineClient;

    @InjectMocks
    private PolicyRuleEngineServiceImpl policyRuleEngineService;



    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }

    private PolicyRuleEngineFeeRequest mockPolicyRuleEngineFeeRequest() {
        PolicyRuleEngineFeeRequest policyRuleEngineFeeRequest = new PolicyRuleEngineFeeRequest();
        policyRuleEngineFeeRequest.setRuleEngine(PLATFORMFEE_RULE_ENGINE);
        List<Integer> ruleStatus = new ArrayList<>();
        ruleStatus.add(1);
        policyRuleEngineFeeRequest.setRuleStatus("1");
        List<Map<String, Object>> payload = new ArrayList<>();
        Map<String, Object> payload1 = new HashMap<>();
        payload1.put("verticalId", 76);
        payload1.put("amount", 3970);
        payload1.put("productId", 1200139321L);
        payload1.put("merchantId", 267095);
        payload1.put("categoryId", 26);
        List<String> segmentId = new ArrayList<>();
        payload1.put("segmentId", segmentId);
        payload.add(payload1);
        policyRuleEngineFeeRequest.setPayloads(payload);

        return policyRuleEngineFeeRequest;
    }


    @Test
    @DisplayName("checking if getting platform fee")
    public void testGetPlatformFeeViaPolicyEngine() throws Exception {
        Map<String, Object> requestParams = new HashMap<>();
        RechargeVerifyRequest request = getVerifyRequest().get(63779622L);
        List<String> segmentIds = new ArrayList<>();

        Product product = getProducts().get(63779622L);
        CVRProductCache.getInstance().addProductDetails(product);

        PolicyRuleEngineFeeRequest policyRuleEngineFeeRequest = mockPolicyRuleEngineFeeRequest();
        ApplyFeePayload pay=new ApplyFeePayload();
        pay.setTotalCalculatedFee(1.0);
        ResponseEntity<ApplyFeePayload> PolicyRuleEngineStatusResponse = new ResponseEntity<ApplyFeePayload>(pay,
                HttpStatus.OK);

        Mockito.when(rechargeHelperService.isFeatureLive(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(true);

        when(checkoutCommonService.isRechargesFsid(Mockito.any())).thenReturn(true);
        Mockito.when(policyRuleEngineClient.fetchPlatformFee(Mockito.any(), Mockito.any())).thenReturn(PolicyRuleEngineStatusResponse);

        ApplyFeePayload response = policyRuleEngineService.getPlatformFeeViaPolicyEngine(requestParams, request.getCartItems().get(0), null,  segmentIds,10.0);

        assertEquals(response.getTotalCalculatedFee(), 1.0);

    }

    @Test
    @DisplayName("checking if getting platform fee")
    public void testGetPlatformFeeViaPolicyEngineWithSubsItem() throws Exception {
        Map<String, Object> requestParams = new HashMap<>();
        RechargeVerifyRequest request1 = getVerifyRequest().get(63779622L);
        RechargeVerifyRequest request2 = getVerifyRequest().get(1200167124L);
        List<String> segmentIds = new ArrayList<>();

        Product product = getProducts().get(63779622L);
        Product product1 = getProducts().get(1200167124L);
        CVRProductCache.getInstance().addProductDetails(product);
        CVRProductCache.getInstance().addProductDetails(product1);

        PolicyRuleEngineFeeRequest policyRuleEngineFeeRequest = mockPolicyRuleEngineFeeRequest();
        ApplyFeePayload pay=new ApplyFeePayload();
        pay.setTotalCalculatedFee(1.0);
        ResponseEntity<ApplyFeePayload> PolicyRuleEngineStatusResponse = new ResponseEntity<ApplyFeePayload>(pay,
                HttpStatus.OK);

        Mockito.when(rechargeHelperService.isFeatureLive(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(true);

        when(checkoutCommonService.isRechargesFsid(Mockito.any())).thenReturn(true);
        Mockito.when(policyRuleEngineClient.fetchPlatformFee(Mockito.any(), Mockito.any())).thenReturn(PolicyRuleEngineStatusResponse);

        ApplyFeePayload response = policyRuleEngineService.getPlatformFeeViaPolicyEngine(requestParams, request1.getCartItems().get(0), request2.getCartItems().get(0),  segmentIds,10.0);

        assertEquals(response.getTotalCalculatedFee(), 1.0);

    }
}
