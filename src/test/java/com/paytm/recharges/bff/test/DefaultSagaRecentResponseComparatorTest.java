package com.paytm.recharges.bff.test;

import com.paytm.recharges.bff.constants.DateFormat;
import com.paytm.recharges.bff.datalayer.cache.CVRProductCache;
import com.paytm.recharges.bff.datalayer.dto.response.BillObject;
import com.paytm.recharges.bff.datalayer.dto.response.SagaRecentResponse;
import com.paytm.recharges.bff.datalayer.model.Product;
import com.paytm.recharges.bff.enums.EventState;
import com.paytm.recharges.bff.enums.EventType;
import com.paytm.recharges.bff.responsehandler.HomeReminderResponseHandlerUtil;
import com.paytm.recharges.bff.service.impl.DefaultSagaRecentResponseComparator;
import com.paytm.recharges.bff.utils.DateUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DefaultSagaRecentResponseComparatorTest {

    @Mock
    private HomeReminderResponseHandlerUtil homeReminderResponseHandlerUtil;

    @InjectMocks
    private DefaultSagaRecentResponseComparator comparator;

    @Before
    public void setup() {
        // Initialize mocks
    }

    @Test
    public void testCompare_AvoidDisconnection() {
        SagaRecentResponse o1 = new SagaRecentResponse();
        o1.setEventState(EventState.OLD_BILL_AVOID_DISCONNECTION.toString());
        o1.setDate(String.valueOf(new Date()));

        SagaRecentResponse o2 = new SagaRecentResponse();
        o2.setEventState(EventState.OLD_BILL_PENALTY.toString());
        o2.setDate(String.valueOf(new Date()));

        int result = comparator.compare(o1, o2);
        assertEquals(-1, result);
    }

    @Test
    public void testCompare_Penalty() {
        SagaRecentResponse o1 = new SagaRecentResponse();
        o1.setEventState(EventState.OLD_BILL_PENALTY.toString());
        o1.setDate(String.valueOf(new Date()));

        SagaRecentResponse o2 = new SagaRecentResponse();
        o2.setEventState(EventState.OLD_BILL_AVOID_DISCONNECTION.toString());
        o2.setDate(String.valueOf(new Date()));

        int result = comparator.compare(o1, o2);
        assertEquals(1, result);
    }


    @Test
    public void testCompare_SmartRecent() {
        SagaRecentResponse o1 = new SagaRecentResponse();
        o1.setEventType(EventType.valueOf(EventType.SMART_RECENT.toString()));
        o1.setDate(String.valueOf(new Date()));

        SagaRecentResponse o2 = new SagaRecentResponse();
        o2.setEventType(EventType.valueOf(EventType.RECENT.toString()));
        o2.setDate(String.valueOf(new Date()));

        int result = comparator.compare(o1, o2);
        assertEquals(1, result);
    }

    @Test
    public void testCompare_LowBalance() {
        SagaRecentResponse o1 = createSagaResponse("1", EventState.LOW_BALANCE.toString());
        SagaRecentResponse o2 = createSagaResponse("2", EventState.OLD_BILL_AVOID_DISCONNECTION.toString());
        Product product = new Product();
        product.setService("electricity");

        try (MockedStatic<CVRProductCache> mockedStatic = mockStatic(CVRProductCache.class)) {
            CVRProductCache cacheInstance = mock(CVRProductCache.class);
            when(CVRProductCache.getInstance()).thenReturn(cacheInstance);
//            when(cacheInstance.getProductDetails(anyLong())).thenReturn(product);
            int result = comparator.compare(o1, o2);
            assertEquals(1, result);
        }
    }

    @Test
    public void testCompare_HeuristicAnd_OLD_BILL_AVOID_DISCONNECTION() {
        SagaRecentResponse o1 = createSagaResponseWithBillDate("1", EventState.HEURISTIC_LOW_BALANCE.toString(), "2024-11-18");
        SagaRecentResponse o2 = createSagaResponseWithBillDate("2", EventState.OLD_BILL_AVOID_DISCONNECTION.toString(), "2024-11-19");
        Product product = new Product();
        product.setService("electricity");
        try (MockedStatic<CVRProductCache> mockedStatic = mockStatic(CVRProductCache.class)) {
            CVRProductCache cacheInstance = mock(CVRProductCache.class);
            when(CVRProductCache.getInstance()).thenReturn(cacheInstance);
//            when(cacheInstance.getProductDetails(anyLong())).thenReturn(product);

            int result = comparator.compare(o1, o2);
            assertEquals(1, result);
        }
    }

    @Test
    public void testCompare_OLD_BILL_AVOID_DISCONNECTION_andHeuristic() {
        SagaRecentResponse o1 = createSagaResponseWithBillDate("1", EventState.OLD_BILL_AVOID_DISCONNECTION.toString(), "2024-11-18");
        SagaRecentResponse o2 = createSagaResponseWithBillDate("2", EventState.HEURISTIC_LOW_BALANCE.toString(), "2024-11-19");
        Product product = new Product();
        product.setService("electricity");
        try (MockedStatic<CVRProductCache> mockedStatic = mockStatic(CVRProductCache.class)) {
            CVRProductCache cacheInstance = mock(CVRProductCache.class);
            when(CVRProductCache.getInstance()).thenReturn(cacheInstance);
//            when(cacheInstance.getProductDetails(anyLong())).thenReturn(product);

            int result = comparator.compare(o1, o2);
            assertEquals(-1, result);
        }
    }

    @Test
    public void testOldBillAvoidDisconnectionPriority() {
        SagaRecentResponse o1 = new SagaRecentResponse();
        o1.setEventState(EventState.OLD_BILL_AVOID_DISCONNECTION.toString());
        o1.setDate("2024-06-01 10:00:00");
        SagaRecentResponse o2 = new SagaRecentResponse();
        o2.setEventState(EventState.RECHARGE_FAILURE.toString());
        o2.setDate("2024-06-01 11:00:00");
        int result = comparator.compare(o1, o2);
        assertEquals(-1, result);
    }

    @Test
    public void testOldBillPenaltyPriority() {
        SagaRecentResponse o1 = new SagaRecentResponse();
        o1.setEventState(EventState.OLD_BILL_PENALTY.toString());
        o1.setDate("2024-06-01 10:00:00");
        SagaRecentResponse o2 = new SagaRecentResponse();
        o2.setEventState(EventState.RECHARGE_SUCCESS.toString());
        o2.setDate("2024-06-01 11:00:00");
        int result = comparator.compare(o1, o2);
        assertEquals(-1, result);
    }

    @Test
    public void testSmartRecentEventTypePriority() {
        SagaRecentResponse o1 = new SagaRecentResponse();
        o1.setEventType(EventType.SMART_RECENT);
        o1.setDate("2024-06-01 10:00:00");
        SagaRecentResponse o2 = new SagaRecentResponse();
        o2.setEventType(EventType.RECENT);
        o2.setDate("2024-06-01 11:00:00");
        int result = comparator.compare(o1, o2);
        assertEquals(1, result);
    }

    @Test
    public void testRenewAutomaticPriority() {
        SagaRecentResponse o1 = new SagaRecentResponse();
        o1.setEventState(EventState.RENEW_AUTOMATIC.toString());
        o1.setDate("2024-06-01 10:00:00");
        SagaRecentResponse o2 = new SagaRecentResponse();
        o2.setEventState(EventState.NEW_ACCOUNT.toString());
        o2.setDate("2024-06-01 11:00:00");
        int result = comparator.compare(o1, o2);
        assertEquals(-1, result);
    }

    @Test
    public void testAutomaticStatePriority() {
        SagaRecentResponse o1 = new SagaRecentResponse();
        o1.setAutomaticState(5);
        o1.setDate("2024-06-01 10:00:00");
        SagaRecentResponse o2 = new SagaRecentResponse();
        o2.setAutomaticState(1);
        o2.setDate("2024-06-01 11:00:00");
        int result = comparator.compare(o1, o2);
        assertEquals(-1, result);
    }

    @Test
    public void testPaytmPostpaidServicePriority() {
        SagaRecentResponse o1 = new SagaRecentResponse();
        o1.setPid("pid1");
        SagaRecentResponse o2 = new SagaRecentResponse();
        o2.setPid("pid2");
        Product postpaidProduct = new Product();
        postpaidProduct.setService("paytm_postpaid");
        Product otherProduct = new Product();
        otherProduct.setService("mobile");
        when(homeReminderResponseHandlerUtil.fetchProductDetails("pid1")).thenReturn(postpaidProduct);
        when(homeReminderResponseHandlerUtil.fetchProductDetails("pid2")).thenReturn(otherProduct);
        int result = comparator.compare(o1, o2);
        assertEquals(0, result);
    }

    @Test
    public void testDueDateComparison() {
        SagaRecentResponse o1 = new SagaRecentResponse();
        o1.setPid("pid1");
        BillObject bill1 = new BillObject();
        bill1.setBillDueDate("2024-06-10 10:00:00");
        o1.setBill(bill1);
        SagaRecentResponse o2 = new SagaRecentResponse();
        o2.setPid("pid2");
        BillObject bill2 = new BillObject();
        bill2.setBillDueDate("2024-06-11 10:00:00");
        o2.setBill(bill2);
        Product product = new Product();
        product.setService("mobile");
        when(homeReminderResponseHandlerUtil.fetchProductDetails(anyString())).thenReturn(product);
        int result = comparator.compare(o1, o2);
        assertEquals(-1, result);
    }

    @Test
    public void testNullDueDateHandling() {
        SagaRecentResponse o1 = new SagaRecentResponse();
        o1.setPid("pid1");
        BillObject bill1 = new BillObject();
        o1.setBill(bill1);
        SagaRecentResponse o2 = new SagaRecentResponse();
        o2.setPid("pid2");
        BillObject bill2 = new BillObject();
        bill2.setBillDueDate("2024-06-11 10:00:00");
        o2.setBill(bill2);
        Product product = new Product();
        product.setService("mobile");
        when(homeReminderResponseHandlerUtil.fetchProductDetails(anyString())).thenReturn(product);
        int result = comparator.compare(o1, o2);
        assertEquals(-1, result);
    }

    private SagaRecentResponse createSagaResponse(String pid, String eventState) {
        SagaRecentResponse response = new SagaRecentResponse();
        response.setPid(pid);
        response.setEventState(eventState);
        return response;
    }

    private SagaRecentResponse createSagaResponseWithBillDate(String pid, String eventState, String billDate) {
        SagaRecentResponse response = createSagaResponse(pid, eventState);
        BillObject bill = new BillObject();
        bill.setBillDate(billDate);
        response.setBill(bill);
        return response;
    }

}

