package com.paytm.recharges.bff.test;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTCreator;
import com.auth0.jwt.algorithms.Algorithm;
import com.paytm.recharges.bff.utils.JwtUtil;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;

import java.util.HashMap;

public class TestJwtUtil {
    @Test
    public void TestGenerateJwtToken()
    {
        JwtUtil jwtUtil=new JwtUtil();
        HashMap<String,String> payload=new HashMap<>();
        payload.put("key","value");
        String str=jwtUtil.generateJwtToken(payload,"paytm","key");
        Assert.assertNotNull(str);
    }
    @Test
    @DisplayName("Null map")
    public void TestGenerateJwtTokenNull()
    {
        JwtUtil jwtUtil=new JwtUtil();
        String str=jwtUtil.generateJwtToken(null,"paytm","key");
        Assert.assertNull(str );
    }

    @Test
    @DisplayName("validateTokenAndGetCustomerId Valid Case")
    public void TestValidateTokenAndGetCustomerIdTemp()
    {
        String abc=generateValidToken("123","awdsfddsfdcsdf");
        System.out.println("abc-------"+abc);
        Assert.assertNotNull(abc);
    }

    public String generateValidToken(String customerId, String jwtTokenSecretKey) {
        try {
            JWTCreator.Builder jwtBuilder = JWT.create();
            jwtBuilder.withClaim("ts", String.valueOf(System.currentTimeMillis()));
            jwtBuilder.withClaim("customerid", customerId);
            Algorithm algorithm = Algorithm.HMAC256(jwtTokenSecretKey);
            return "Bearer "+jwtBuilder.sign(algorithm);
        } catch (Throwable e) {
            System.out.print(e.getMessage());
        }
        return null;
    }
    //@Test
    //@DisplayName("validateTokenAndGetCustomerId Valid Case")
    public void TestValidateTokenAndGetCustomerId()
    {
        String cusotmerId="1940461621";
        String secret="";
        String token=generateValidToken(cusotmerId,secret);
        JwtUtil jwtUtil=new JwtUtil();
        Long customerId=jwtUtil.validateTokenAndGetCustomerId(token,secret);
        Assert.assertNotNull(customerId);
        Assert.assertEquals(Long.valueOf(cusotmerId),customerId);
    }

}
