package com.paytm.recharges.bff.test;

import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.assertEquals;

import java.util.*;

import com.paytm.recharges.bff.constants.Constants;
import com.paytm.recharges.bff.datalayer.dto.request.RechargeVerifyRequest;
import com.paytm.recharges.bff.datalayer.dto.response.Action;
import com.paytm.recharges.bff.datalayer.dto.response.RechargeItem;
import com.paytm.recharges.bff.enums.FeatureConfigName;
import com.paytm.recharges.bff.responsehandler.VerifyGenericEnrichMessageResponseHandler;
import com.paytm.recharges.bff.responsehandler.VerifyRentTFResponseHandler;
import com.paytm.recharges.bff.service.impl.RechargeHelperService;
import org.apache.commons.lang3.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.context.ConfigDataApplicationContextInitializer;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.util.ReflectionTestUtils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.paytm.recharges.bff.datalayer.cache.CVRProductCache;
import com.paytm.recharges.bff.datalayer.dto.DisplaySummaryKeys;
import com.paytm.recharges.bff.datalayer.dto.response.RechargeVerifyResponse;
import com.paytm.recharges.bff.datalayer.model.Product;
import com.paytm.recharges.bff.responsehandler.VerifyElectricityResponseHandler;
import com.paytm.recharges.bff.service.LocalisationManager;
import com.paytm.recharges.bff.utils.JsonUtil;
import com.paytm.recharges.bff.monitor.MetricsAgent;

@RunWith(MockitoJUnitRunner.Silent.class)
@ContextConfiguration(initializers = ConfigDataApplicationContextInitializer.class)
public class TestVerifyElectricityResponseHandler extends TestBaseRecharge {

    @Mock
    private LocalisationManager localisationManager;
    @Mock
    private RechargeHelperService rechargeHelperService;
    @Mock
    private VerifyGenericEnrichMessageResponseHandler verifyGenericEnrichMessageResponseHandler;
    @Mock
    private MetricsAgent metricsAgent;

    @InjectMocks
    private VerifyElectricityResponseHandler verifyElectricityResponseHandler;

    private String displaySummaryKeysStr = "[{\"keys\":[\"Due Date\"], \"priority\": 0}, {\"keys\": [\"Bill Date\"], \"priority\": 1}, {\"keys\": [\"Consumer Name\"], \"priority\": 2}, {\"keys\": [\"First Name\", \"Last Name\"], \"priority\": 3, \"label\": \"Consumer Name\"}]";

    private List<DisplaySummaryKeys> displaySummaryKeys;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
        displaySummaryKeys = JsonUtil.fromJson(displaySummaryKeysStr, new TypeReference<List<DisplaySummaryKeys>>() {
        });
    }

    @Test
    @DisplayName("consumer name not present")
    public void verifyElectricityTitleBarTest() {

        RechargeVerifyResponse verifyResponse = getVerifyResponse().get(1200167124L);
        Action action = new Action();
        Product product = getProducts().get(1200167124L);

        String rechargeNumber = (String) verifyResponse.getCart().getItems().get(0).getConfiguration()
                .get("recharge_number");

        Map<String, Object> titleBar = verifyElectricityResponseHandler.buildTitleBar(product, action, rechargeNumber);

        assertEquals(rechargeNumber, titleBar.get("title"));
        assertEquals(product.getBrand(), titleBar.get("subTitle"));
    }

    @Test
    @DisplayName("consumer name present")
    public void verifyElectricityTitleBarTestWithName() {

        RechargeVerifyResponse verifyResponse = getVerifyResponse().get(1200167124L);
        Action action = verifyResponse.getCart().getItems().get(0).getServiceOptions().getActions().get(0);
        Product product = getProducts().get(1200167124L);

        String rechargeNumber = (String) verifyResponse.getCart().getItems().get(0).getConfiguration()
                .get("recharge_number");

        Map<String, Object> titleBar = verifyElectricityResponseHandler.buildTitleBar(product, action, rechargeNumber);

        assertEquals("A SUNDARESAN NAIR", titleBar.get("title"));
        assertEquals(rechargeNumber, titleBar.get("subTitle"));
    }

    @Test
    @DisplayName("consumer name present with abbreviation")
    public void verifyElectricityTitleBarTestWithNameAbbr() {

        RechargeVerifyResponse verifyResponse = getVerifyResponse().get(1200763625L);
        Action action = verifyResponse.getCart().getItems().get(0).getServiceOptions().getActions().get(0);
        Product product = getProducts().get(1200763625L);

        String rechargeNumber = (String) verifyResponse.getCart().getItems().get(0).getConfiguration()
                .get("recharge_number");

        Map<String, Object> titleBar = verifyElectricityResponseHandler.buildTitleBar(product, action, rechargeNumber);

        assertEquals("Sharon Chaturgee Cho...", titleBar.get("title"));
        assertEquals(rechargeNumber, titleBar.get("subTitle"));
    }

    @Test
    public void verifyElectricityDisplaySummaryTest() {

        RechargeVerifyResponse verifyResponse = getVerifyResponse().get(1200167124L);

        Map<String, Object> displaySummary = verifyElectricityResponseHandler.buildDisplaySummary(
                verifyResponse.getCart().getItems().get(0).getServiceOptions().getActions().get(0).getDisplayValues(),
                displaySummaryKeys);

        assertEquals(2, displaySummary.size());
        assertEquals("10-03-2021", displaySummary.get("Due Date"));
    }

    @Test
    public void testElectricityMetaData() {

        RechargeVerifyResponse verifyResponse = getVerifyResponse().get(1200167124L);

        List<String> copyKeys = new ArrayList<String>();
        copyKeys.add("Consumer Name");
        ReflectionTestUtils.setField(verifyElectricityResponseHandler, "metaCopyKeys", copyKeys);
        Product product = getProducts().get(1200167124L);

        verifyElectricityResponseHandler.updateMetaData(verifyResponse.getCart().getItems().get(0), "en-IN", product);

        @SuppressWarnings("unchecked")
        Map<String, Object> additionalData = (Map<String, Object>) verifyResponse.getCart().getItems().get(0)
                .getMetaData().get("additionalData");
        assertNotNull(additionalData);
        assertEquals("A SUNDARESAN NAIR", additionalData.get("Consumer Name"));
    }


    @Test
    @DisplayName("Enrich popup V2 is not live on given version neither enrich pop V1")
    public void testSetInfoMessageValueWithEnrichV2VersionNonLive() {
        RechargeVerifyResponse verifyResponse = getVerifyResponse().get(322717357L);
        RechargeVerifyRequest verifyRequest= getVerifyRequest().get(322717357L);
        RechargeItem item=verifyResponse.getCart().getItems().get(0);
        Product product=getProducts().get(322717357L);
        CVRProductCache.getInstance().addProductDetails(product);
        String resmsg= "{\"errorMessageCode\":999,\"errorPopup\":{\"message\":\"The plan is only valid for users with active validity plan\"}}";
        String remsg= "{\"errorMessageCode\":999,\"errorPopup\":{\"message\":\"The plan is only valid for users with  plan\"}}";
        String[] key1 = {Constants.LANGUAGE_ENGLISH, "000",Constants.REQUEST_TYPE_VALIDATION_ENRICH_V2, String.valueOf(product.getVerticalId()), String.valueOf(product.getMerchantId()),product.getOperator()};
        String[] key2 = {Constants.LANGUAGE_ENGLISH, "000",Constants.REQUEST_TYPE_VALIDATION_ENRICH, String.valueOf(product.getVerticalId()), String.valueOf(product.getMerchantId()),product.getOperator()};
        Mockito.when(localisationManager.getMessage(Mockito.eq(key1), Mockito.eq(false), Mockito.anyMap())).thenReturn(remsg);
        Mockito.when(localisationManager.getMessage(Mockito.eq(key2), Mockito.eq(false), Mockito.anyMap())).thenReturn(resmsg);
        Map<String, Object> queryParams=new HashMap();
        queryParams.put("client","androidapp");
        queryParams.put("version","9.18.0");
        verifyRequest.setQueryParams(queryParams);
        Set<Long> productIdSet=new HashSet<>();
        productIdSet.add(322717357L);
        Mockito.when(rechargeHelperService.isFeatureLive(Mockito.eq(productIdSet),Mockito.eq(FeatureConfigName.enrichPopUpV2.name().toLowerCase()),Mockito.eq("androidapp"),Mockito.eq("9.18.0"))).thenReturn(false);
        verifyElectricityResponseHandler.setInfoMessage(verifyRequest, verifyResponse, item);
        assertEquals(verifyResponse.getCart().getErrorInfo(),"{\"errorMessageCode\":999}");
    }

    @Test
    @DisplayName("Enrich popup V2 and Enrich popup V1 is live on given version")
    public void testSetInfoMessageValueWithBothEnrichVersionLive() {
        RechargeVerifyResponse verifyResponse = getVerifyResponse().get(322717357L);
        RechargeVerifyRequest verifyRequest= getVerifyRequest().get(322717357L);
        RechargeItem item=verifyResponse.getCart().getItems().get(0);
        Product product=getProducts().get(322717357L);
        product.setServiceKey(StringUtils.lowerCase(product.getService()).replace(" ", ""));
        product.setPayTypeKey(StringUtils.lowerCase(product.getPayType()).replace(" ", ""));
        CVRProductCache.getInstance().addProductDetails(product);
        String resmsg= "{\"errorMessageCode\":999,\"errorPopup\":{\"message\":\"The plan is only valid for users with active validity plan\"}}";
        String resmsg2= "{\"errorMessageCode\":999}";
        String resmsg1= "{\"errorMessageCode\":999,\"errorPopup\":{\"message\":\"The plan is only valid for users with  plan\"}}";
        String[] key = {Constants.LANGUAGE_ENGLISH,Constants.REQUEST_TYPE_VALIDATION_ENRICH, "999", String.valueOf(product.getVerticalId()), String.valueOf(product.getMerchantId()),product.getPayTypeKey(),product.getServiceKey(), product.getOperator()};
        Mockito.when(localisationManager.getMessage(Mockito.eq(key), Mockito.eq(true), Mockito.anyMap())).thenReturn(resmsg);
        String[] key1 = {Constants.LANGUAGE_ENGLISH,Constants.REQUEST_TYPE_VALIDATION_ENRICH_V2, "999", String.valueOf(product.getVerticalId()), String.valueOf(product.getMerchantId()),product.getPayTypeKey(),product.getServiceKey(), product.getOperator()};
        Mockito.when(localisationManager.getMessage(Mockito.eq(key1), Mockito.eq(true), Mockito.anyMap())).thenReturn(resmsg1);
        Map<String, Object> queryParams=new HashMap();
        queryParams.put("client","androidapp");
        queryParams.put("version","9.18.0");
        verifyRequest.setQueryParams(queryParams);
        Set<Long> productIdSet=new HashSet<>();
        productIdSet.add(322717357L);
        Mockito.when(rechargeHelperService.isFeatureLive(Mockito.eq(productIdSet),Mockito.eq(FeatureConfigName.enrichPopUpV2.name().toLowerCase()),Mockito.eq("androidapp"),Mockito.eq("9.18.0"))).thenReturn(true);
        Mockito.when(rechargeHelperService.isFeatureLive(Mockito.eq(productIdSet),Mockito.eq(FeatureConfigName.enrichPopUp.name().toLowerCase()),Mockito.eq("androidapp"),Mockito.eq("9.18.0"))).thenReturn(true);
        Mockito.when(verifyGenericEnrichMessageResponseHandler.isValidErrorCode(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(resmsg);
        verifyElectricityResponseHandler.setInfoMessage(verifyRequest, verifyResponse, item);
        assertEquals(verifyResponse.getCart().getErrorInfo(),resmsg1);
    }

    @Test
    @DisplayName("Enrich popup V1 is live on given version but enrich pop V2")
    public void testSetInfoMessageValueWithEnrichV1Live() {
        RechargeVerifyResponse verifyResponse = getVerifyResponse().get(322717357L);
        RechargeVerifyRequest verifyRequest= getVerifyRequest().get(322717357L);
        RechargeItem item=verifyResponse.getCart().getItems().get(0);
        Product product=getProducts().get(322717357L);
        product.setServiceKey(StringUtils.lowerCase(product.getService()).replace(" ", ""));
        product.setPayTypeKey(StringUtils.lowerCase(product.getPayType()).replace(" ", ""));
        CVRProductCache.getInstance().addProductDetails(product);
        String resmsg= "{\"errorMessageCode\":999,\"errorPopup\":{\"message\":\"The plan is only valid for users with active validity plan\"}}";
        String resmsg1= "{\"errorMessageCode\":999,\"errorPopup\":{\"message\":\"The plan is only valid for users with  plan\"}}";
        String resmsg2= "{\"errorMessageCode\":999}";
        String[] key = {Constants.LANGUAGE_ENGLISH,Constants.REQUEST_TYPE_VALIDATION_ENRICH, "999", String.valueOf(product.getVerticalId()), String.valueOf(product.getMerchantId()),product.getPayTypeKey(),product.getServiceKey(), product.getOperator()};
        Mockito.when(localisationManager.getMessage(Mockito.eq(key), Mockito.eq(true), Mockito.anyMap())).thenReturn(resmsg);
        String[] key1 = {Constants.LANGUAGE_ENGLISH,Constants.REQUEST_TYPE_VALIDATION_ENRICH_V2,"999", String.valueOf(product.getVerticalId()), String.valueOf(product.getMerchantId()),product.getPayTypeKey(),product.getServiceKey(), product.getOperator()};
        Mockito.when(localisationManager.getMessage(Mockito.eq(key1), Mockito.eq(true), Mockito.anyMap())).thenReturn(resmsg1);
        Map<String, Object> queryParams=new HashMap();
        queryParams.put("client","androidapp");
        queryParams.put("version","9.17.0");
        verifyRequest.setQueryParams(queryParams);
        Set<Long> productIdSet=new HashSet<>();
        productIdSet.add(322717357L);
        Mockito.when(rechargeHelperService.isFeatureLive(Mockito.eq(productIdSet),Mockito.eq(FeatureConfigName.enrichPopUp.name().toLowerCase()),Mockito.eq("androidapp"),Mockito.eq("9.17.0"))).thenReturn(true);
        verifyElectricityResponseHandler.setInfoMessage(verifyRequest, verifyResponse, item);
        assertEquals(verifyResponse.getCart().getErrorInfo(),resmsg);
    }


    @Test
    @DisplayName("Enrich popup V1 and Enrich popup V1  are non live")
    public void testSetInfoMessageValueWithBothEnrichNonLIve() {
        RechargeVerifyResponse verifyResponse = getVerifyResponse().get(322717357L);
        RechargeVerifyRequest verifyRequest= getVerifyRequest().get(322717357L);
        RechargeItem item=verifyResponse.getCart().getItems().get(0);
        Product product=getProducts().get(322717357L);
        CVRProductCache.getInstance().addProductDetails(product);
        String resmsg= "{\"errorMessageCode\":999,\"errorPopup\":{\"message\":\"The plan is only valid for users with active validity plan\"}}";
        String resmsg1= "{\"errorMessageCode\":999,\"errorPopup\":{\"message\":\"The plan is only valid for users with  plan\"}}";
        String[] key = {Constants.LANGUAGE_ENGLISH, "999",Constants.REQUEST_TYPE_VALIDATION_ENRICH, String.valueOf(product.getVerticalId()), String.valueOf(product.getMerchantId()), product.getOperator()};
        Mockito.when(localisationManager.getMessage(Mockito.eq(key), Mockito.eq(false), Mockito.anyMap())).thenReturn(resmsg);
        String[] key1 = {Constants.LANGUAGE_ENGLISH, "999",Constants.REQUEST_TYPE_VALIDATION_ENRICH_V2, String.valueOf(product.getVerticalId()), String.valueOf(product.getMerchantId()), product.getOperator()};
        Mockito.when(localisationManager.getMessage(Mockito.eq(key1), Mockito.eq(false), Mockito.anyMap())).thenReturn(resmsg1);
        Map<String, Object> queryParams=new HashMap();
        queryParams.put("client","androidapp");
        queryParams.put("version","9.17.0");
        verifyRequest.setQueryParams(queryParams);
        Set<Long> productIdSet=new HashSet<>();
        productIdSet.add(322717357L);
        Mockito.when(rechargeHelperService.isFeatureLive(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(false);
        verifyElectricityResponseHandler.setInfoMessage(verifyRequest, verifyResponse, item);
        assertEquals(verifyResponse.getCart().getErrorInfo(),"{\"errorMessageCode\":999}");
    }


}
