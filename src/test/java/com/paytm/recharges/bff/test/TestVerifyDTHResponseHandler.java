package com.paytm.recharges.bff.test;

import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.assertEquals;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.paytm.recharges.bff.datalayer.dto.response.Action;
import com.paytm.recharges.bff.monitor.MetricsAgent;
import com.paytm.recharges.bff.responsehandler.VerifyDTHResponseHandler;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.context.ConfigDataApplicationContextInitializer;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.util.ReflectionTestUtils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.paytm.recharges.bff.datalayer.cache.CVRProductCache;
import com.paytm.recharges.bff.datalayer.dto.DisplaySummaryKeys;
import com.paytm.recharges.bff.datalayer.dto.response.RechargeVerifyResponse;
import com.paytm.recharges.bff.datalayer.model.Product;
import com.paytm.recharges.bff.responsehandler.VerifyElectricityResponseHandler;
import com.paytm.recharges.bff.service.LocalisationManager;
import com.paytm.recharges.bff.utils.JsonUtil;

@RunWith(MockitoJUnitRunner.Silent.class)
@ContextConfiguration(initializers = ConfigDataApplicationContextInitializer.class)
public class TestVerifyDTHResponseHandler extends TestBaseRecharge {

    @Mock
    private LocalisationManager localisationManager;

    @Mock
    private MetricsAgent metricsAgent;

    @InjectMocks
    private VerifyDTHResponseHandler verifyDTHResponseHandler;

    private String displaySummaryKeysStr = "[{\"keys\":[\"Due Date\"], \"priority\": 0}]";

    private List<DisplaySummaryKeys> displaySummaryKeys;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
        displaySummaryKeys = JsonUtil.fromJson(displaySummaryKeysStr, new TypeReference<List<DisplaySummaryKeys>>() {
        });
    }

    @Test
    @DisplayName("consumer name not present")
    public void verifyDTHTitleBarTest() {

        RechargeVerifyResponse verifyResponse = getVerifyResponse().get(1200167124L);
        Action action = new Action();
        Product product = getProducts().get(1200167124L);

        String rechargeNumber = (String) verifyResponse.getCart().getItems().get(0).getConfiguration()
                .get("recharge_number");

        Map<String, Object> titleBar = verifyDTHResponseHandler.buildTitleBar(product, action, rechargeNumber);

        assertEquals(rechargeNumber, titleBar.get("title"));
        assertEquals(product.getBrand(), titleBar.get("subTitle"));
    }

    @Test
    @DisplayName("consumer name present")
    public void verifyDTHTitleBarTestWithName() {

        RechargeVerifyResponse verifyResponse = getVerifyResponse().get(1200167124L);
        Action action = verifyResponse.getCart().getItems().get(0).getServiceOptions().getActions().get(0);
        Product product = getProducts().get(1200167124L);

        String rechargeNumber = (String) verifyResponse.getCart().getItems().get(0).getConfiguration()
                .get("recharge_number");

        Map<String, Object> titleBar = verifyDTHResponseHandler.buildTitleBar(product, action, rechargeNumber);

        assertEquals("A SUNDARESAN NAIR", titleBar.get("title"));
        assertEquals(rechargeNumber, titleBar.get("subTitle"));
    }

    @Test
    @DisplayName("consumer name present with abbreviation")
    public void verifyDTHTitleBarTestWithNameAbbr() {

        RechargeVerifyResponse verifyResponse = getVerifyResponse().get(1200763625L);
        Action action = verifyResponse.getCart().getItems().get(0).getServiceOptions().getActions().get(0);
        Product product = getProducts().get(1200763625L);

        String rechargeNumber = (String) verifyResponse.getCart().getItems().get(0).getConfiguration()
                .get("recharge_number");

        Map<String, Object> titleBar = verifyDTHResponseHandler.buildTitleBar(product, action, rechargeNumber);

        assertEquals("Sharon Chaturgee Cho...", titleBar.get("title"));
        assertEquals(rechargeNumber, titleBar.get("subTitle"));
    }

    @Test
    public void verifyDTHDisplaySummaryTest() {

        RechargeVerifyResponse verifyResponse = getVerifyResponse().get(1200167124L);

        Map<String, Object> displaySummary = verifyDTHResponseHandler.buildDisplaySummary(
                verifyResponse.getCart().getItems().get(0).getServiceOptions().getActions().get(0).getDisplayValues(),
                displaySummaryKeys);

        assertEquals(1, displaySummary.size());
        assertEquals("10-03-2021", displaySummary.get("Due Date"));
    }

    @Test
    public void testDTHMetaData() {

        RechargeVerifyResponse verifyResponse = getVerifyResponse().get(1200167124L);

        List<String> copyKeys = new ArrayList<String>();
        copyKeys.add("Consumer Name");
        ReflectionTestUtils.setField(verifyDTHResponseHandler, "metaCopyKeys", copyKeys);
        Product product = getProducts().get(1200167124L);

        verifyDTHResponseHandler.updateMetaData(verifyResponse.getCart().getItems().get(0), "en-IN", product);

        @SuppressWarnings("unchecked")
        Map<String, Object> additionalData = (Map<String, Object>) verifyResponse.getCart().getItems().get(0)
                .getMetaData().get("additionalData");
        assertNotNull(additionalData);
        assertEquals("A SUNDARESAN NAIR", additionalData.get("Consumer Name"));
    }
}