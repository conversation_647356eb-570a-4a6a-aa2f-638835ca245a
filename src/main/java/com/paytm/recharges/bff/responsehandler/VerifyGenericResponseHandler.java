package com.paytm.recharges.bff.responsehandler;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.paytm.recharges.bff.constants.Constants;
import com.paytm.recharges.bff.datalayer.cache.CVRProductCache;
import com.paytm.recharges.bff.datalayer.cache.ServiceConfigCache;
import com.paytm.recharges.bff.datalayer.dto.DisplaySummaryKeys;
import com.paytm.recharges.bff.datalayer.dto.request.RechargeVerifyRequest;
import com.paytm.recharges.bff.datalayer.dto.response.Action;
import com.paytm.recharges.bff.datalayer.dto.response.Action.DisplayValue;
import com.paytm.recharges.bff.datalayer.dto.response.ExtendedCachePopup;
import com.paytm.recharges.bff.datalayer.dto.response.RechargeItem;
import com.paytm.recharges.bff.datalayer.dto.response.RechargeVerifyResponse;
import com.paytm.recharges.bff.datalayer.dto.response.ServiceOptions;
import com.paytm.recharges.bff.datalayer.model.Product;
import com.paytm.recharges.bff.enums.FeatureConfigName;
import com.paytm.recharges.bff.monitor.MetricsAgent;
import com.paytm.recharges.bff.service.LocalisationManager;
import com.paytm.recharges.bff.service.impl.RechargeHelperService;
import com.paytm.recharges.bff.utils.JsonUtil;
import com.paytm.recharges.bff.utils.Utils;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import static com.paytm.recharges.bff.constants.Constants.DISPLAY_SUMMARY_KEYS;

@Component
public  class VerifyGenericResponseHandler implements VerifyResponseHandler, VerifyGenericEnrichMessageResponseHandler {
    private static final CustomLogger log = CustomLogManager.getLogger(VerifyGenericResponseHandler.class);

    @Autowired
    protected LocalisationManager localisationManager;

    @Autowired
    protected RechargeHelperService rechargeHelperService;

    @Autowired
    private MetricsAgent metricsAgent;


    @Value("#{'${recharges.meta_data.copy_keys}'.split(',')}")
    protected List<String> metaCopyKeys;
    @Override
    public void handleResponse(RechargeVerifyRequest verifyRequest, RechargeVerifyResponse verifyResponse,
            RechargeItem item) {
        log.debug("VerifyGenericResponseHandler::handleResponse");
        String language = Objects.isNull(verifyRequest.getQueryParams()) ? Constants.LANGUAGE_ENGLISH
                : (String) verifyRequest.getQueryParams().getOrDefault("locale", Constants.LANGUAGE_ENGLISH);
        Product product = CVRProductCache.getInstance().getProductDetails(item.getProductId());
        setDisplaySummary(item);
        setTitleBar(item);
        setInfoMessage(verifyRequest, verifyResponse, item);
        updateMetaData(item, language, product);
        updateServeExtendedCache(verifyRequest,item);
    }

    public void updateServeExtendedCache(RechargeVerifyRequest verifyRequest,RechargeItem item){
        if (Objects.isNull(item.getServiceOptions()) || Objects.isNull(item.getServiceOptions().getActions()) || Objects.isNull(item.getMetaData())) {
            return;
        }
        Map<String, Object> queryParams = verifyRequest.getQueryParams();
        //handling cases where query params are null - express flow
        if (Objects.isNull(queryParams)) {
            return;
        }
        String language = Objects.isNull(verifyRequest.getQueryParams()) ? Constants.LANGUAGE_ENGLISH
                : (String) verifyRequest.getQueryParams().getOrDefault("locale", Constants.LANGUAGE_ENGLISH);
        Product product = CVRProductCache.getInstance().getProductDetails(item.getProductId());
        log.info("updateServeExtendedCache::  language "+language);
        if (item.getMetaData().get("check_operator_down") != null && item.getMetaData().get("errorMessageCode") != null && item.getMetaData().get("check_operator_down").toString().equals("true") && item.getMetaData().get("errorMessageCode").toString().equals("OPERATORDOWN")) {
            Action action;
            if (item.getServiceOptions().getActions().size() == 0) {
                action = new Action();
                item.getServiceOptions().getActions().add(action);
            } else
                action = item.getServiceOptions().getActions().get(0);

            if (Objects.isNull(action.getInfoMessage())) {
                action.setInfoMessage(new HashMap<>());
            }

            log.info("updateServeExtendedCache::  checking message");

            String keys[] = infoMessageKeysCache(product,action, language);
            Map<String, String> payload = new HashMap<>();
            if(item.getMetaData().get("fetchDate")!=null)
                payload.put("fetchDate",item.getMetaData().get("fetchDate").toString());
            payload.put("operator",product.getOperator());
            String message = localisationManager.getMessage(keys, true, payload);
            ExtendedCachePopup extendedCachePopup;
            try {
                extendedCachePopup=JsonUtil.fromJson(message, ExtendedCachePopup.class);
            }
            catch (Exception e){
                log.error("updateServeExtendedCache:: localisation message not found keys {}", keys);
                log.error("updateServeExtendedCache::  message: {}",message);
                log.error("updateServeExtendedCache:: Error {}",e);
                return;
            }
            action.getInfoMessage().put("infoMessageVisibility", "1");
            if (Objects.isNull(action.getInfoMessage().get("infoMessage")))
                action.getInfoMessage().put("infoMessage", "");
            ExtendedCachePopup extendedCachePopup_shortMessage=new ExtendedCachePopup();
            extendedCachePopup_shortMessage.setShortMessage(extendedCachePopup.getShortMessage());
            action.getInfoMessage().put("infoMessageCTA",extendedCachePopup_shortMessage);
            String client = (String) queryParams.getOrDefault("client",""),
                    version = (String) queryParams.getOrDefault("version","");
            Set<Long> productIdSet = new HashSet<>();
            productIdSet.add(item.getProductId());
            if (!rechargeHelperService.isFeatureLive(productIdSet, FeatureConfigName.extendedCachePopup.name().toLowerCase(),client,version)) {
                return;
            }
            action.getInfoMessage().put("infoMessageCTA",extendedCachePopup);
            log.info("updateServeExtendedCache::  function success");
        }
    }
    private String[] infoMessageKeysCache(Product product, Action action, String language) {
        String keys[] = new String[] { language, "infoMessageCTAPopup",
                product.getService().toLowerCase()};
        return keys;

    }
    @SuppressWarnings("unchecked")
    public void updateMetaData(RechargeItem item, String language, Product product) {
        if (Objects.isNull(item.getServiceOptions()) || Objects.isNull(item.getServiceOptions().getActions())) {
            return;
        }

        Map<String, Object> metaData = item.getMetaData();
        if (Objects.isNull(metaData)) {
            metaData = new HashMap<>();
            item.setMetaData(metaData);
        }
        List<Action> actions = item.getServiceOptions().getActions();

        addAdditionalData(metaData, actions);
        additionalBillInfo(metaData, actions, language, product);
    }

    protected void addAdditionalData(Map<String, Object> metaData, List<Action> actions) {
        Object additionalDataObj = metaData.get("additionalData");

        Map<String, Object> additionalData = null;

        if(Objects.isNull(additionalDataObj)) {
            additionalData = new HashMap<String, Object>();
        } else {

            try {
                additionalData = new HashMap<String, Object>();
                log.info("metaData.additionalDataObj {} ", additionalDataObj.toString());

                additionalDataObj.toString().replace('=', ':');
                JSONObject additionalDataJson = new JSONObject(additionalDataObj.toString().replace('=', ':'));
                log.info("additionalDataJson is {}", additionalDataJson.toString());
                Iterator it = additionalDataJson.keys();

                while (it.hasNext()) {
                    String key = it.next().toString();
                    String value = additionalDataJson.get(key).toString();
                    additionalData.put(key, value);
                }

            }
            catch(Exception ex){
                log.error("exception occurred for additionalDataObj {}", ex);
            }


        }

        for (Action action : actions) {

                for (String key : metaCopyKeys) {
                    if (Objects.nonNull(action.getDisplayValues())) {
                        String value = getDisplayValue(action.getDisplayValues(), key);
                        additionalData.put(key, value);
                    }
                }


        }
        metaData.put("additionalData", additionalData);
    }

    public void additionalBillInfo(Map<String, Object> metaData, List<Action> actions, String language, Product product) {

        Object additionalBillInfo = metaData.get("additionalBillInfo");

        if(Objects.isNull(additionalBillInfo)) {
            return;
        }
        
        List<Map<String, Object>> additionalBillInfoList = JsonUtil.fromObject(additionalBillInfo, List.class);

        for(Map<String, Object> map : additionalBillInfoList) {
            log.info("additionalBillInfo:: additionalBillInfoList data {} ", map);
            if(Objects.nonNull(map.get("isApplicable")) && "true".equalsIgnoreCase((String) map.get("isApplicable"))) {

                // get localisation message
                // key VALIDATION~BILL_LABEL~INCENTIVE~0, VALIDATION~BILL_LABEL~LATEFEE~0
                String keys[] = new String[] { language, "VALIDATION", "BILL_LABEL",
                        (String) map.get("type"), Long.toString(product.getCategoryId()) };
                Map<String, String> payload = new HashMap<>();
                payload.put("amount", map.get("amount")+"");
                String locMessage = localisationManager.getMessage(keys, true, payload);
                if(StringUtils.isEmpty(locMessage)) {
                    log.error("additionalBillInfo:: localisation message not found keys {}", keys);
                    continue;
                }
                log.info("additionalBillInfo:: localisation message {}", locMessage);
                //{ "billLabel": "Pay Now and Get instant discount of ₹$40", "billIcon": "", "billBgcolor": "", "billFontcolor": ""}
                Map<String, Object> locMessageMap = JsonUtil.fromJson(locMessage, new TypeReference<Map<String, Object>>() {
                });

                Map<String, Object> incentiveDetails = new HashMap<>();
                incentiveDetails.put("isApplicable", map.get("isApplicable"));
                incentiveDetails.put("incentiveAmount", map.get("amount"));
                incentiveDetails.put("incentiveDate", map.get("date"));

                // localization details
                incentiveDetails.put("incentiveLabel", locMessageMap.get("billLabel"));
                incentiveDetails.put("incentiveIcon", locMessageMap.get("billIcon"));
                incentiveDetails.put("incentiveBgcolor", locMessageMap.get("billBgcolor"));
                incentiveDetails.put("incentiveFontcolor", locMessageMap.get("billFontcolor"));

                // set in action[i].additionalUserInfo
                for(Action action : actions) {
                    if (Objects.isNull(action.getAdditionalUserInfo())) {
                        action.setAdditionalUserInfo(new HashMap<>());
                    }
                    Map<String, Object> additionalUserInfo = action.getAdditionalUserInfo();
                    additionalUserInfo.put("incentiveDetails", incentiveDetails);
                    log.info("additionalBillInfo:: added incentiveDetails {}", incentiveDetails);
                }
                break;
            }
        }
    }

    public void setDisplaySummary(RechargeItem item) {
        String categoryName = rechargeHelperService.getCategoryName(item);
        List<DisplaySummaryKeys> displaySummaryKeys = getDisplaySummaryKeys(categoryName);

        log.debug("VerifyGenericResponseHandler::setDisplaySummary::keys {}", displaySummaryKeys);
        ServiceOptions serviceOptions = item.getServiceOptions();
        log.debug("VerifyGenericResponseHandler::setDisplaySummary::serviceOptions:  {}", serviceOptions);
        if (Objects.nonNull(serviceOptions)) {
            List<Action> actions = serviceOptions.getActions();
            if (Objects.nonNull(actions)) {
                for (Action action : actions) {
                    action.setDisplaySummary(buildDisplaySummary(action.getDisplayValues(), displaySummaryKeys));
                }
            }
        }
    }

    public List<DisplaySummaryKeys> getDisplaySummaryKeys(String categoryName) {

        Map<String, List<DisplaySummaryKeys>> displaySummaryKeyMap = null;

        try {
            displaySummaryKeyMap = JsonUtil.convertObjectToMap(ServiceConfigCache.getInstance().get(DISPLAY_SUMMARY_KEYS), new TypeReference<Map<String, List<DisplaySummaryKeys>>>() {
            });
            return Objects.requireNonNull(displaySummaryKeyMap).get(categoryName);
        } catch (NullPointerException | IllegalArgumentException | ClassCastException | JsonProcessingException e) {
            log.error("VerifyGenericResponseHandler::getDisplaySummaryFromServiceConfig :: error while fetching display summary" +
                    " from service config {}", e);
        }
        return null;

    }
    public void setTitleBar(RechargeItem item) {
        Product product = CVRProductCache.getInstance().getProductDetails(item.getProductId());
        ServiceOptions serviceOptions = item.getServiceOptions();
        if(Objects.isNull(serviceOptions))
        {
            serviceOptions=new ServiceOptions();
            item.setServiceOptions(serviceOptions);

        }
        List<Action> actions = serviceOptions.getActions();
        String rechargeNumber = Objects.isNull(item.getConfiguration()) ? ""
                    : (String) item.getConfiguration().get("recharge_number");
        if(Objects.isNull(actions))
        {
            actions=new ArrayList<Action>();
            Action action=new Action();
            actions.add(action);
            serviceOptions.setActions(actions);

        }
        for (Action action : actions) {
                    action.setTitleBar(buildTitleBar(product, action, rechargeNumber));
        }


    }

    public void setInfoMessage(RechargeVerifyRequest verifyRequest, RechargeVerifyResponse verifyResponse,
            RechargeItem item) {

        if (!infoMessageEnabled())
            return;

        if (Objects.isNull(item.getServiceOptions()) || Objects.isNull(item.getServiceOptions().getActions())) {
            return;
        }

        String language = Objects.isNull(verifyRequest.getQueryParams()) ? Constants.LANGUAGE_ENGLISH
                : (String) verifyRequest.getQueryParams().getOrDefault("locale", Constants.LANGUAGE_ENGLISH);
        Product product = CVRProductCache.getInstance().getProductDetails(item.getProductId());
        List<Action> actions = item.getServiceOptions().getActions();
        for (Action action : actions) {
            if (Objects.nonNull(action.getInfoMessage())) {
                String keys[] = infoMessageKeys(product, action, language);
                Map<String, String> payload = infoMessagePayload(product, action, item);
                String message = localisationManager.getMessage(keys, true, payload);

                if (!StringUtils.isEmpty(message)) {
                    String infoMessageOrientation = Utils
                            .getAsString(action.getInfoMessage().get("infoMessageOrientation"));
                    if (Constants.ORIENTATION_ENRICH.equals(infoMessageOrientation)) {
                        log.debug("setInfoMessage:: setting enrich popup {}", message);
                        rechargeHelperService.setEnrichPopup(verifyResponse, message);
                    } else {
                        log.debug("setInfoMessage:: setting infoMessage {}", message);
                        action.getInfoMessage().put("infoMessage", message);
                    }
                } else {
                    log.info("No message found for keys {}", JsonUtil.toJson(keys));
                }
                // remove info message object in case infoMessage field is empty
                if (ObjectUtils.isEmpty(action.getInfoMessage().get("infoMessage"))) {
                    log.error("setInfoMessage:: infoMessage is null");
                    action.setInfoMessage(null);
                }
            }
        }
    }

    public void getLatestInfoMessage(RechargeVerifyRequest verifyRequest, RechargeVerifyResponse verifyResponse,
                                     RechargeItem item, Map<String, String> payload) {
        Map<String, Object> queryParams = verifyRequest.getQueryParams();

        log.info("VerifyGenericResponseHandler.getLatestInfoMessage:: queryParams: {}", queryParams);

        //handling cases where query params are null - express flow
        if (Objects.isNull(queryParams)) {
            return;
        }
//        String enrichVersion = decideVersion(verifyRequest,verifyResponse,item,queryParams);
//        if(Objects.nonNull(enrichVersion))
//            setEnrichMessage(verifyRequest, verifyResponse, item, enrichVersion);
        String client = (String) queryParams.getOrDefault(Constants.CLIENT, ""),
                version = (String) queryParams.getOrDefault(Constants.VERSION, "");
        Set<Long> productIdSet = new HashSet<>();
        productIdSet.add(item.getProductId());
        VerifyGenericEnrichMessageResponseHandler verifyGenericEnrichMessageResponseHandler = new VerifyGenericResponseHandler();
        String errorCode = verifyGenericEnrichMessageResponseHandler.isValidErrorCode(verifyResponse, item, verifyRequest);
        JSONObject errorInfo = getErrorInfo(verifyResponse, item, verifyRequest);

        log.info("VerifyGenericResponseHandler.getLatestInfoMessage:: errorCode, {}", errorCode);

        if(Objects.nonNull(errorCode)){
            extraProcessing(errorInfo,item,productIdSet,verifyResponse,errorCode,client, version);
            if (verifyResponse.getCart().getErrorInfo()!=null&&!new JSONObject(verifyResponse.getCart().getErrorInfo().toString()).has("errorPopup")&&rechargeHelperService.isFeatureLive(productIdSet, FeatureConfigName.enrichPopUpV2.name().toLowerCase(), client, version)) {
                setEnrichMessage(verifyResponse, item,Constants.VERSION_2,errorCode,verifyGenericEnrichMessageResponseHandler, payload);
            }
            if (verifyResponse.getCart().getErrorInfo()!=null&&!new JSONObject(verifyResponse.getCart().getErrorInfo().toString()).has("errorPopup")&&rechargeHelperService.isFeatureLive(productIdSet, FeatureConfigName.enrichPopUp.name().toLowerCase(), client, version)) {
                setEnrichMessage(verifyResponse, item, Constants.VERSION_1,errorCode,verifyGenericEnrichMessageResponseHandler, payload);
            }
        }

        else {
            new VerifyGenericResponseHandler().setInfoMessage(verifyRequest,verifyResponse,item);
        }
    }


    public void setEnrichMessage(RechargeVerifyResponse verifyResponse,
                                 RechargeItem item,String version,String errorCode,VerifyGenericEnrichMessageResponseHandler verifyGenericEnrichMessageResponseHandler,Map<String, String> payload) {

        if (Objects.nonNull(errorCode))
            verifyGenericEnrichMessageResponseHandler.fetchEnrichMessage( verifyResponse,item, version, localisationManager,errorCode,payload);

    }
    public String decideVersion(RechargeVerifyRequest verifyRequest, RechargeVerifyResponse verifyResponse,
                                  RechargeItem item,Map<String, Object> queryParams){

        String client = (String) queryParams.getOrDefault(Constants.CLIENT, ""),
                version = (String) queryParams.getOrDefault(Constants.VERSION, "");
        Set<Long> productIdSet = new HashSet<>();
        productIdSet.add(item.getProductId());
        if (rechargeHelperService.isFeatureLive(productIdSet, FeatureConfigName.enrichPopUpV2.name().toLowerCase(), client, version)) {
            return Constants.VERSION_2;
        }
        if (verifyResponse.getCart().getErrorInfo()!=null&&!new JSONObject(verifyResponse.getCart().getErrorInfo().toString()).has("errorPopup")&&rechargeHelperService.isFeatureLive(productIdSet, FeatureConfigName.enrichPopUp.name().toLowerCase(), client, version)) {
            return Constants.VERSION_1;
        }
        return null;
    }
    public String[] infoMessageKeys(Product product, Action action, String language) {
        // override in category
        return null;
    }

    public Map<String, String> infoMessagePayload(Product product, Action action, RechargeItem item) {
        // override in category
        return null;
    }

    public Map<String, Object> buildDisplaySummary(List<DisplayValue> displayValues,
                                                   List<DisplaySummaryKeys> displaySummaryKeys) {

        Map<String, Object> displaySummary = new LinkedHashMap<>();

        if (Objects.isNull(displayValues) || displayValues.isEmpty()) {
            log.info("VerifyGenericResponseHandler::buildDisplaySummary:: displayValues or displaySummaryKeys  is null");
            return displaySummary;
        }


        if(Objects.isNull(displaySummaryKeys)) {
            if(!StringUtils.isEmpty(displayValues.get(0).getLabel())&&!ObjectUtils.isEmpty(displayValues.get(0).getValue())) {
                displaySummary.put(displayValues.get(0).getLabel(),displayValues.get(0).getValue());
            }
            log.debug("buildDefaultDisplaySummary:: displaySummary {}", JsonUtil.toJson(displaySummary));
            return displaySummary;
        }else if(displaySummaryKeys.isEmpty()){
            return displaySummary;
        }

        Collections.sort(displaySummaryKeys);

        // create lable map
        Map<String, DisplayValue> displayValueMap = new HashMap<>();
        for (DisplayValue dv : displayValues) {
            if (!ObjectUtils.isEmpty(dv.getValue())) {
                displayValueMap.put(dv.getLabel().toLowerCase(), dv);
            }
        }

        buildSummaryLabel(displaySummaryKeys, displayValueMap, displaySummary);
        log.info("VerifyGenericResponseHandler::buildDisplaySummary:: displaySummary {} ", displaySummary);
        return displaySummary;
    }

    private void buildSummaryLabel(List<DisplaySummaryKeys> displaySummaryKeys, Map<String, DisplayValue> displayValueMap
            , Map<String, Object> displaySummary) {
        int lastPriority = -1;
        int presentPriority;
        for (DisplaySummaryKeys dk : displaySummaryKeys) {
            presentPriority = dk.getPriority();
            if ((lastPriority == -1 || lastPriority != presentPriority) && setSummaryForKeys(dk, displayValueMap, displaySummary))
                lastPriority = presentPriority;
            if (displaySummary.size() == Constants.DISPLAY_SUMMARY_SIZE) break;
        }
        return;
    }

    public boolean setSummaryForKeys(DisplaySummaryKeys dk, Map<String, DisplayValue> displayValueMap,
                                     Map<String, Object> displaySummary) {
        boolean found = false;
        boolean isFirstIteration = true;
        for (String key : dk.getKeys()) {
            if (!found && !isFirstIteration)
                break;
            if (displayValueMap.containsKey(key.toLowerCase())) {
                DisplayValue dv = displayValueMap.get(key.toLowerCase());
                String label = Objects.nonNull(dk.getLabel()) ? dk.getLabel() : dv.getLabel();
                if (displaySummary.containsKey(label)) {
                    displaySummary.put(label, String.format("%s %s", displaySummary.get(label), dv.getValue()));
                } else {
                    displaySummary.put(label, dv.getValue());
                }
                found = true;
            }
            isFirstIteration = false;
        }
        return found;
    }

    public Map<String, Object> buildTitleBar(Product product, Action action, String rechargeNumber) {
        Map<String, Object> titleBar = new HashMap<>();
        titleBar.put("title", getTitle(product, action, rechargeNumber));
        titleBar.put("subTitle", getSubTitle(product, action, rechargeNumber));

        log.debug("buildTitleBar:: titleBar {}", JsonUtil.toJson(titleBar));
        return titleBar;
    }

    public String getTitle(Product product, Action action, String rechargeNumber) {
        return product.getBrand();
    }

    public String getSubTitle(Product product, Action action, String rechargeNumber) {
        return rechargeNumber;
    }

    public String getDisplayValue(List<DisplayValue> displayValues, String label) {
        if (Objects.nonNull(displayValues)) {
            for (DisplayValue dv : displayValues) {
                if (dv.getLabel().equals(label)) {
                    return dv.getValue() + "";
                }
            }
        }
        return null;
    }

    protected boolean infoMessageEnabled() {
        return false;
    }

    protected  void extraProcessing(JSONObject errorInfo, RechargeItem item, Set<Long> productIdSet, RechargeVerifyResponse verifyResponse, String errorCode, String client, String version){
       return;}



}
