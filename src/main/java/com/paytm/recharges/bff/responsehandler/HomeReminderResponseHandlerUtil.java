package com.paytm.recharges.bff.responsehandler;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.recharges.bff.constants.Constants;
import com.paytm.recharges.bff.datalayer.cache.CVRProductCache;
import com.paytm.recharges.bff.datalayer.model.Product;
import com.paytm.recharges.bff.factory.FrequentResponseHandlerFactory;
import com.paytm.recharges.bff.service.RecentLocalisationManager;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.recharges.bff.utils.RecentUtils;
import com.paytm.recharges.bff.monitor.MetricsAgent;
import com.paytm.recharges.favourite_orders_client.datalayer.model.FrequentOrder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.paytm.recharges.bff.constants.Constants.LANGUAGE_ENGLISH;
import static com.paytm.recharges.bff.constants.RecentConstants.OPERATOR_BRAND;
import static com.paytm.recharges.bff.constants.RecentConstants.RECENTS;
import static com.paytm.recharges.bff.constants.RecentConstants.SHORT_OPERATOR_NAME;

@Service
public class HomeReminderResponseHandlerUtil {
    private static final CustomLogger log = CustomLogManager.getLogger(HomeReminderResponseHandlerUtil.class);

    @Autowired
    FrequentResponseHandlerFactory frequentResponseHandlerFactory;
    @Autowired
    RecentLocalisationManager localisationManager;
    @Autowired
    private MetricsAgent metricsAgent;

    @Value("${favourite.frequentOrder.config.priority}")
    private Integer priority;


    public Product fetchProductDetails(String pid){
        Long productId = Long.parseLong(pid);
        Product product = CVRProductCache.getInstance().getProductDetails(productId);
        String operator = null;
        Map obj = null;
        if(product==null){
            return null;
        }
        product.setServiceKey(product.getService().toLowerCase().replaceAll(" ",""));
        product.setPayTypeKey(product.getPayType().toLowerCase().replaceAll(" ",""));

        try {
            if(Objects.nonNull(product) && Objects.nonNull(product.getAttributes())) {
                obj = new ObjectMapper().convertValue(product.getAttributes(), Map.class);
            }
        } catch (NullPointerException ex) {
            log.error("error while fetching CVR attributes" + ex);
        }
        if(Objects.nonNull(obj) && Objects.nonNull(obj.get(SHORT_OPERATOR_NAME))) operator = (String) obj.get(SHORT_OPERATOR_NAME);
        else if (Objects.nonNull(obj) && Objects.nonNull(obj.get(OPERATOR_BRAND))) operator = (String) obj.get(OPERATOR_BRAND);
        else if (product.getBrand() != null) operator = product.getBrand();
        else operator = product.getOperator();
        product.setRecoOperatorName(operator);

        return modifyProduct(product);
    }

    private Product modifyProduct(Product product) {
        HashMap<String,String> payload = new HashMap<>();
        String [] key = new String[]{LANGUAGE_ENGLISH, RECENTS,"brand", product.getBrand() };
        String brand = localisationManager.getMessage(key, false, payload);
        if(!StringUtils.isEmpty(brand))
            product.setBrand(brand);
        key = new String[]{LANGUAGE_ENGLISH, RECENTS,"service_label", product.getService()};
        String prodServiceLabel = localisationManager.getMessage(key, false, payload);
        if(!StringUtils.isEmpty(prodServiceLabel))
            product.setService(prodServiceLabel);
        key = new String[]{LANGUAGE_ENGLISH, RECENTS,"paytype_label", product.getPayType()};
        String prodPaytypeLabel = localisationManager.getMessage(key, false, payload);
        if(!StringUtils.isEmpty(prodPaytypeLabel))
            product.setPayType(prodPaytypeLabel);
        key = new String[]{LANGUAGE_ENGLISH, RECENTS,"operator_label", product.getOperatorLabel()};
        String prodOperatorLabel = localisationManager.getMessage(key, false, payload);
        if(!StringUtils.isEmpty(prodOperatorLabel))
            product.setOperatorLabel(prodOperatorLabel);
        key = new String[]{LANGUAGE_ENGLISH, RECENTS,"circle_label", product.getCircle()};
        String prodCircleLabel = localisationManager.getMessage(key, false, payload);
        if(!StringUtils.isEmpty(prodCircleLabel))
            product.setCircle(prodCircleLabel);
        key = new String[]{LANGUAGE_ENGLISH, RECENTS,"service_display_label", product.getDisplayName()};
        String prodServiceDisplayLabel = localisationManager.getMessage(key, false, payload);
        if(!StringUtils.isEmpty(prodServiceDisplayLabel))
            product.setDisplayName(prodServiceDisplayLabel);
        key = new String[]{LANGUAGE_ENGLISH, RECENTS,"paytype_display_label", product.getPayTypeLabel()};
        String prodPaytypeDisplayLabel = localisationManager.getMessage(key, false, payload);
        if(!StringUtils.isEmpty(prodPaytypeDisplayLabel))
            product.setPayTypeLabel(prodPaytypeDisplayLabel);
        key = new String[]{LANGUAGE_ENGLISH, RECENTS,"operator_display_label", product.getOperatorLabel()};
        String prodOperatorDisplayLabel = localisationManager.getMessage(key, false, payload);
        if(!StringUtils.isEmpty(prodOperatorDisplayLabel))
            product.setOperatorLabel(prodOperatorDisplayLabel);

        return  product;
    }

}
