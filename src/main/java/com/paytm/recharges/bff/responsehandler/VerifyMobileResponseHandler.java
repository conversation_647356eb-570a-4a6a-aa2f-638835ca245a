package com.paytm.recharges.bff.responsehandler;

import com.paytm.recharges.bff.client.MNPClient;
import com.paytm.recharges.bff.constants.Constants;
import com.paytm.recharges.bff.datalayer.cache.CVRProductCache;
import com.paytm.recharges.bff.datalayer.dto.request.RechargeVerifyRequest;
import com.paytm.recharges.bff.datalayer.dto.response.MNPResponse;
import com.paytm.recharges.bff.datalayer.dto.response.RechargeCart;
import com.paytm.recharges.bff.datalayer.dto.response.RechargeItem;
import com.paytm.recharges.bff.datalayer.dto.response.RechargeVerifyResponse;
import com.paytm.recharges.bff.datalayer.model.Product;
import com.paytm.recharges.bff.enums.FeatureConfigName;
import com.paytm.recharges.bff.monitor.MetricsAgent;
import com.paytm.recharges.bff.service.LocalisationManager;
import com.paytm.recharges.bff.service.PlanMappingService;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import static com.paytm.recharges.bff.utils.Utils.toStartCase;

@Component
public class VerifyMobileResponseHandler  extends VerifyGenericResponseHandler  {
    private static final CustomLogger log = CustomLogManager.getLogger(VerifyMobileResponseHandler.class);

    @Autowired
    MNPClient mnpClient;

    @Value("${mnp.config.http.thread_timeout}")
    private int threadTimeout;

    @Autowired
    private ExecutorService mnpExecutor;

    @Autowired
    private MetricsAgent metricsAgent;

    @Autowired
    private PlanMappingService planMappingService;

    @Override
    public void setInfoMessage(RechargeVerifyRequest verifyRequest, RechargeVerifyResponse verifyResponse, RechargeItem item) {
        log.info("VerifyMobileResponseHandler.setInfoMessage:: Entering setInfoMessage for Mobile Handler");
        if (!infoMessageEnabled()){
            log.debug("VerifyMobileResponseHandler.setInfoMessage:: infoMessage not enabled for Mobile Handler");
            return;
        }
        Map<String, String> payload = new HashMap<>();
        if (checkForInvalidPlan(verifyResponse)) {
            setCorrectPlanAmount(item, payload);
        }
        getLatestInfoMessage(verifyRequest, verifyResponse, item, payload);
    }

    @Override
    protected boolean infoMessageEnabled() {
        return true;
    }

    public void setMnpErrorPopupInCart(RechargeCart cart, LocalisationManager localisationManager, String operator, String circle, String paytype) {
        log.info("VerifyMobileResponseHandler.setMnpErrorPopupInCart:: for customerId: " + cart.getCustomerId() + " operator: " + operator + " circle: " + circle + " paytype: " + paytype);

        Map<String,String> payload = new HashMap<>();
        payload.put(Constants.OPERATOR, operator);
        payload.put(Constants.CIRCLE, circle);
        payload.put(Constants.PAY_TYPE, paytype);
        payload.put(Constants.OPERATOR_PAYTYPE_LABEL, toStartCase(operator + " " + paytype));

        String[] key =  {Constants.LANGUAGE_ENGLISH, Constants.MNP_ERROR_POPUP, paytype, operator};
        String mnpErrorPopupMsg = localisationManager.getMessage(key, true, payload);

        log.info("VerifyMobileResponseHandler.setMnpErrorPopupInCart:: customerId: " + cart.getCustomerId() + " mnpErrorPopupMsg: " + mnpErrorPopupMsg);

        cart.setMnpErrorPopup(mnpErrorPopupMsg);
    }

    public String getOperatorSelectionMsg(RechargeCart cart, LocalisationManager localisationManager) {
        log.info("VerifyMobileResponseHandler.getOperatorSelectionMsg:: for customerId: " + cart.getCustomerId());

        String[] key =  {Constants.LANGUAGE_ENGLISH, Constants.OPERATOR_SELECTION_MSG};
        String operatorSelectionMsg = localisationManager.getMessage(key, true, new HashMap<>());

        log.info("VerifyMobileResponseHandler.getOperatorSelectionMsg:: customerId: " + cart.getCustomerId() + " operatorSelectionMsg: " + operatorSelectionMsg);

        return operatorSelectionMsg;
    }

    public String getPayType(Boolean isPostpaid) {
        if(isPostpaid == null) {
            return null;
        }

        return isPostpaid == true ? "postpaid" : "prepaid";
    }

    public boolean shouldUpdateWithMnpError(MNPResponse mnpResponse, Product product, RechargeCart cart){

        if(mnpResponse == null || product == null) {
            log.debug("VerifyMobileResponseHandler.shouldUpdateWithMnpError:: mnpResponse or product value is null for customerId: " + cart.getCustomerId());
            return false;
        }

        Map<String, String> properties = new HashMap<>();
        properties.put(mnpResponse.getOperator(), product.getOperator());
        properties.put(mnpResponse.getCircle(), product.getCircle());
        properties.put(getPayType(mnpResponse.isPostpaid()), product.getPayType());

        for (Map.Entry<String, String> entry : properties.entrySet()) {
            String mnpValue = entry.getKey();
            String productValue = entry.getValue();

            if (Objects.nonNull(mnpValue) && Objects.nonNull(productValue) && !mnpValue.equalsIgnoreCase(productValue)) {
                log.debug("VerifyMobileResponseHandler.shouldUpdateWithMnpError:: Should update mnp response as different values present for mnp and product for customerId: " + cart.getCustomerId() + " mnpValue: " + mnpValue + " productValue: " + productValue);
                return true;
            }
        }

        log.debug("VerifyMobileResponseHandler.shouldUpdateWithMnpError:: Should not set mnp response as no conditions matched for customerId: " + cart.getCustomerId());
        return false;
    }

    public void setMnpErrorOrOperatorRedirectMsg(MNPResponse mnpResponse, Product product, RechargeCart cart) {
        log.info("VerifyMobileResponseHandler.setMnpErrorOrOperatorRedirectMsg:: for customerId: " + cart.getCustomerId() + "mnpResponse: " + mnpResponse + " product: " + product + " cart: " + cart);

        if(shouldUpdateWithMnpError(mnpResponse, product, cart)){
            metricsAgent.incrementEventCount(Constants.CORRECTED_OPERATOR_DETAILS);
            setMnpErrorPopupInCart(cart, localisationManager, mnpResponse.getOperator(), mnpResponse.getCircle(), getPayType(mnpResponse.isPostpaid()));
        } else {
            cart.setRedirectToOperatorSelection(true);
            cart.setOperatorSelectionMsg(getOperatorSelectionMsg(cart, localisationManager));
        }
    }

    protected void extraProcessing(JSONObject errorInfo,RechargeItem item,Set<Long> productIdSet,RechargeVerifyResponse verifyResponse,String errorMsgCode,String client,String version){
        log.info("VerifyMobileResponseHandler.extraProcessing:: extra processing for mobile handler");
        
        boolean changeOperatorCode = errorInfo.has(Constants.IS_CHANGE_OPERATOR_CODE)&&!Objects.isNull(errorInfo.get(Constants.IS_CHANGE_OPERATOR_CODE)) && Boolean.parseBoolean(errorInfo.get(Constants.IS_CHANGE_OPERATOR_CODE).toString());
        Product product = CVRProductCache.getInstance().getProductDetails(item.getProductId());
        Map<String, String> payload = new HashMap<>();
        payload.put(Constants.OPERATOR,product.getOperatorLabel());
        payload.put(Constants.AMOUNT,item.getPrice().toString());

        if (changeOperatorCode&&rechargeHelperService.isFeatureLive(productIdSet, FeatureConfigName.wrongOperatorEnrich.name().toLowerCase(), client, version)) {
            MNPResponse response = null;
            if (Objects.nonNull(item.getConfiguration()) && Objects.nonNull(item.getConfiguration().get(Constants.REC_NUM)) && Objects.nonNull(product.getOperator())) {

                try {
                    Future<MNPResponse> futureResponse = mnpExecutor.submit(new Callable<MNPResponse>() {
                        @Override
                        public MNPResponse call() throws Exception {
                            return mnpClient.getCorrectOperatorDetailsFromOperator(item.getConfiguration().get(Constants.REC_NUM).toString(), product.getOperator());
                        }
                    });
                    response = futureResponse.get(threadTimeout, TimeUnit.MILLISECONDS);
                } catch (RejectedExecutionException ex) {
                    log.error("VerifyMobileResponseHandler.extraProcessing:: rejecting task due to non availablity of active threads ");
                } catch (ExecutionException | InterruptedException | TimeoutException e) {
                    log.error("VerifyMobileResponseHandler.extraProcessing::exception thrown while calling mnp");
                }
                catch(Exception ex){
                    log.error("VerifyMobileResponseHandler.extraProcessing:: exception while calling MNP ");

                }
            }


            if(verifyResponse != null &&  verifyResponse.getCart() != null) {
                setMnpErrorOrOperatorRedirectMsg(response, product, verifyResponse.getCart());
            }

            if(Objects.nonNull(response) && Objects.nonNull(product.getOperator()) && Objects.nonNull(response.getOperator()) && !response.getOperator().equalsIgnoreCase(product.getOperator()) && Objects.nonNull(response.getCircle()) && Objects.nonNull(product.getCircle()) && !response.getCircle().equalsIgnoreCase(product.getCircle())){
                payload.put(Constants.OPERATOR,response.getOperator());
                payload.put(Constants.CIRCLE,response.getCircle());
                payload.put(Constants.ENRICH_PAYTYPE,response.isPostpaid()?"postpaid":"prepaid");
                if(Objects.nonNull(product.getPayType())){
                    payload.put(Constants.WRONG_ENRICH_PAYTYPE,product.getPayType().toLowerCase());
                }
                payload.put(Constants.WRONG_OPERATOR,product.getOperator());
                setEnrichMessage(verifyResponse, item,Constants.VERSION_3,errorMsgCode,new VerifyGenericResponseHandler(),payload);
            }
        }
    }

    public boolean checkForInvalidPlan(RechargeVerifyResponse verifyResponse) {
        try {
            if (Objects.nonNull(verifyResponse) && Objects.nonNull(verifyResponse.getCart()) && Objects.nonNull(verifyResponse.getCart().getGwParamsToPass())) {
                Map<String, Object> gwParamsToPass = verifyResponse.getCart().getGwParamsToPass();
                String isInvalidPlan = (String) gwParamsToPass.get(Constants.IS_INVALID_PLAN);
                if (Constants.ONE.equals(isInvalidPlan)) {
                    return true;
                }
            }
        } catch (Exception ex) {
            log.error("VerifyMobileResponseHandler :: checkForInvalidPlan exception occurred", ex);
        }
        return false;
    }

    public void setCorrectPlanAmount(RechargeItem item, Map<String, String> payload) {
        Product product = CVRProductCache.getInstance().getProductDetails(item.getProductId());
        if (product == null) {
            return;
        }
        String operator = StringUtils.lowerCase(product.getOperator());
        String circle = StringUtils.lowerCase(product.getCircle());
        Double oldAmount = item.getPrice();

        Double newAmount = planMappingService.getUpdatedAmtForDiscontinuedPlan(operator, circle, oldAmount);
        if (Objects.nonNull(newAmount)) {
            payload.put(Constants.NEW_PLAN_AMOUNT, newAmount.toString());
        }
        else {
            setDefaultErrorCodeForInvalidPlan(item, payload);
        }
    }

    public void setDefaultErrorCodeForInvalidPlan(RechargeItem item, Map<String, String> payload) {
        try {
            if (Objects.nonNull(item) && Objects.nonNull(item.getMetaData())) {
                Map<String, Object> metaData = item.getMetaData();
                String defaultErrorCodeForInvalidPlan = (String) metaData.get(Constants.DEFAULT_ERROR_CODE_FOR_INVALID_PLAN);
                if (Objects.nonNull(defaultErrorCodeForInvalidPlan)) {
                    payload.put(Constants.OVERRIDE_ERROR_CODE, Constants.ONE);
                    payload.put(Constants.DEFAULT_ERROR_CODE, defaultErrorCodeForInvalidPlan);
                }
            }
        } catch (Exception ex) {
            log.error("VerifyMobileResponseHandler :: setDefaultErrorCodeForInvalidPlan exception occurred", ex);
        }
    }
}