package com.paytm.recharges.bff.responsehandler;


import com.fasterxml.jackson.core.type.TypeReference;
import com.paytm.recharges.bff.constants.CTAConstants;
import com.paytm.recharges.bff.constants.Constants;
import com.paytm.recharges.bff.datalayer.cache.CVRProductCache;
import com.paytm.recharges.bff.datalayer.dto.response.BillObject;
import com.paytm.recharges.bff.datalayer.dto.response.RecentResponse;
import com.paytm.recharges.bff.datalayer.dto.response.SagaRecentResponse;
import com.paytm.recharges.bff.datalayer.model.CTAObj;
import com.paytm.recharges.bff.datalayer.model.HeadingProperties;
import com.paytm.recharges.bff.datalayer.model.Product;
import com.paytm.recharges.bff.datalayer.model.RecentBillObject;
import com.paytm.recharges.bff.datalayer.model.RecentProduct;
import com.paytm.recharges.bff.datalayer.model.RecentWrapper;
import com.paytm.recharges.bff.enums.EventState;
import com.paytm.recharges.bff.enums.EventType;
import com.paytm.recharges.bff.monitor.MetricsAgent;
import com.paytm.recharges.bff.service.RecentLocalisationManager;
import com.paytm.recharges.bff.service.impl.CTAManagerImpl;
import com.paytm.recharges.bff.service.impl.HeadingManagerImpl;
import com.paytm.recharges.bff.service.impl.RecentLocalisationKeyResolver;
import com.paytm.recharges.bff.utils.DropOffConfigHelper;
import com.paytm.recharges.bff.utils.FavouriteUtils;
import com.paytm.recharges.bff.utils.JsonUtil;
import com.paytm.recharges.bff.utils.RecentUtils;
import com.paytm.recharges.bff.utils.Utils;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import jakarta.annotation.PostConstruct;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.paytm.recharges.bff.constants.Constants.CREDIT_CARD;
import static com.paytm.recharges.bff.constants.Constants.DATE_FORMAT;
import static com.paytm.recharges.bff.constants.Constants.DATE_FORMAT_1;
import static com.paytm.recharges.bff.constants.Constants.DATE_FORMAT_FOR_TIMESTAMP;
import static com.paytm.recharges.bff.constants.Constants.EVENT_TYPE;
import static com.paytm.recharges.bff.constants.Constants.HEADING;
import static com.paytm.recharges.bff.constants.Constants.IS_AMBIGUOUS_PID;
import static com.paytm.recharges.bff.constants.Constants.MOBILE;
import static com.paytm.recharges.bff.constants.Constants.PAY_TYPE_TEXT;
import static com.paytm.recharges.bff.constants.Constants.PREPAID;
import static com.paytm.recharges.bff.constants.Constants.PRICE;
import static com.paytm.recharges.bff.constants.Constants.PRICE_NEW;
import static com.paytm.recharges.bff.constants.Constants.RECHARGE_NUMBER;
import static com.paytm.recharges.bff.constants.Constants.RECHARGE_NUMBER_2;
import static com.paytm.recharges.bff.constants.Constants.RECHARGE_NUMBER_3;
import static com.paytm.recharges.bff.constants.Constants.RECHARGE_NUMBER_4;
import static com.paytm.recharges.bff.constants.Constants.RECHARGE_NUMBER_5;
import static com.paytm.recharges.bff.constants.Constants.RECHARGE_NUMBER_6;
import static com.paytm.recharges.bff.constants.Constants.RECHARGE_NUMBER_7;
import static com.paytm.recharges.bff.constants.Constants.RECHARGE_NUMBER_8;
import static com.paytm.recharges.bff.constants.Constants.RECO_REQUEST_IDENTIFIER;
import static com.paytm.recharges.bff.constants.Constants.SERVICE_LABEL;
import static com.paytm.recharges.bff.constants.Constants.SUBSCRIPTION_ID;
import static com.paytm.recharges.bff.constants.Constants.TRUE;
import static com.paytm.recharges.bff.constants.RecentConstants.BRAND;
import static com.paytm.recharges.bff.constants.RecentConstants.BUTTON_TEXT;
import static com.paytm.recharges.bff.constants.RecentConstants.CIRCLE_LABEL;
import static com.paytm.recharges.bff.constants.RecentConstants.COLLAPSE_TEXT;
import static com.paytm.recharges.bff.constants.RecentConstants.CTA_DATA_ORDER_ID;
import static com.paytm.recharges.bff.constants.RecentConstants.CYLINDER_AGENCY_NAME;
import static com.paytm.recharges.bff.constants.RecentConstants.DESCRIPTION;
import static com.paytm.recharges.bff.constants.RecentConstants.META_DESCRIPTION;
import static com.paytm.recharges.bff.constants.RecentConstants.META_TITLE;
import static com.paytm.recharges.bff.constants.RecentConstants.OPERATOR_DISPLAY_LABEL;
import static com.paytm.recharges.bff.constants.RecentConstants.OPERATOR_LABEL;
import static com.paytm.recharges.bff.constants.RecentConstants.PAYTYPE_DISPLAY_LABEL;
import static com.paytm.recharges.bff.constants.RecentConstants.PAYTYPE_LABEL;
import static com.paytm.recharges.bff.constants.RecentConstants.PAY_TYPE_SUPPORTED;
import static com.paytm.recharges.bff.constants.RecentConstants.PayloadRelatedConstants.DATACARD;
import static com.paytm.recharges.bff.constants.RecentConstants.SERVICE_DISPLAY_LABEL;
import static com.paytm.recharges.bff.utils.DateUtil.formatDateTimeinGMT;
import static com.paytm.recharges.bff.utils.DateUtil.stringDateFormat;
import static com.paytm.recharges.bff.utils.NumberFormatUtils.convertToStringWithRoundValue;
import static com.paytm.recharges.bff.utils.RecentUtils.gettingPrice;

@Component
public class RecentDynamicResponseBuilder {
    private static final CustomLogger log = CustomLogManager.getLogger(RecentDynamicResponseBuilder.class);
    @Value("${favourite.frequentOrder.config.prioritys}")
    private String priorityMapJson;

    @Value("${favourite.frequentOrder.config.paytypeMap}")
    private String payTypeMapJson;
    @Value("${favourite.frequentOrder.config.stateMapping}")
    private String stateMapJson;

    private Map<String, Integer> priorityMap;
    private Map<String, String> payTypeMap;
    private Map<String, String> stateMap;

    @PostConstruct
    public void init() {

        priorityMap = JsonUtil.fromJson(priorityMapJson, new TypeReference<HashMap<String, Integer>>() {
        });
        payTypeMap = JsonUtil.fromJson(payTypeMapJson, new TypeReference<HashMap<String, String>>() {
        });
        stateMap = JsonUtil.fromJson(stateMapJson, new TypeReference<HashMap<String, String>>() {
        });
    }

    @Autowired
    private RecentLocalisationManager localisationManager;
    @Autowired
    private RecentLocalisationKeyResolver recentLocalisationKeyResolver;
    @Autowired
    private HeadingManagerImpl headingManager;
    @Autowired
    private CTAManagerImpl ctaManager;
    @Autowired
    private MetricsAgent metricsAgent;

    /*
     * This method generates dynamic response for recent recharge history
     * @param recentResponse
     * @param sagaRecentResponse
     * @param recentWrapper
     * */
    public void generateDynamicResponse(RecentResponse recentResponse, SagaRecentResponse sagaRecentResponse, RecentWrapper recentWrapper, Map<String, String> allRequestParams) {

        Product product = CVRProductCache.getInstance().getProductDetails(Long.valueOf(sagaRecentResponse.getPid()));
        recentResponse.setProduct(product);
        recentResponse.setIsPaytmVPA(sagaRecentResponse.getIsPaytmVPA());
        updateRentTFData(sagaRecentResponse);
        log.debug("Setting favLabel for the recent recharge   ");
        //favLabel
        if (EventType.RECENT.equals(sagaRecentResponse.getEventType())&&Objects.nonNull(sagaRecentResponse.getRechargeNumber1()) && Objects.nonNull(gettingPrice(sagaRecentResponse))) {
            Number formattedAmount=convertToStringWithRoundValue(gettingPrice(sagaRecentResponse));
            if(Objects.nonNull(formattedAmount)){
                recentResponse.setFavLabelId(sagaRecentResponse.getRechargeNumber1().concat(":").concat(String.valueOf(formattedAmount)));
            }

        }
        recentResponse.setPriority(fetchPriority(sagaRecentResponse));
        recentResponse.setRecentUpdatedAt(sagaRecentResponse.getDate());
        if (EventType.RECENT.equals(sagaRecentResponse.getEventType()) || EventType.CSV.equals(sagaRecentResponse.getEventType())){
            if(StringUtils.equalsIgnoreCase(sagaRecentResponse.getEventState(),(EventState.OLD_BILLER.name()))){
                recentResponse.setType(stateMap.getOrDefault(StringUtils.defaultString(sagaRecentResponse.getEventState()), Constants.DEFAULT_RECENT_TYPE));
            }
            else {
                recentResponse.setType(StringUtils.defaultString(sagaRecentResponse.getEventType().name()).toLowerCase());
            }
        }
        else {
            if(EventType.SMS_CARD.equals(sagaRecentResponse.getEventType())){
                recentResponse.setType(stateMap.getOrDefault(StringUtils.defaultString(EventType.SMS_CARD.name()), Constants.SMS_CARD_TYPE));
            }else{
                recentResponse.setType(stateMap.getOrDefault(StringUtils.defaultString(sagaRecentResponse.getEventState()), Constants.DEFAULT_RECENT_TYPE));
            }
            recentResponse.setFavLabel("");
        }
        if (Constants.DEFAULT_RECENT_TYPE.equals(recentResponse.getType())) {
            List<String> tagsList = new ArrayList<>();
            tagsList.add("eventState=" + sagaRecentResponse.getEventState());
            tagsList.add("eventType=" + sagaRecentResponse.getEventType());
            metricsAgent.incrementEventCount("DEFAULT_RECENT_TYPE_USED", tagsList);
        }
        /*if(SERVICE_TUITION_FEES.equalsIgnoreCase(product.getService()) || SERVICE_RENT.equalsIgnoreCase(product.getService()) || SERVICE_BUSINESS_PAYMENT.equalsIgnoreCase(product.getService())){
            if(Objects.nonNull(sagaRecentResponse.getRechargeNumber2()) && sagaRecentResponse.getRechargeNumber2().equals(PAYTM_IFSC)){
                recentResponse.setType(PPBL_RECENT);
            }
        }*/
        recentResponse.setUpdatedAt(formatDateTimeinGMT(sagaRecentResponse.getTxnDate()));
        if (Constants.NEW_ACCOUNT_STATES_LIST.contains(sagaRecentResponse.getEventState())) {
            log.info("Adding updatedAt for NEW_ACCOUNT_STATES for rech no = {}, state = {}, rech no 7 ={}",sagaRecentResponse.getRechargeNumber1(),sagaRecentResponse.getEventState(),sagaRecentResponse.getRechargeNumber7());
            recentResponse.setUpdatedAt(formatDateTimeinGMT(sagaRecentResponse.getCreatedAt()));
        }
        if (Objects.isNull(sagaRecentResponse.getBill()) && !StringUtils.isEmpty(sagaRecentResponse.getMarkAsPaidDate())) {
            if(Objects.nonNull(sagaRecentResponse.getTxnDate())){
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                LocalDateTime txnDate=LocalDateTime.parse(sagaRecentResponse.getTxnDate(),formatter);
                LocalDateTime markAsPaidDate=LocalDateTime.parse(sagaRecentResponse.getMarkAsPaidDate(),formatter);
                if(markAsPaidDate.isAfter(txnDate)){
                    recentResponse.setUpdatedAt(formatDateTimeinGMT(sagaRecentResponse.getMarkAsPaidDate()));
                }
            }
            else {
                recentResponse.setUpdatedAt(formatDateTimeinGMT(sagaRecentResponse.getMarkAsPaidDate()));
            }
        }

        if (Objects.nonNull(sagaRecentResponse.getCylinderAgencyName()))
            setMetadata(sagaRecentResponse.getCylinderAgencyName(), recentResponse);
        if (Objects.nonNull(sagaRecentResponse.getAutomaticState()) && sagaRecentResponse.getAutomaticState() == 1)
            recentResponse.setAutomaticActive(true);
        if (Objects.nonNull(sagaRecentResponse.getAutomaticState()) && sagaRecentResponse.getAutomaticState() == 5)
            recentResponse.setRenewSubscription(true);
        recentResponse.setRecentProduct(fetchProductDetails(String.valueOf(recentResponse.getProductId()), sagaRecentResponse, recentWrapper, allRequestParams));
        if (Objects.nonNull(recentResponse.getProduct().getOpe_logo_url()))
            recentResponse.setOperatorLogoUrl(product.getOpe_logo_url());
        else if (Objects.nonNull(recentResponse.getProduct().getThumbnail()))
            recentResponse.setOperatorLogoUrl(product.getThumbnail());
        recentResponse.setConfiguration(getConfiguration(sagaRecentResponse, recentWrapper, recentResponse));
        headingManager.populateHeadings(recentResponse, recentWrapper, sagaRecentResponse);//
        recentResponse.setCtaObj(getCTAObject(sagaRecentResponse, recentResponse, recentWrapper));
        setContactHandler(recentResponse);
        recentResponse.setCta(getCta(recentResponse, sagaRecentResponse, recentWrapper));
        log.debug("Updating bills for the recent recharge");
        updateBills(recentResponse, sagaRecentResponse, allRequestParams);
        setCreatedDesc(recentResponse);
        if (EventType.RECENT.equals(sagaRecentResponse.getEventType())){
            recentResponse.setReminderNotificationEnabled(sagaRecentResponse.isReminderNotificationEnabled());
            recentResponse.setBillReminder(sagaRecentResponse.isReminderNotificationEnabled());
        }
        if (StringUtils.equalsAnyIgnoreCase(recentResponse.getProduct().getService(),"Cylinder Booking") && EventType.RECENT.equals(sagaRecentResponse.getEventType())) {
            recentResponse.setTrackDelivery(false);
        }
        recentResponse.setAllEventState(sagaRecentResponse.getEventState());
        if (!EventState.SMS_CARD_TXN.name().equals(sagaRecentResponse.getEventState())){
            recentResponse.setEventState(null);
        }
        if(EventState.PARTIAL_BILL.name().equals(sagaRecentResponse.getEventState())){
            recentResponse.setIsPartialBill(true);
        }
        //
        recentResponse.setCustomerName(RecentUtils.getCustomerNameFromSagaResponse(sagaRecentResponse));
        recentResponse.setRechargeNumberForDisplay(sagaRecentResponse.getRechargeNumber1());
    }

    private String updateRecentConsentValue(String consent) {
        if (StringUtils.isEmpty(consent)|| consent.endsWith("_DEFAULT")) {
            return "DEFAULT";
        } else if (consent.endsWith("_YES")) {
            return "YES";
        } else if (consent.endsWith("_NO")) {
            return "NO";
        } else if (consent.endsWith("_SENT")) {
            return "SENT";
        } else {
            return "DEFAULT";
        }
    }

    public void updateRentTFData(SagaRecentResponse sagaRecentResponse){
        if(Objects.isNull(sagaRecentResponse.getRentTfData())){
            sagaRecentResponse.setRentTfData(new HashMap<String,Object>());
        }
        HashMap<String,Object> rentTfData = (HashMap<String,Object>) sagaRecentResponse.getRentTfData();
        if(Objects.nonNull(rentTfData)){
            rentTfData.put("consent",updateRecentConsentValue(sagaRecentResponse.getRentConsent()));
            //rentTfData.put("consentMessage",updateRecentConsentValue(sagaRecentResponse.getRentConsent()));
            //sagaRecentResponse.setRentTfData(rentTfData);
        }
    }

    private Product setProductAttributesWhenAmbiguous(Product product, Map<String,Object> ambiguous){
        Product productCopy = new Product();
        BeanUtils.copyProperties(product, productCopy);
        if (product.getAttributes() != null) {
            Map<String, Object> currentattributesMap = (Map<String, Object>) product.getAttributes();
            Map<String, Object> attributesMap = new HashMap<>(currentattributesMap);

            String[] groupingKeys = {"state", "board", "district", "sub_district", "sub_division"};

            for (String key : groupingKeys) {
                if (ambiguous.containsKey(key)) {
                    Object ambiguousValue = ambiguous.get(key);
                    if (ambiguousValue instanceof Boolean && !(Boolean) ambiguousValue) {
                        attributesMap.put(key, "N/A");
                    } else if (ambiguousValue instanceof String) {
                        attributesMap.put(key, ambiguousValue);
                    }
                } else {
                    attributesMap.put(key, "N/A");
                }
            }

            if(!ambiguous.containsKey("productId")){
                attributesMap.remove("productId");
                attributesMap.put(IS_AMBIGUOUS_PID,true);
                productCopy.setProductId(null);
            }

            productCopy.setAttributes(attributesMap);
        }
        return productCopy;
    }


    private void setMetadata(String cylinderAgencyName, RecentResponse recentResponse) {
        Map<String, Object> metaMap = new HashMap<>();
        metaMap.put(CYLINDER_AGENCY_NAME, cylinderAgencyName);
        recentResponse.setMetadata(metaMap);
    }

    private void setContactHandler(RecentResponse recentResponse) {
        Map<String, Object> contactHeadings = new HashMap<>();
        if (StringUtils.equalsAnyIgnoreCase(recentResponse.getProduct().getService(), MOBILE, DATACARD)) {
            HeadingProperties heading = recentResponse.getCtaObj().getHeading2();
            contactHeadings.put(HEADING, heading);
        }
        recentResponse.getCtaObj().setContact(contactHeadings);
    }

    private void setCreatedDesc(RecentResponse recentResponse) {
        HeadingProperties heading = recentResponse.getCtaObj().getHeading2();
        if (Objects.nonNull(heading)) recentResponse.setCreated_desc(StringUtils.defaultString(heading.getLabel()));
    }

    private Map<String, Object> getCta(RecentResponse recentResponse, SagaRecentResponse sagaRecentResponse, RecentWrapper recentWrapper) {
        if (!StringUtils.equalsAnyIgnoreCase(recentResponse.getProduct().getPayType(), CREDIT_CARD) || DropOffConfigHelper.liveOnVersionBasedOnCatId(recentWrapper.getClient(), recentWrapper.getVersion(), String.valueOf(recentResponse.getProduct().getCategoryId()))) {
            return new HashMap<>();
        }
        Map<String, Object> cta = new HashMap<>();
        HeadingProperties heading2 = recentResponse.getCtaObj().getHeading2();
        if (Objects.nonNull(heading2)) {
            cta.put("message_color", heading2.getColor());
            cta.put("message", heading2.getLabel());
        }

        if (Objects.nonNull(sagaRecentResponse.getBill())) {
            cta.put(COLLAPSE_TEXT, "show_bill_details");
            cta.put(BUTTON_TEXT, "pay_bill");
        } else {
            cta.put(COLLAPSE_TEXT, "show_payment_detail");
            cta.put(BUTTON_TEXT, "pay_more");
        }
        cta.put(CTA_DATA_ORDER_ID, sagaRecentResponse.getOrderId());
        return cta;
    }

    private Integer fetchPriority(SagaRecentResponse sagaRecentResponse) {
        Integer priority = 0;
        if (EventType.RECENT.equals(sagaRecentResponse.getEventType())) {
            log.debug("Setting priority {} {}", priorityMap.get(sagaRecentResponse.getEventType().name()), (sagaRecentResponse.getEventType().name()));
            return priorityMap.get(sagaRecentResponse.getEventType().name());
        } else priority = priorityMap.get(sagaRecentResponse.getEventState());
        log.debug("Setting priority {} {}", sagaRecentResponse, priority);
        return priority;
    }

    private CTAObj getCTAObject(SagaRecentResponse sagaRecentResponse, RecentResponse recent, RecentWrapper recentWrapper) {
        log.debug("Setting ctaObject for the recent recharge");
        CTAObj ctaObject = recent.getCtaObj();
        // Adding logo URL to the ctaObject map
        ctaObject.setLogo(recent.getOperatorLogoUrl());
        recentWrapper.setLocalisationKeyConstant(CTAConstants.Constants.CTA_TO_SHOW);
        ctaObject.setCta(ctaManager.createCTA(recentWrapper, sagaRecentResponse, recent.getProduct()));
        // Returning the filled ctaObject map
        return ctaObject;
    }

    private RecentProduct fetchProductDetails(String pid, SagaRecentResponse sagaRecentResponse, RecentWrapper recentWrapper, Map<String, String> allRequestParams) {
        log.debug("Updating product details for the recent recharge");
        RecentProduct recentProduct = new RecentProduct();
        Long productId = Long.parseLong(pid);
        Product product = CVRProductCache.getInstance().getProductDetails(productId);
        if (product == null) {
            return null;
        }
        if(sagaRecentResponse.getAmbiguous() != null && !sagaRecentResponse.getAmbiguous().isEmpty()){
            product = setProductAttributesWhenAmbiguous(product,sagaRecentResponse.getAmbiguous());
        }
        product.setServiceKey(StringUtils.lowerCase(product.getService()).replace(" ", ""));
        product.setPayTypeKey(StringUtils.lowerCase(product.getPayType()).replace(" ", ""));
        return modifyProduct(product, recentWrapper, recentProduct, allRequestParams);
    }


    private RecentProduct modifyProduct(Product product, RecentWrapper recentWrapper, RecentProduct recentProduct, Map<String, String> allRequestParams) {
        Map<String, Object> attr = Utils.filterProductAttributes(product, allRequestParams);
        if(Objects.nonNull(product.getProductId())&&product.getProductId().equals(971L)){
            log.info("modifyProduct::attr are {}",attr);
        }
        if (StringUtils.isNotBlank(getLocalizationOrDefaultLabel(recentWrapper, BRAND, product.getBrand(), null)))
            recentProduct.setBrand(getLocalizationOrDefaultLabel(recentWrapper, BRAND, product.getBrand(), null));
        else recentProduct.setBrand(product.getBrand());
        attr.put(SERVICE_LABEL, StringUtils.defaultString(getLocalizationOrDefaultLabel(recentWrapper, SERVICE_LABEL, product.getService(), attr)));
        attr.put(PAYTYPE_LABEL, StringUtils.defaultString(getLocalizationOrDefaultLabel(recentWrapper, PAYTYPE_LABEL, product.getPayType(), attr)));
        attr.put(OPERATOR_LABEL, StringUtils.defaultString(getLocalizationOrDefaultLabel(recentWrapper, OPERATOR_LABEL, product.getOperatorLabel(), attr)));
        attr.put(CIRCLE_LABEL, StringUtils.defaultString(getLocalizationOrDefaultLabel(recentWrapper, CIRCLE_LABEL, product.getCircle(), attr)));
        attr.put(SERVICE_DISPLAY_LABEL, StringUtils.defaultString(getLocalizationOrDefaultLabel(recentWrapper, SERVICE_DISPLAY_LABEL, product.getDisplayName(), attr)));
        attr.put(PAYTYPE_DISPLAY_LABEL, StringUtils.defaultString(getLocalizationOrDefaultLabel(recentWrapper, PAYTYPE_DISPLAY_LABEL, product.getPayTypeLabel(), attr)));
        attr.put(OPERATOR_DISPLAY_LABEL, StringUtils.defaultString(getLocalizationOrDefaultLabel(recentWrapper, OPERATOR_DISPLAY_LABEL, product.getOperatorLabel(), attr)));
        attr.remove(META_DESCRIPTION);
        attr.remove(DESCRIPTION);
        attr.remove(META_TITLE);
        attr.remove(PAY_TYPE_SUPPORTED);


        recentProduct.setOpe_logo_url(product.getThumbnail());
        recentProduct.setAttributes(attr);
        recentProduct.setCategoryId(product.getCategoryId());
        Integer schedulable= FavouriteUtils.fetchSchedulable(product);
        recentProduct.setSchedulable(schedulable);
        if (Objects.isNull(product.getCategoryId())) recentProduct.setCategoryId(0L);
//        if (Objects.isNull(product.getSchedulable())) recentProduct.setSchedulable(0L);
        return recentProduct;
    }

    private String getLocalizationOrDefaultLabel(RecentWrapper recentWrapper, String constant, String label, Map<String, Object> attr) {
        String localizedMessage = recentLocalisationKeyResolver.getLocalizationValue(recentWrapper, constant, label);
        String constantLabel = "";

        if (!StringUtils.isEmpty(localizedMessage)) {
            log.debug(" localised  label set for {} : {}", constant, localizedMessage);
            return localizedMessage;
        }
        if (Objects.nonNull(attr)) {
            constantLabel = (String) attr.get(constant);
        }
        log.debug(" label set for {} : {}", constant, constantLabel);

        return constantLabel;
    }

    private void settingRechargeNumberToConfiguration(SagaRecentResponse sagaRecentResponse, Map<String, Object> conf) {
        Product product=CVRProductCache.getInstance().getProductDetails(Long.valueOf(sagaRecentResponse.getPid()));
        if(Objects.nonNull(product)&&Objects.nonNull(product.getService())&&Constants.SERVICE_TUITION_FEES.equalsIgnoreCase(product.getService())&&
                StringUtils.isEmpty(sagaRecentResponse.getRechargeNumber3()) ){
            log.info("settingRechargeNumberToConfiguration :: sagaRecentResponse {} {}",sagaRecentResponse.getCustomerId(),sagaRecentResponse.getRechargeNumber1());
            metricsAgent.incrementEventCount("tuition_fee_recharge_number_null");

        }

        if (Objects.nonNull(sagaRecentResponse.getRechargeNumber1())) {
            conf.put(RECHARGE_NUMBER, sagaRecentResponse.getRechargeNumber1());
        } else {
            conf.put(RECHARGE_NUMBER, "");
        }
        if (Objects.nonNull(sagaRecentResponse.getRechargeNumber2())) {
            conf.put(RECHARGE_NUMBER_2, sagaRecentResponse.getRechargeNumber2());
        }
        if (Objects.nonNull(sagaRecentResponse.getRechargeNumber3())) {
            conf.put(RECHARGE_NUMBER_3, sagaRecentResponse.getRechargeNumber3());
        }
        if (Objects.nonNull(sagaRecentResponse.getRechargeNumber4())) {
            conf.put(RECHARGE_NUMBER_4, sagaRecentResponse.getRechargeNumber4());
        }
        if(Objects.nonNull(sagaRecentResponse.getRechargeNumber5())){
            conf.put(RECHARGE_NUMBER_5,sagaRecentResponse.getRechargeNumber5());
        }
        if(Objects.nonNull(sagaRecentResponse.getRechargeNumber6())){
            conf.put(RECHARGE_NUMBER_6,sagaRecentResponse.getRechargeNumber6());
        }
        if(Objects.nonNull(sagaRecentResponse.getRechargeNumber7())){
            conf.put(RECHARGE_NUMBER_7,sagaRecentResponse.getRechargeNumber7());
        }
        if(Objects.nonNull(sagaRecentResponse.getRechargeNumber8())){
            conf.put(RECHARGE_NUMBER_8,sagaRecentResponse.getRechargeNumber8());
        }
    }

    private void settingPriceForDropOff(SagaRecentResponse sagaRecentResponse, Map<String, Object> conf) {
        conf.put(PRICE, convertToStringWithRoundValue(gettingPrice(sagaRecentResponse)));
    }

    private void settingPriceForRecents(SagaRecentResponse sagaRecentResponse, Map<String, Object> conf, RecentWrapper recentWrapper, RecentResponse recentResponse) {
        conf.put(PRICE, convertToStringWithRoundValue(gettingPrice(sagaRecentResponse)));
        if (!StringUtils.isEmpty(getLocalizationOrDefaultLabel(recentWrapper, PAY_TYPE_TEXT, recentResponse.getProduct().getPayType(), null))) {
            conf.put(PRICE_NEW, getLocalizationOrDefaultLabel(recentWrapper, PAY_TYPE_TEXT, recentResponse.getProduct().getPayType(), null));
            log.debug("Indide if -{}-",conf.get(PRICE_NEW));
        } else {

            conf.put(PRICE_NEW, Objects.nonNull(payTypeMap.get(recentResponse.getProduct().getPayTypeKey())) ? payTypeMap.get(recentResponse.getProduct().getPayTypeKey()) : gettingPrice(sagaRecentResponse));
            log.debug("Iside else -{}-",conf.get(PRICE_NEW));
        }
    }

    private void settingEventTypeToConfiguration(SagaRecentResponse sagaRecentResponse, Map<String, Object> conf) {
        if (Objects.nonNull(sagaRecentResponse.getEventType())) {
            conf.put(EVENT_TYPE, sagaRecentResponse.getEventType());
        }
    }

    private void settingSubscriptionIdToConfiguration(SagaRecentResponse sagaRecentResponse, Map<String, Object> conf) {
        if (Objects.nonNull(sagaRecentResponse.getAutomaticSubscriptionId())) {
            conf.put(SUBSCRIPTION_ID, sagaRecentResponse.getAutomaticSubscriptionId());
        }
    }

    private Map<String, Object> getConfiguration(SagaRecentResponse sagaRecentResponse, RecentWrapper recentWrapper, RecentResponse recentResponse) {
        log.debug("Setting configuration for the recent recharge");
        Map<String, Object> conf = new HashMap<>();
        settingRechargeNumberToConfiguration(sagaRecentResponse, conf);
        settingEventTypeToConfiguration(sagaRecentResponse, conf);
        settingSubscriptionIdToConfiguration(sagaRecentResponse, conf);
        if (!EventType.RECENT.equals(sagaRecentResponse.getEventType())) {
            settingPriceForDropOff(sagaRecentResponse, conf);
            return conf;
        }

        settingPriceForRecents(sagaRecentResponse, conf, recentWrapper, recentResponse);

        return conf;
    }

    /*
     * This method updates the bill data for the recent recharge
     * @param recentResponse
     * @param sagaRecentResponse
     * @param recentWrapper
     * */
    public void updateBills(RecentResponse recentResponse, SagaRecentResponse sagaRecentResponse, Map<String, String> allRequestParams) {
        log.debug("Fetching bill object for the recent recharge");
        recentResponse.setBills(getBillObject(sagaRecentResponse, recentResponse, allRequestParams));
    }

    private ArrayList<RecentBillObject> getBillObject(SagaRecentResponse sagaRecentResponse, RecentResponse recent, Map<String, String> allRequestParams) {
        HeadingProperties heading = recent.getCtaObj().getHeading2();

        ArrayList<RecentBillObject> baseBillObjects = new ArrayList<>();

        if (Objects.nonNull(sagaRecentResponse.getBill()) && !sagaRecentResponse.getEventType().equals(EventType.RECENT)) {
            RecentBillObject baseBillObject = prepareBillObject(sagaRecentResponse, recent, allRequestParams);
            baseBillObjects.add(baseBillObject);
        } else if (Objects.nonNull(heading) && Objects.nonNull(sagaRecentResponse.getBill())) {

            RecentBillObject recentBillObject = prepareBillObject(sagaRecentResponse, recent, allRequestParams);
            recentBillObject.setBillsLabelColor(heading.getColor());
            recentBillObject.setContactScreenBillsLabelColor(heading.getColor());
            recentBillObject.setCtaLabel(heading.getLabel());
            recentBillObject.setContactScreenCtaLabel(heading.getLabel());
            recentBillObject.setLabel(heading.getLabel());
            baseBillObjects.add(recentBillObject);
        }
        return baseBillObjects;
    }

    private RecentBillObject prepareBillObject(SagaRecentResponse sagaRecentResponse, RecentResponse recent, Map<String, String> allRequestParams) {
        RecentBillObject baseBillObject = new RecentBillObject();
        BillObject billObject = sagaRecentResponse.getBill();
        BeanUtils.copyProperties(billObject, baseBillObject);
        baseBillObject.setAmount(billObject.getBillAmount());
        if(Objects.nonNull(recent.getProduct().getPayType())&&Utils.toLowerCaseAndRemoveSpace(recent.getProduct().getPayType()).equalsIgnoreCase("creditcard") && ! EventType.RECENT.equals(sagaRecentResponse.getEventType())){
            baseBillObject.setOrderId(sagaRecentResponse.getOrderId());
        }

        if (Objects.nonNull(billObject.getOriginal_min_due_amount())) {
            baseBillObject.setOriginalMinDueAmount(billObject.getOriginal_min_due_amount().toString());
        }

        if (Objects.nonNull(billObject.getMin_due_amount())) {
            baseBillObject.setMinDueAmount(billObject.getMin_due_amount().toString());
        }
        if (Objects.nonNull(billObject.getCurrentOutstandingAmount())) {
            baseBillObject.setCurrentOutstandingAmount(billObject.getCurrentOutstandingAmount().toString());
        }

        if (Objects.nonNull(billObject.getOriginal_due_amount())) {
            baseBillObject.setOriginalDueAmount(billObject.getOriginal_due_amount().toString());
        }
        baseBillObject.setBillDate(stringDateFormat(billObject.getBillDate(),DATE_FORMAT_FOR_TIMESTAMP,DATE_FORMAT_1));

        log.debug("bill object is {}", sagaRecentResponse);
        log.debug("baseBillObject object is {}", baseBillObject);

        if (Objects.nonNull(billObject.getMarkedAsPaid()) && Boolean.TRUE.equals(billObject.getMarkedAsPaid()) && !StringUtils.isEmpty(sagaRecentResponse.getMarkAsPaidDate())) {
            baseBillObject.setMarkAsPaidTime(stringDateFormat(sagaRecentResponse.getMarkAsPaidDate(),DATE_FORMAT_FOR_TIMESTAMP,DATE_FORMAT));
        }


        if (recent.getProduct().getPayType().equalsIgnoreCase(PREPAID)) {
            baseBillObject.setExpiry(stringDateFormat(billObject.getBillDueDate(),DATE_FORMAT_FOR_TIMESTAMP,DATE_FORMAT_1));
            baseBillObject.setPlanBucket(billObject.getPlan_bucket());
        } else {
            if (Objects.nonNull(sagaRecentResponse.getEventState()) && ((sagaRecentResponse.getEventState().equals(EventState.OLD_BILL_PENALTY.toString()) || sagaRecentResponse.getEventState().equals(EventState.OLD_BILL_AVOID_DISCONNECTION.toString())) && Objects.nonNull(allRequestParams.get(RECO_REQUEST_IDENTIFIER)) && TRUE.equalsIgnoreCase(allRequestParams.get(RECO_REQUEST_IDENTIFIER)))) {
                baseBillObject.setDueDate(null);
            } else {
                baseBillObject.setDueDate(stringDateFormat(billObject.getBillDueDate(), DATE_FORMAT_FOR_TIMESTAMP, DATE_FORMAT_1));
            }
        }

        log.debug("BillObject : {}", baseBillObject);

        return baseBillObject;
    }
}
