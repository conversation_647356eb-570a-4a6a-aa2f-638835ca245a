package com.paytm.recharges.bff.responsehandler;


import com.paytm.recharges.bff.datalayer.dto.response.RecentResponse;
import com.paytm.recharges.bff.datalayer.dto.response.SagaRecentResponse;
import com.paytm.recharges.bff.datalayer.model.RecentWrapper;
import com.paytm.recharges.bff.enums.Widget;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.paytm.recharges.bff.constants.Constants.CLIENT;
import static com.paytm.recharges.bff.constants.Constants.LOCALE;
import static com.paytm.recharges.bff.constants.Constants.VERSION;
import static com.paytm.recharges.bff.utils.RecentUtils.getLocalizationPayload;

@Component
public class RecentResponseWorker {
    private static final CustomLogger log = CustomLogManager.getLogger(RecentResponseWorker.class);


    @Autowired
    private RecentResponseBuilder recentResponseBuilder;
    @Autowired
    private RecentDynamicResponseBuilder recentDynamicResponseBuilder;


    public RecentResponse build(SagaRecentResponse sagaRecentResponse, Map<String, String> allRequestParams, Widget widget)  {
            try {
                RecentWrapper recentWrapper = new RecentWrapper();
                recentWrapper.setWidget(widget);
                setRecentWrapperInfo(allRequestParams, recentWrapper, sagaRecentResponse);
                RecentResponse recentResponse = recentResponseBuilder.build(sagaRecentResponse);
                Map<String, Object> metaData=recentResponse.getMetadata();
                if(Objects.nonNull(sagaRecentResponse.getEventState())
                        && sagaRecentResponse.getEventState().startsWith("DATA_EXHAUST_")){
                    if(Objects.isNull(metaData)) {
                        metaData=new HashMap<>();
                    }
                    metaData.put("is_data_exhaust",true);
                }
                recentResponse.setMetadata(metaData);
                recentDynamicResponseBuilder.generateDynamicResponse(recentResponse, sagaRecentResponse, recentWrapper, allRequestParams);
                return recentResponse;
            } catch (Exception ex) {
                log.info("exception in RecentResponseWorker build sagaRecentResponse {} ex is {}", sagaRecentResponse, ex);
                throw ex;
            }

    }


    private void setRecentWrapperInfo(Map<String, String> allRequestParams, RecentWrapper recentWrapper, SagaRecentResponse sagaRecentResponse) {
        recentWrapper.setClient(allRequestParams.get(CLIENT));
        recentWrapper.setVersion(allRequestParams.get(VERSION));
        recentWrapper.setLocale(allRequestParams.get(LOCALE));
        recentWrapper.setPayload(getLocalizationPayload(recentWrapper, sagaRecentResponse));
    }
}
