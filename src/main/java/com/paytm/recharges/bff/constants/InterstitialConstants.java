package com.paytm.recharges.bff.constants;

public class InterstitialConstants {
    // Existing constants
    public static final String SHOW_POPUP = "show_popup";
    public static final String DISPLAY_API = "display_api";
    public static final String BILL_AMOUNT = "bill_amount";
    public static final String BILL_DUE = "bill_due";
    public static final String INTERSTITIAL_DISPLAY_FLAG = "enable_interstitial";
    public static final String INTERSTITIAL_DISPLAY_SERVICE = "interstitial_display_service";
    public static final String INTERSTITIAL_DISPLAY_PER_BILL = "interstitial_display_per_bill";
    public static final String INTERSTITIAL_DISPLAY_PER_DAY = "interstitial_display_per_day";
    public static final String INTERSTITIAL_DISPLAY_PER_MONTH = "interstitial_display_per_month";
    public static final String INTERSTITIAL_THREAD_TIMEOUT = "interstitial_thread_timeout";
    public static final String INTERSTITIAL_DISPLAY_COOL_OFF = "interstitial_display_cool_off";
    public static final String INTERSTITIAL_BILLKEY_COOL_OFF_SUFFIX = "_cooloff";
    public static final String INTERSTITIAL_BATCH_TIMEOUT_IN_MS = "interstitial_batch_timeout_in_ms";
    public static final String INTERSTITIAL_LOCALISATION_DUE_DATE_KEY = "_impression";
    
    // Request parameter constants
    public static final String PARAM_RECHARGE_NUMBER = "rechargeNumber";
    public static final String PARAM_OPERATOR = "operator";
    public static final String PARAM_CUSTOMER_ID = "customerId";
    public static final String PARAM_PAY_TYPE = "payType";
    public static final String PARAM_SERVICE = "service";
    public static final String PARAM_PRODUCT_ID = "productId";
    public static final String PARAM_PLAN_BUCKET = "planBucket";
    public static final String PARAM_AMOUNT = "amount";
    public static final String PARAM_CIRCLE = "circle";
    public static final String PARAM_EXPIRY = "expiry";
    public static final String PARAM_DUE_DATE = "dueDate";
    public static final String PARAM_REFERENCE_ID = "referenceId";
    public static final String PARAM_RETRY_COUNT = "retry_count";
}
