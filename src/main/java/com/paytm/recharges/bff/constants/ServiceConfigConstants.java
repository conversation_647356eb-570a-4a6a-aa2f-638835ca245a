package com.paytm.recharges.bff.constants;

public class ServiceConfigConstants {
    public static final String ENABLE_SMS_CARD_IN_RECENT = "enableSMSCardInRecent";
    public static final String CIR_BANK_NAME_MAPPING = "CIRBankNameMapping";
    public static final String CIR_FLOW_TYPE = "cirFlowType";
    public static final String CIR_CARD_LIMIT = "cirCardLimit";
    public static final String RULES_CONFIG = "rules_config";
    public static final String NEW_BILL_TAG_FOR_ALL =  "newBillTagForAll";
    public static final String FUZZY_MATCH_CUTOFF = "fuzzyMatchCutOff";
    public static final String VRN_TTL_NOT_IN_TOP_BANK = "vrnTTLNotInTopBank";
    public static final String FASTAG_TOP_PID_LIST = "fastagTopPidList";
    public static final String FASTAG_CORE_POOL_SIZE = "fastagCorePoolSize";
    public static final String FASTAG_MAX_POOL_SIZE = "fastagMaxPoolSize";
    public static final String RECO_DUE_DATE_PRIORITY = "recoDueDatePriority";
    public static final String RECO_PRIORITY_ENABLED_LIST = "recoPriorityEnabledList";
    public static final String RECO_PRIORITY_ENABLED = "recoPriorityEnabled";
    public static final String RECO_PRIORITY_ENABLED_CUST_LIST = "recoPriorityEnabledCustList";
    public static final String CATEGORY_WISE_SUBSCRIPTION_MAX_AMOUNT = "categoryWiseSubscriptionMaxAmount";
    public static final String ENABLE_HR_ENCRYPTION_KEY = "enable_hr_encryption";
    public static final String CUSTOMER_ID_ROLLOUT_PERCENTAGE = "customerIdRolloutPercentage";
}
