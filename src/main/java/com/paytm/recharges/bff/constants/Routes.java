package com.paytm.recharges.bff.constants;


public class Routes {

    public static final String FAVOURITE_BASE_URL = "/favourite";
    public static final String FREQUENT_ORDER_URL = "{channelId}/v2/frequentOrders";
    public static final String FREQUENT_ORDER_URL_HR = "/v1/homepage/reminders";
    public static final String UPDATE_RECENT_URL = "{channelId}/v1/updateNickname";
    public static final String VALIDATE_URL = "v1/recharge/validate";
    public static final String REMINDER_BASE_URL = "/v2/bill";
    public static final String REMINDER_STATUS_URL = "/notificationStatus";
    public static final String FREQUENT_ORDER_URL_V3 = "{channelId}/v3/frequentOrders";
    public static final String FREQUENT_ORDER_URL_V5 = "{channelId}/v5/frequentOrders";
    public static final String FREQUENT_ORDER_URL_V4 = "{channelId}/v4/frequentOrders/{category}";
    public static final String INTERSTITIAL_URL = "/v1/homepage/interstitial";
    public static final String INTERSTITIAL_URL_RETRY = "/v1/homepage/interstitial/retry";
    public static final String METRIC_BASE_URL = "/metrics";
    public static final String METRIC_DATA_URL = "/getMetrics";
    public static final String EVICT_CACHE_URL = "/evictcache";
    public static final String EVICT_OR_CACHE_URL = "/evictOrCache";

    public static final String UPSERT_ORDER_COUNTS = "/upsertOrderCounts";
    public static final String DISMISS_ACTION="/v1/dismissaction/{action}";
    public static final String FREQUENT_ORDER_URL_V1 = "/user/favourite/frequentorders";
    public static final String SHORTEN_API = "/v1/getSmartLink";
    public static final String GET_CIR_URL = "/getCIR";
    public static final String DELETE_CIR_URL = "/deleteCIR";
    public static final String FETCH_RECENTS = "fetchRecents";
    public static final String FETCH_V2_RECENTS = "v2/fetchRecents";

    public static final String REMOVE_PG_TOKEN = "/pg/removeToken";

    public static final String CC_WIDGET = "v1/getCcWidget";

    public static final String FASTAG_CACHE_REFRESH_URL = "/fastagRefreshCache";
    public static final String REMIND_ME_LATER = "v1/setRemindLater";
    public static final String REMIND_ME_LATER_V2 = "v2/setRemindLater";
    public static final String CREATE_OR_UPDATE_BULK_CONSENT_V1 = "v1/createOrUpdateBulkConsent";

}
