package com.paytm.recharges.bff.constants;

import com.paytm.recharges.bff.enums.EventState;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class Constants {


    // reminder dropoff
    public static final String USER_ID_HEADER = "x-user-id";
    public static final String ERROR_LOCATION_BODY = "body";
    public static final String ERROR_MSG = "Invalid value";
    public static final String INVALID_PARAM_OPERATOR = "operator";
    public static final String INVALID_PARAM_RECHARGE_NUMBER = "rechargeNumber";
    public static final String INVALID_PARAM_PRODUCTID = "productId";
    public static final String INVALID_PARAM_BODY = "request body";
    public static final String INVALID_PARAM_NOTIFICATION_STATUS = "notificationStatus";
    public static final String INVALID_PARAM_REFERENCE_ID = "referenceId";
    public static final String JWT_CUSTOMERID = "customerid";
    // Metrics Agent stats count
    public static final String API_STATS_COUNT = "api:";
    public static final String DELETE_CIR_API = "DeleteCIRFromSagaAPI_Checkouts";
    public static final String GET_BANKNAMES_API = "getBankNamesFromSagaAPI_Checkouts";
    public static final String EVENT_STATS_COUNT = "event:";
    public static final String X_CLIENT_ID = "X-CLIENT-ID";
    public static final String X_REQUEST_ID = "X-REQUEST-ID";
    //    public static final String CIPHER_ALGORITHM = "AES";
    public static final String CIPHER_PADDING = "AES/CBC/PKCS5PADDING";
    public static final String CUSTOMER_ID = "x-user-id";
    public static final String VERTICAL_ID = "verticalId";
    public static final String MERCHANT_ID = "merchantId";
    public static final String RECHARGE_NUMBER = "recharge_number";
    public static final String FASTAG_ISSUING_BANK = "fastag_issuing_bank";
    //public static final String RECHARGE_NUMBER_1 = "recharge_number_1";
    public static final String RECHARGE_NUMBER_2 = "recharge_number_2";
    public static final String RECHARGE_NUMBER_3 = "recharge_number_3";
    public static final String RECHARGE_NUMBER_4 = "recharge_number_4";
    public static final String OPERATOR = "operator";
    public static final String FETCH_OPERATOR_FLAG = "fetchOperatorFlag";
    public static final String CUSTOMER_ID_METRIC_TAG = "customer_id:";
    public static final String CHECKOUT_THRESHOLD_METRIC = "checkout_threshold_breach";

    public static final String RECHARGE_NUMBER_5="recharge_number_5";
    public static final String RECHARGE_NUMBER_6="recharge_number_6";
    public static final String RECHARGE_NUMBER_7="recharge_number_7";
    public static final String RECHARGE_NUMBER_8="recharge_number_8";
    public static final String EVENT_TYPE = "event_type";
    public static final String SUBSCRIPTION_ID = "subscription_id";
    public static final String LANGUAGE_ENGLISH = "en-IN";
    public static final String DEFAULT_PREFIX = "default_";
    public static final String PAY_TYPE_TEXT = "paytypeText";
    public static final String PRICE_NEW = "price_new";

    public static final String MOBILE_POSTAPID = "postpaid";
    public static final String MOBILE_PREPAID = "prepaid";
    public static final String TRACKING_DETAILS = "tracking_details";
    public static final String JWT_TOKEN_PREFIX = "Bearer ";
    public static final String JWT_TIME_KEY = "ts";
    public static final Integer JWT_TTL = 300000;
    public static final Integer JWT_ALLOWED_DIFF = -1000;

    public static final String JWT_ALGO_KEY = "alg";
    public static final String JWT_ALGO = "HS256";
    public static final String JWT_TYPE_KEY = "typ";
    public static final String JWT_TYPE = "JWT";
    public static final String BOOKING_FLOW = "booking_flow";
    public static final String COLOR = "color";
    public static final String BOOKING_MESSAGE = "booking_msg";
    public static final String AGENCY_CONTACT_MESSAGE = "agency_contact_msg";
    public static final String CONNECTION_TIMEOUT = "connectionTimeout";
    public static final String FREQUENT_ORDERS_URL = "frequentOrdersUrl";
    public static final Integer PAY_FOR_IVRS_BOOKING = 10;
    public static final String ACTION_CTA = "action_cta";
    public static final String SAGA_CUSTOMER_ID = "{customer_id}";
    public static final String SERVICE = "service";
    public static final String CONTACT = "contact";
    public static final String FINANCIAL_SERVICES = "financial services";
    public static final String FASTAG = "fastag recharge";
    public static final String ONLY_REMINDER = "onlyreminder";
    public static final String EXCLUDE_DROPOFF = "excludedropoff";
    public static final String BANNER_KEY = "bannerKey";
    public static final String RECO_UNIQUEID_KEY = "reco_unique_key";
    public static final String RECCO = "Recco";
    public static final String FREQUENT_ORDER_ERROR = "frequent_order_error";
    public static final String MARKET_FAV_FREQUENT_ORDER_SIZE = "market_fav_frequent_order_size";

    public static final String HOMEPAGE_PERSONALISE_VIEW_IDS = "HOMEPAGE_PERSONALISE_VIEW_IDS";

    public static final String MARKET_FAV_FREQUENT_ORDER_API = "market_fav_frequent_order_api";

    public static final String HOME_REMINDER_RESPONSE_COUNT = "HOME_REMINDER_RESPONSE_COUNT";

	public static final String MNP_FETCH_OPERATOR_DETAILS = "MNP_FETCH_OPERATOR_DETAILS";
    public static final String CORRECTED_OPERATOR_DETAILS = "CORRECTED_OPERATOR_DETAILS";

    public static final String RU_REMINDER_RESPONSE_COUNT = "RU_REMINDER_RESPONSE_COUNT";
    public static final String RU_RECO_TITLE = "title";
    public static final String RU_RECO_TITLE_1 = "title1";
    public static final String RU_RECO_TITLE_2 = "title2";
    public static final String RU_BRAND_LOGO = "brandLogo";
    public static final String RU_BRAND_LOGO_1 = "brandLogo1";
    public static final String RU_BRAND_LOGO_2 = "brandLogo2";
    public static final String RU_RECO_CTA = "cta";
    public static final Integer RU_THIN_BANNER_1_LIMIT = 33;
    public static final Integer RU_THIN_BANNER_2_LIMIT = 17;
    public static final String RU_RECO_WIDGET = "smart-reminder";
    public static final String RU_BANNER_WIDGET = "thin-small";
    public static final String RU_OVERIDE_WIDGET = "smart-icon-grid-4xn";
    public static final String RU = "ru";
    public static final String RU_UPPERCASE = "RU";
    public static final String RU_BANNER_KEY_1 = "bannerKey_1";
    public static final String RU_BANNER_KEY_2 = "bannerKey_2";
    public static final String RU_BANNER_KEY_3 = "bannerKey_3";
    public static final String THIN_BANNER_IMAGE_URL_1 = "image_url_1";
    public static final String THIN_BANNER_IMAGE_URL_2 = "image_url_2";
    public static final String THIN_BANNER_IMAGE_URL_3 = "image_url_3";
    public static final String THIN_BANNER_3_CTA = "Bills Due";
    public static final String INCREASE = "increase";
    public static final String MY_BILLS_URL = "my_bills_url";
    public static final String MY_BILLS_PAYLOAD = "my_bills_payload";
    public static final String RED_DOT_PRIORITY_ORDER = "ruRedDotPriorityOrder";
    public static final String OR_ALLOWED_CUST_IDS = "orAllowedCustomerIds";
    public static final String CUSTOMERID_COHORT_MAP = "customerIdToCohortNameMap";
    public static final String SETS_CONFIG_KEY = "sets_config";
    public static final String BLOCK_REQUEST_CONFIG = "block_request_config";

    public static final String SETS_CONFIG_NEW_KEY = "sets_config_new";
    public static final String RU_OVERRIDE_WIDGET_TYPES_KEY = "ru_override_widget_types";
    public static final String PAYTM_POSTPAID_SERVICE_KEY = "paytmpostpaid";
    public static final String VIEW_ID_WIDGET_MAPPING = "viewIdWidgetMapping";
    public static final String MODE_WISE_VIEW_ID_WIDGET_MAPPING="modeWiseViewIdWidgetMapping";
    public static final String DARK = "dark";
    public static final String HP_CATEGORY_LIMIT = "homepageCategoryLimit";
    public static final String HOMEPAGE_THREAD_TIMEOUT = "homepageThreadTimeout";
    public static final String PERSONALISATION_THREAD_TIMEOUT = "personalisationThreadTimeout";

    public static final String HOMEPAGE_THREAD_TIMEOUT_EVENT = "homepage_thread_timeout";
    public static final String PERSONALISATION_RELEASE_PERCENTAGE = "personalisationReleasePercentage";
    public static final String OVERRIDE_ERROR_MSG = "Error while creating override response";
    public static final String OVERRIDE_CONFIG_ERROR_MSG = "Error as override view id/widgetType is null or wrong";

    public static final String OVERRIDE_RESPONSE_SUCCESS = "success";

    public static final String CC_WIDGET_SUCCESS = "success";
    public static final String CC_WIDGET_SUCCESS_MSG = "Credit information has been retrieved successfully.";
    public static final String CC_WIDGET_FAILURE = "failure";
    public static final String CC_WIDGET_FAILURE_MSG = "Failed to retrieve credit information. Please try again later.";
    public static final String CC_WIDGET_FLAG = "isCreditChangeAndSummaryLive";
    public static final String CC_WIDGET_FLAG_NEW = "isNewCreditChangeAndSummaryLive";
    public static final String CC_WIDGET_PUBLISHED_DATE = "MMM ''yy";
    public static final String CC_WIDGET_SUMMARY_API_KEY = "summary";
    public static final String CC_WIDGET_SUMMARY_API_VALUE = "PAYMENT_HISTORY_SUMMARY";



    public static final String OVERRIDE_RESPONSE_FAILURE = "fail";
    public static final String OVERRIDE_RESPONSE = "RESPONSE";
    public static final String OVERRIDE_VERSION = "VERSION";
    public static final String OVERRIDE_PREFIX = "override|paytm|";
    public static final String RANK_WISE_SLOT_ID = "rankWiseSlotId";
    public static final String SERVICE_CAT_ID_MAP = "serviceToCatIdMap";

    public static final String OVERRIDE_TYPE = "custom-key-value";
    public static final String HOMEPAGE = "homepage";
    public static final String OVERRIDE_DYNAMIC_IMAGE_URL = "dynamic_image_url";
    public static final String OVERRIDE_STATIC_IMAGE_URL = "static_image_url";
    public static final String OVERRIDE_VIEW_IMAGE_URL = "view_more_image_url";
    public static final String OVERRIDE_YELLOW_DOT_IMAGE_URL = "yellow_dot_image_url";
    public static final String OPERATOR_ICON_ENABLED_MAP = "enableOperatorsWiseIcon";

    public static final String OVERRIDE_BANNER_ID = "bannerId";
    public static final String OVERRIDE_VIEW_BANNER_ID = "view_more_bannerId";
    public static final String OVERRIDE_DEEPLINK = "deepLink";
    public static final String OVERRIDE_VIEW_DEEPLINK = "view_more_deepLink";

    public static final String ENABLE_PERSONALISATION = "enablePersonalisation";

    public static final String GET_SAVED_CARD_ERROR = "get_save_card_error";
    public static final String IMAGEURL = "imageUrl";
    public static final String CTA_OBJ = "cta_obj";
    public static final String HEADING3 = "heading3";

    public static final String SERVICE_RENT = "rent payment";
    public static final String SERVICE_UPI_P2P = "non ru";
    public static final String BFF_REQUEST_IDENTIFIER = "bff_request";
    public static final String BFF_CHECKOUT_REQUEST_IDENTIFIER = "checkout";
    

    public static final String SERVICE_TUITION_FEES = "tuition fees";
    public static final String OVERRIDE_ERROR_CODE = "overrideErrorCode";
    public static final String DEFAULT_ERROR_CODE = "defaultErrorCode";
    public static final String NEW_PLAN_AMOUNT = "newPlanAmount";
    public static final String GW_PARAMS_TO_PASS = "gwParamsToPass";
    public static final String IS_INVALID_PLAN = "isInvalidPlan";
    public static final String DEFAULT_ERROR_CODE_FOR_INVALID_PLAN = "defaultErrorCodeForInvalidPlan";

    // ff-recharges
    public static final String ORIENTATION_ENRICH = "2";


    public static final String CREDIT_CARD_ID = "creditCardId";
    public static final String CARD_VARIANT = "cardVariant";
    public static final String PRODUCT_ID = "productId";
    public static final String UPDATED_AT_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";
    public static final String LOCALE = "locale";
    public static final String IS_HOME_REMINDER = "IsHomeRemainder";
    public static final String HOME_REMINDER = "HomeReminder";


    public static final String AUTOMATIC_CHANNEL = "SUBS 1";
    public static final String RECHARGE_AUTOMATIC_RECENT_TYPE = "RECHARGE_AUTOMATIC";
    public static final String RECHARGE_RECENT_TYPE = "RECHARGE";
    public static final String VALIDATION_RECENT_TYPE = "DROP_OFF";
    public static final String VALIDATION_AUTOMATIC_RECENT_TYPE = "DROP_OFF_AUTOMATIC";
    public static final String VALIDATION_RECENT_WO_AMT_TYPE = "DROP_OFF_WO_AMOUNT";
    public static final String VALIDATION_AUTOMATIC_RECENT_WO_AMT_TYPE = "DROP_OFF_WO_AMOUNT_AUTOMATIC";
    public static final String SMS = "sms";
    public static final String SAVED_CARD = "saved_card";
    public static final String HEADING = "heading";
    public static final String HEADING2 = "heading2";
    public static final String HEADING1 = "heading1";
    public static final String CTA = "cta";
    public static final String CHILD_CTA = "childCta";
    public static final String LABEL = "label";
    public static final String DISPLAY_LABEL = "display_label";
    public static final String DISPLAY_LABEL_COLOR = "display_label_color";
    public static final String END_BENEFICIARY_NAME = "end_beneficiary_name";
    public static final String BENEFICIARY_NAME = "beneficiary_name";
    public static final String LOGO = "logo";
    public static final String RECHARGE_LABEL_COLOR = "recharge_number_label_color";
    public static final String HEADINGS = "headings";
    public static final String CIN = "cin";
    public static final String STATUS = "status";
    public static final String SUCCESS = "success";
    public static final String FAILURE = "failure";
    public static final String UNKNOWN_INSTRUCTION = "Unknown instruction";
    public static final String ERROR_RESPONSE = "Exception while fetching this profile";
    public static final String ERROR_TIMEOUT_RESPONSE = "request_timed_out_success";
    public static final boolean FALSE = false;
    //Error code given by CT
    public static final int E_1204 = 1204;

    public static final String BILL_DATE_FORMAT = "yyyy-MM-dd";
    public static final Integer THREAD_POOL_SIZE = 100;
    public static final String DELETEFROMALLSYSTEM_SUCCESS_MSG = "Your action completed successfully.";
    public static final String DELETEFROMALLSYSTEM_FAILURE_MSG = "Your action could not be completed successfully. Please try again.";
    public static final String DELETEFROMALLSYSTEM_UNKNOWN_FAILURE_MSG = "Unknown error occurred. Please try again later!";
    public static final String DELETEFROMALLSYSTEM_API_FAILURE_MSG ="One or multiple api calls failed.";
    public static final Integer DELETION_STATUS_SUCCESS = 0;
    public static final Integer DELETION_STATUS_FAILED = 1;
    public static final Integer DELETION_STATUS_NOT_APPLICABLE = 2;
    public static final String SAGA = "SAGA";
    public static final String UPDATE_RECENT_URL = "updateRecentUrl";
    public static final String CLIENT = "client";
    public static final String VERSION = "version";
    public static final String ROLE = "role";
    public static final String CHANNEL = "channel";
    public static final String ISSUED_AT = "issuedAt";
    public static final int MAX_NAME_LENGTH = 20;
    public static final String REQUEST_TYPE_VALIDATION = "VALIDATION";
    public static final String REQUEST_TYPE_VALIDATION_ENRICH = "VALIDATION_ENRICH";
    public static final String METRIC = "metric_configuration";
    public static final String CATEGORY_ID = "categoryId";
    public static final String SEGMENT_ID = "segment_id";
    public static final String SERVICE_LABEL = "service_label";
    public static final String REDIRECT_TO_PLANS = "redirect_to_Plans";
    public static final String MESSAGES = "metric_messages";
    public static final String REQUEST_TYPE_VERIFY = "REQUEST_TYPE:EXPRESS_VERIFY";
    public static final String PRODUCT_INFO_EMPTY = "STATCODE:PRODUCT_INFO_EMPTY";
    public static final String PNS = "PnS";
    public static final String PRICE = "price";
    public static final String TOTAL_AMOUNT = "totalamount";
    public static final String VALIDITY = "validity";
    public static final String MOBILE_SUPPORT = "{\"recharge_number\": \"mobile_number\"}";
    public static final String PAYTYPE_CT_TEXT_MAPPING = "{\"postpaid\":\"Pay Bill\", \"creditcard\":\"Pay Bill\", \"feepayment\":\"Pay Now\", \"newregistratiion\":\"Pay Now\", \"prepaid\":\"Recharge\"}";
    public static final String SUBSCRIPTION_PAYMENT_MODES_DISABLED = "subscription_payment_modes_disabled";
    public static final String ITEM_CATEGORY_ID = "category_id";
    public static final String PID_BASEDON_CATEGORY = "pid_based_on_category";
    public static final String SHOW_BANK_OFFERS = "show_bank_offers";
    public static final String SITE_ID = "site_id";
    public static final String WALLET_TOKEN = "wallet_token";
    public static final String ENC_WALLET_TOKEN_IV = "encWalletTokenIV";
    public static final String X_USER_WALLET_TOKEN = "x-user-wallettoken";
    public static final String SSO_TOKEN = "sso_token";
    public static final String X_USER_SSO_TOKEN = "x-user-ssotoken";
    public static final String SSO_TOKEN_ENC_IV = "sso_token_enc_iv";
    public static final String REF_ITEM_ID = "ref_item_id";
    public static final String SUBSCRIPTION_ON = "subs_on";
    public static final String PLAN_ID = "plan_id";
    public static final String TYPE = "type";
    public static final String SUBSCRIPTION_EXPIRY_DATE = "subscriptionExpiryDate";
    public static final String SUBSCRIPTION_START_DATE = "subscriptionStartDate";
    public static final String SUBS_AMT_TYPE = "subscriptionAmountType";
    public static final String SUBS_MAX_AMT = "subscriptionMaxAmount";
    public static final String SUBS_ENABLE_RETRY = "subscriptionEnableRetry";
    public static final String SUBS_GRACE_DAYS = "subscriptionGraceDays";
    public static final String SUBS_RETRY_COUNT = "subscriptionRetryCount";
    public static final String DISABLE_PAYMENT_MODES = "disabled_payment_modes";
    public static final String SUBS_FREQ_UNIT = "subscriptionFrequencyUnit";
    public static final String SUBS_FREQ = "subscriptionFrequency";
    public static final String SUBS_VALIDITY_EXPIRY = "validity_expiry";
    public static final String MOBILE_NUMBER = "mobile_number";
    public static final String PAYTM_POSTPAID_SERVICE = "paytm postpaid";
    public static final String LOAN_SERVICE = "loan";
    public static final String FASTAG_SERVICE = "fastag recharge";
    public static final String PAYTM_POSTPAID_AID = "paytm_postpaid_aid";
    public static final String GROUPING_ERROR_ENABLED_OPERATOR = "grouping_error_enabled_operator";
    public static final String PAYTM_POSTPAID_LMS_AID = "paytm_postpaid_lms_aid";
    public static final String PAYTM_POSTPAID_ROLLOUT_PERCENTAGE = "paytm_postpaid_rollout_percentage";
    public static final String CLP_SORTER_ROLLOUT_PERCENTAGE = "clp_sorter_rollout_percentage";
    public static final String PAYTM_POSTPAID_MIN_VERSION = "paytm_postpaid_app_min_version";
    public static final String PAYTM_POSTPAID_DEEPLINK_PATH ="paytm_postpaid_deeplink_path";
    public static final String PAYTM_POSTPAID_DEEPLINK_BODY = "paytm_postpaid_deeplink_body_";
    public static final String FASTAG_DEEPLINK_PATH ="fastag_deeplink_path";
    public static final String FASTAG_DEEPLINK_BODY = "fastag_deeplink_body_";
    public static final String FASTAG_AID = "fastag_aid";
    public static final String SUBS_SERVICE_ID = "subscription_service_id";
    public static final String APPINVOKEDEVICE = "appInvokeDevice";
    public static final String SUBS_INFO = "subscribe_info";
    public static final String S2S = "s2s";
    public static final String UPDATE_SUBSCRIPTION = "update_subscription";
    public static final String PO_DATE = "pODate";
    public static final String AMOUNT = "amount";
    public static final String PENALTY_AMOUNT = "penaltyAmount";
    public static final String VARIABLE = "VARIABLE";
    public static final String ONUS_PRODUCT_ID = "onus_product_id";
    public static final String IS_SUBSCRIBE = "is_subscribe";
    public static final String ON_DEMAND = "ON_DEMAND";
    public static final String BILLER_ACCOUNT_ID ="billerAccountId";
    public static final String OLD_PID ="oldProductId";
    public static final String PAR_ID ="parId";
    public static final String NEW_CUSTOMER_ID ="customer_id";
    public static final String RECHARGE_BENEFITS = "recharge_benefits";

    public static final String REQUEST_TYPE_VALIDATION_ENRICH_V2 = "VALIDATION_ENRICH_V2";
    public static final String VERSION_1 = "version1";
    public static final String VERSION_2 = "version2";
    public static final String VERSION_3= "version3";
    public static final String TIN = "tin";
    public static final String PAR = "panUniqueReference";
    public static final String RECENT_CONFIGURATION = "configuration";
    public static final String SAVED_CARD_NETWORK = "cardNetwork";
    public static final String ISSUING_BANK_NAME = "bank_name";
    public static final String PRODUCT = "product";
    public static final String RECENT_ATTRIBUTES = "attributes";
    public static final String RECENT_CARD_NETWORK = "card_network";
    public static final String OPERATOR_DATA = "operatorData";
    public static final String OPERATOR_RECENT_DATA = "operatorRecentData";
    public static final String REC_NUM = "recharge_number";
    public static final String REC_NUM_4 = "recharge_number_4";
    public static final String REC_NUM_3 = "recharge_number_3";
    public static final String REC_NUM_2 = "recharge_number_2";
    public static final String IS_COFT = "isCoft";
    public static final String IS_NAME_REQUIRED="isNameRequired";
    public static final String IS_COFT_VIEW = "isCoftForView";
    public static final String MASKED_CARD = "maskingCard";
    public static final String SAVED_PRODUCT_ID = "productId";
    public static final String RECENT_PRODUCT_ID = "product_id";
    public static final String HAS_SAVED_CARD = "hasSavedCard";
    public static final String IS_CARD_COFT = "isCardCoft";
    public static final String IS_ELIGIBLE_FOR_COFT = "isEligibleForCoft";
    public static final String FETCH_CIN_CARDS_V1 = "FetchCINCards";
    public static final String CARD_PAYTYPE = "paytype";
    public static final String CREDIT_CARD = "Credit card";
    public static final String FEATURE_FOR_ALL = "all";
    public static final String ACTIVE_STATUS = "1";
    public static final String SMARTREMINDER = "smartreminder";
    public static final String RECHARGE_FASTAG = "rechargesfastag";

    public static final String SMARTREMINDER_AGENT_SET = "agent";
    public static final String INTERSTITIAL_SEEN_COUNT_BIN = "seenCount";
    public static final String SMARTREMINDER_VEHICAL_DETAIL_SET = "vehicaldetail";

    public static final String HR_KEY = "HR_";
    public static final String TB_KEY = "TB_";
    public static final String OR_KEY = "OR_";
    public static final String CD_KEY = "CD_";
    public static final String RU_RECO = "RU_RECO";
    public static final String SMARTREMINDER_AGENT_BIN_NAME = "AG";
    public static final String SMARTREMINDER_VEHICAL_DETAIL_BIN_NAME = "VD";
    public static final String SMARTREMINDER_AGENT_BIN_DEFAULT_VALUE = "1";
    
    
    //Block Request
    public static final String BFF_NAMESPACE = "recharges_bff";
    public static final String BLOCK_REQUEST_SET = "request_block_data_set";

    //rent Payment
    public static final String BANK_Account = "bankAccount";
    public static final String ACCOUNT_TYPE = "accountType";
    public static final String UPI = "upi";
    public static final String LATITUDE = "lat";
    public static final String LONGITUDE = "long";
    public static final String MOBILE = "mobile";
    public static final String RENT = "rent";
    public static final String RENTPAYMENT = "rentpayment";
    public static final String RENTAL = "rental";
    public static final String RENTAL_RESPONSE_STATUS = "S";
    public static final String CONTENT_TYPE = "Content-Type";
    public static final String REMOVE_RECENT_URL = "removeRecentUrl";
    public static final String DELETE_CARD_SUCCESS="SUCCESS";
    public static final String DELETE_CARD_FAILURE="FAILED";
    public static final String DELETE_CARD_NOT_APPLICABLE="NOT_APPLICABLE";
    public static final String SERVICE_IDENTIFIER_RECENT="RECENT";
    public static final String SERVICE_IDENTIFIER_REMINDER="REMINDER";
    public static final String SERVICE_IDENTIFIER_SAVED_CARD="SAVED_CARD";
    public static final String SAGA_REMOVE_RECENT="SAGA_REMOVE_RECENT";
    public static final String SERVICE_IDENTIFIER_SUBSCRIPTION="SUBSCRIPTION";
    public static final String SERVICE_IDENTIFIER_BILLER="BILLER";
    public static final String SERVICE_IDENTIFIER_SMS_PARSED_BILLS="SMS_PARSED_BILLS";
    public static final String SERVICE_IDENTIFIER_CIR="CIR";
    public static final String SUBSCRIPTION_NO_CONTENT_ERROR_STATUS="\"status\":-1";
    public static final Integer SUBSCRIPTION_NO_CONTENT_STATUS=409;
    public static final String MISSING_PARAM_CODE="02";
    public  static final String MISSING_PARAM_MESSAGE="One or more mandatory parameters is/are missing";
    public static final String DELETE_CARD_SUCCESS_MESSAGE="Your action completed successfully.";
    //CCBP
    public static final String GCIN="gcin";
    public static final String MASKED_CARD_NUMBER="maskedCardNumber";
    public static final String CARD_NETWORK="cardNetwork";
    public static final String BANK_NAME="bankName";
    public static final String LOCATION="location";

    public static final String INVALID_ACTION_TYPE_MESSAGE="Invalid Action Type";
    public static final String INVALID_REQUEST_BODY_MESSAGE="Invalid Request Body";

    //CCF
    public static final String ANDROID = "Android";
    public static final String IOS = "iOS";
    public static final String WEB = "Web";
    public static final String ANDROIDAPP = "androidapp";
    public static final String IOSAPP = "iosapp";
    public static final String SUB_CONTEXT = "";
    public static final String CASH_ON_DELIVERY = "COD";


    public static final Object IS_DYNAMIC_CCF = "is_dynamic_ccf";
    public static final String CCF = "-ccf";
    public static final String Risk_Segment_Identifier = "-R";
    public static final String CT_SERVICE = "ctPullService";
    public static final String X_CLEVERTAP_ACCOUNT_ID = "X-CleverTap-Account-Id";
    public static final String X_CLEVERTAP_PASSCODE = "X-CleverTap-Passcode";
    public static final String CLEVERTAP_EXECUTOR = "CLEVERTAP_EXECUTOR";
    public static final String CLEVERTAP_EXECUTOR_THREAD = "CLEVERTAP_EXECUTOR_THREAD";
    public static final String CLEVERTAP_RESTTEMPLATE = "CLEVERTAP_RESTTEMPLATE";
    public static final String MINPOOLSIZE = "minPoolSize";
    public static final String MAXPOOLSIZE = "maxPoolSize";
    public static final String QUEUESIZE = "queueSize";
    public static final String KEEPALIVE = "keepAlive";
    public static final String THREADTIMEOUT = "threadTimeout";
    public static final String MAXCONNECTION = "maxConnection";
    public static final String CONNECTTIMEOUT = "connectTimeout";
    public static final String READTIMEOUT = "readTimeout";
    public static final String REQUESTTIMEOUT = "requestTimeout";
    public static final String CONFIGURATION_LOCAL = "CONFIGURATION";
    public static final Integer RETRY_CT_API = 2;
    public static final String CACHE_NAMESPACE = "smartreminder";
    public static final String EVICT_CACHE_EVENT = "EvictCache";
    public static final String EVICT_OR_CACHE_EVENT = "EvictOrCache";
    public static final String HR_ENCRYPTED_BIN = "HR_ENCRYPTED";
    public static final String HR_BIN = "HR";
    public static final String WRONG_OPERATOR_ENRICH="WRONG_OPERATOR_ENRICH";
    public static final String IS_CHANGE_OPERATOR_CODE="isChangeOperatorCode";
    public static final String MNP_ERROR_POPUP ="mnp_error_popup";
    public static final String OPERATOR_SELECTION_MSG ="operator_selection_msg";
    public static final String ENRICH_ERROR_MESSAGE_CODE="errorMessageCode";
    public static final String CIRCLE="circle";
    public static final String PAY_TYPE="paytype";
    public static final String OPERATOR_PAYTYPE_LABEL="operatorPaytypeLabel";
    public static final String BLANK_SPACE="";
    public static final String RECHARGENUMBER="rechargeNumber";
    public static final String MNP_RECHARGENUMBER="number";
    public static final String X_APP_RID="x-app-rid";
    public static final String MNP_X_APP_RID   ="test-1";
    public static final String MNP_LATENCY_METRIC="mnpGetOperatorDetailsResponse";
    public static final String BFF_CLIENT_ID = "rechargesbff";
    public static final String URL_TYPE="url_type";
    public static final String DEEP_LINK="deepLink";
    public static final String POSTPAID="postpaid";
    public static final String MOBILE_POSTPAID="mobile_postpaid";
    public static final String MOBILE_PREPAID_FOR_MNP="mobile_prepaid";
    public static final String ERROR_POP_UP="errorPopup";
    public static final String DEEPLINK_TEMPLATE_FOR_OPERATOR_AND_CIRCLE="paytmmp://${url_type}?url=https://catalog.paytm.com/v2/mobile/getproductlist/${categoryId}?operator=${operator}&recharge_number=${recharge_number}&price=${amount}&circle=${circle}&ignoreMnp=true";


    public static final String TITLE="title";
    public static final String PAID_OUTSIDE_COLOR="paidOutsideColor";
    public static final String PAID_OUTSIDE_LABEL="paidOutsideLabel";
    public static final String TITLE_COLOR="color";
    public static final String TITLE_LABEL="label";
    public static final String TITLE_HTML="isHtml";
    public static final String TITLE_ADDITIONAL_INFO="additionalInfo";
    public static final String PAID_OUTSIDE_PAYTM="PAID_OUTSIDE_PAYTM";
    public static final String LAST_PAID_AMOUNT="last_paid_amount";
    public static final String UPSERT_CACHE_EVENT = "UpsertCache";
    public static final String UPDATE_CACHE_EVENT = "UpdateCache";
    public static final String FAV_LABEL_ID = "favLabelId";
    public static final String CTA_OBJECT = "cta_obj";
    public static final String RECHARGE_NUMBER_FOR_DISPLAY = "recharge_number_for_display";
    public static final String MASKING_REGEX = "[0-9]";
    public static final String MASKING_WITH_X = "X";

    public static final String EVALUATION_TYPE = "evaluation_type";
    public static final String EVALUATION_RECHARGE_TYPE = "RECHARGE";


    public static final String[] ALLOWED_ACTION_TYPES = {"MARK_AS_PAID"};

    public static final String PREPAID = "prepaid";

    public static final String UPDATE_NICKNAME = "update_nickname";


    //platform fee keys
    public static final String PLATFORM_FEE = "platform_fee";
    public static final String PLATFORM_FEE_LABEL = "platform_fee_label";
    public static final String PLATFORM_FEE_ADDITIONAL_LABEL = "platform_fee_additional_label";
    public static final String PAYMENT_MODE_WALLET = "BALANCE";
    public static final String PAYMENT_MODE_DEFAULT = "default";
    public static final String PLATFORM_FEE_DEFAULT_LABEL = "You will be charged a platform fee of";
    public static final String PLATFORM_FEE_ADDITIONAL_DEFAULT_LABEL = "Convenience Fee";
    public static final String PLATFORM_FEE_TOKEN = "authKey";
    public static final String PLATFORM_FEE_ANOMALY = "platform_fee_anomaly";
    public static final String PLATFORM_INFO = "platform_info";
    public static final String PLATFORM_FEE_HASH = "platform_fee_hash";
    public static final String PLATFORM_FEE_FLAG = "v1";
    public static final String PLATFORM_DEFAULT_LABEL_V2 = "Platform Fee";
    public static final String CONV_FEE_DEFAULT_LABEL = "default";
    public static final String MOBILE_PREPAID1 = "prepaid";
    public static final String IS_PNS = "isPns";
    public static final String DEFAULT_PLATFORM_FEE = "default_platform_fee";

    public static final String WRONG_OPERATOR="wrongOperator";
    public static final String ENRICH_PAYTYPE="enrichPaytype";
    public static final String WRONG_ENRICH_PAYTYPE="wrongEnrichPaytype";
    public static final String HAS_CONSENT_API_TIMEOUT_ERROR = "hasConsentApiTimeoutError";
    public static final String CREATE_CONSENT_API_TIMEOUT_ERROR = "createConsentApiTimeoutError";
    public static final String HAS_CONSENT_API_HIT = "hasConsentApiHit";
    public static final String CREATE_CONSENT_API_HIT = "createConsentApiHit";
    public static final String BULK_CREATE_CONSENT_API_HIT = "createBulkConsentApiHit";
    public static final String HAS_CONSENT_API_LATENCY = "hasConsentApiLatency";
    public static final String CREATE_CONSENT_API_LATENCY = "createConsentApiLatency";
    public static final String CREATE_BULK_CONSENT_API_LATENCY = "createBulkConsentApiLatency";
    public static final String COOL_OFF_DAYS_KEY = "interstitialCoolOffDays";
    public static final String COOL_OFF = "interstitialCoolOff";
    public static final String INTERSTITIAL_RETRY_KAFKA_ERROR = "interstitialRetryKafkaError";
    public static final String DUE_DATE_FORMAT = "yyyy-MM-dd";
    public static final String INTERSTITIAL_SET = "interstitial_set";


    public static final class HomeReminderConstants {
        public static final String RESPONSE_REASON = "REMINDER|RU";
        public static final String RESPONSE_CT_CAMPAIGNID = "1600000001_%s";
        public static final String THIN_BANNER_DATEFORMAT= "Due on %s";
    }

    public static final String[] MANDATORY_FIELDS = {"getProductId", "getConfiguration", "price"};
    public static final String[] CONFIGURATION_FIELDS = {"price"};
    public static final String GET_CONFIGURATION = "getConfiguration";
    public static final String DAY_DIFF_FROM_DUEDATE = "dayDiffFromDueDate";
    public static final String DAY = "day";
    public static final String DATE = "date";
    public static final String LONGSUBTITLE = "longsubtitle";
    public static final String RECO = "reco";
    public static final String RECO_CC_TITLE = "credit_card_title";
    public static final String RENT_TF_TITLE = "rent_title";
    public static final String RECO_FASTAG_TITLE = "fastag_title";
    public static final Integer BENEFICIARY_CIN_LENGTH = 36;
    public static final String SAVED_CARD_BANK_NAME = "bankName";

    //added new global variables for keys in localisation
    public static final String CONV_FEE_INFO = "conv_fee_info";
    public static final String PLATFORM_FEE_INFO = "platform_fee_info";
    public static final String PLATFORM_FEE_LABEL_NEW = "platform_fee_label";
    public static final String PLATFORM_LABEL_V2 = "platform_fee_label_v2";
    public static final String CONV_FEE_LABEL = "conv_fee_label";
    public static final String CONV_FEE_DEFAULT_INFO = "Fees charged towards covering the cost of transactions (Inclusive of GST)";
    public static final String PLATFORM_FEE_DEFAULT_INFO = "Nominal fees charged for using Paytm’s Recharges and Bill Payment services. Charges levied are regardless of payment option used and inclusive of GST.";
    public static final String PLATFORM_FEE_LABEL_DEFAULT_NEW = "default";
    public static final String POLICY_ENGINE_SEGMENT_ID = "segmentId";

    //Policy Rule Engine
    public static final String PLATFORMFEE_RULE_ENGINE = "platformfee_rule_engine";
    public static final String POLICY_RULE_ENGINE_SERVICE = "preFeeService";
    public static final String FASTTAG_SERVICE = "fasttagService";
    public static final String POLICY_RULE_ENGINE_START_TIME = "startDate";
    public static final String POLICY_RULE_ENGINE_END_TIME = "endDate";

    public static final String DELIMATOR="_";
    public static final String DEEPLINK = "deeplink";
    public static final String ENCODED_LONG_URL = "encodedLongUrl";
    public static final String TTL = "ttl";
    public static final String BASIC = "Basic ";
    public static final String US_ASCII = "US-ASCII";
    public static final String AUTHORISATION = "Authorization";
    public static final Integer VALUE = 129600;

    public static final String CT_NAME = "ct_segment";
    public static final String CT_RISK = "ct_risk";
    public static final String SEGMENT_SET = "segmentSet";
    public static final String CT_API_SEGMENTS = "ct_api_segments";
    public static final String SEGMENT_ERROR_IN_SAVING_CACHE = "segment_error_in_save_cache";
    public static final String SEGMENT_NOT_FOUND_IN_CACHE = "segment_not_found_in_cache";
    public static final String SEGMENT_FOUND_IN_CACHE = "segment_found_in_cache";
    public static final int SEGMENT_EXPIRY = 3600;
    public static final String EMPTY_STRING = "";
    public static final String UPSERT_ERROR_IN_SAVING_CACHE = "upsert_error_in_save_cache";

    //Service Config
    public static final String CATEGORY_MAPPING = "categoryMapping";
    public static final String SMS_ENABLED_SERVICES = "smsEnabledServices";
    public static final String SAGA_ENABLED_SERVICES = "sagaEnabledServices";
    public static final String OR_CUSTOMER_IDS = "overrideCustIds";
    public static final String SUBSCRIPTION_MAX_AMOUNT_CCBP = "subscriptionMaxAmountCCBP";
    public static final String RECO_REQUEST_IDENTIFIER = "isRecoRequest";
    public static final String TRUE = "true";


    public static final String SERVICE_CONFIG_SERVICE = "recharge_service_config_bff_beta";
    public static final String SERVICE_KEY = "key";
    public static final String SERVICE_NAME = "service_name";

    public static final String RU_REMINDERS_WIDGET_TYPES_KEY = "ru_reminders_widget_types";

    public static final String RU_REMINDER_FOLDER_VIEW_OBJECT_KEY = "ru_reminders_folder_view_object";
    public static final Integer RU_REMINDER_FOLDER_VIEW_CONDITION_COUNT = 1;

    //feature_config
    public static final String ROLL_OUT = "all";
    public static final String EXCLUDED_SERVICES = "excludedServices";
    public static final String V1_PLAN_VALIDITY_ALLOWED_FOR_CATEGORY_IDS = "isV1PlanValidityAllowedForCategoryId";
    public static final String V1_PLAN_VALIDITY_ALLOWED_FOR_PIDS = "isV1PlanValidityAllowedForProductIds";

    public static final String RECENTS_V2 = "recents_v2";
    public static final String RENEW_AUTO = "renew_auto_pay";
    public static final String RECHARGE_AUTOMATIC_FAILURE = "recharge_automatic_failure";
    public static final String SMART_RECENTS = "smart_recents";
    public static final String EARLY_PAYMENT_RECENTS = "early_payment_recents";
    public static final String ISRISK = "isRisk";


    public static final String LAKH = "lakh";
    public static final String CRORE = "crore";

    public static final String SMS_CARD_ERROR_EVENT = "get_sms_card_error";
    public static final String SMS_CARD_MERGE_ERROR_EVENT = "get_sms_card_merge_error";
    public static final String SAVED_CARD_MERGE_EVENT = "get_save_card_merge_error";
    public static final String SAVED_CARD_EVENT = "get_save_card_error";

    public static final String RU_RECO_INVALID_CRED = "get_reco_view_id_widget_mismatch";
    public static final String HOMEPAGE_INVALID_CALL = "HOMEPAGE_INVALID_CALL";



    public  static final String DATE_FORMAT_FOR_TIMESTAMP = "yyyy-MM-dd HH:mm:ss";
    public static final  String DATE_FORMAT = "dd-MM-yyyy";
    public static final  String DATE_FORMAT_1 = "yyyy-MM-dd";
    public static final String BILL_DATE_FORMAT_FOR_DUE_BILL="dd MMM";
    public static final String PAYTYPE="payType";
    public static final  String RECENT_CATEGORY_ID="category_id";
    public static final String PAYTYPE_CREDIT_CARD="credit card";
    public static final String RENT_PAYMENT = "rent payment";
    public static final String BUSINESSPAYMENT = "businesspayment";
    public static final String SERVICE_BUSINESS_PAYMENT = "business payment";
    public static final String AUTOMATIC_DATE_TIMESTAMP="yyyy-MM-dd HH:mm:ss";
    public static final String SCHEDULABLE="schedulable";
    public static final String DISPLAY_SUMMARY_KEYS = "displaySummaryKeys";
    public static final String CATEGORY_NAME_MAPPING = "categoryNameMapping";
    public static final Integer DISPLAY_SUMMARY_SIZE = 3;
    public static final String CART_VERSION_V4 = "CART_VERSION_V4";

    public static final String PARALLEL_VERIFY_FLAG = "PARALLEL_VERIFY_FLAG";

    public static final String BLOCKER_USER_REQUEST = "blocked_user_req";
    public static final String BLOCKED_USERS_SET="blocked_users_set";
    public static final String BLOCKED_USERS="blocked_users";
    public static final String USER_NOT_FOUND_IN_BLOCKED_LIST_CACHE="user_not_found_in_blocked_list_cache";
    public static final String USER_FOUND_IN_BLOCKED_LIST_CACHE="user_found_in_blocked_list_cache";
    public static final String BLOCKED_USER_ERROR_CODE="1100";
    public static final String SMS_CARD_TYPE = "smsCard";
    public static final String UPI_CARD_TYPE = "upiCard";
    public static final String CSV_CARD_TYPE = "csv";
    public static final String SAVED_CARD_TYPE = "savedCard";
    public static final String PG_CARDS = "PG_Cards";
    public static final String SMS_CARD = "SMS_CARD";
    public static final String SMS_BILLED_SERVICES="smsBillsServices";
    public static final String DELETE_FROM_ALL_SYSTEM_API="deletefromallsystem_api_error";
    public static final String DELETE_SUBSCRIPTION_API="deleteSubscriptionApi";
    public static final String SUBSCRIPTION_DELETED="subscription_deleted";
    public static final String DELETE_FROM_ALL_SYSTEM_REMINDER_NON_RU_NOTIFICATION_API_MISSING_PARAM="deletefromallsystem_remindernonrunotification_api_missingparam";
    public static final String CARD_SKIN_NOT_FOUND = "card_skin_not_found";
    public static final Map<String, String[]> DELETE_API_FAILURE  = new HashMap<String, String[]>() {{

        put("01", new String[] {"UNKNOWN_ERROR : 01"});

        put("02", new String[] {"MANDATORY_PARAM_MISSING : 02"});

        put("03", new String[] {"API_FAILURE : 03"});

    }};
    public static final Map<String, String> RECO_SAGA_CLIENT_NAME_MAP  = new HashMap<String, String>() {{
        put("IOS", "iosapp");
        put("ANDROID", "androidapp");
    }};
    public static final String CATEGORY = "Category : ";
    public static final String METRIC_SERVICE = "Service : ";
    public static final String METRIC_MARKET_FAV_RESPONSE_SIZE = "Response_Size : ";
    public static final String ERROR_IN_GET_V3="error_in_get_V3";
    public static final String TXN_COUNT = "txnCount";
    public static final String X_USER_ID = "x-user-id";
    public static final String PAYMODE_COUNT_SUPPORT_SERVICE="paymodeCountSupportService";
    public static final String ORDER_COUNT = "orderCount";
    public static final String ORDER_COUNT_SET = "orderCountSet";
    public static final String TXNCOUNT_DELIMITER = "_t";
    public static final String MCN_MASKED_PREFIX = "XXXX XXXX XXXX ";
    public static final String MCN_MASKED_PREFIX_PLACEHOLDER = "XXXX";


    public static final String  REFERENCE_ID_VALUE= "referenceIdValue";
    public static final String  ACCESS_TOKEN_VALUE= "accessTokenValue";
    public static final String SUBSCRIPTION_MAX_AMOUNT = "subscriptionMaxAmount";
    public static final String SUBS_PAY_INTENT = "subs_pay_intent";
    public static final String MODE = "mode";


    public static final class LocalisationKeyConstants {
        public static final String RECENTS = "recents_v2";
        public static final String CIR = "cir";
        public static final String RECO = "reco";
    }

    public static final class LatestTwoOrdersConstants {
        public static final String RECENT = "RECENT";
        public static final String SUGGESTED_CARD = "SUGGESTED_CARD";
        public static final String INVALID_PLAN_CARD = "INVALID_PLAN_CARD";
        public static final String SUCCESS_CARD = "SUCCESS_CARD";
    }

    public static final String WAREHOUSE_ID_FOR_PAYTM_POSTPAID_TXNS= "wareHouseIdForPaytmPostpaidTxns";
    public static final String PAYTM_POSTPAID= "paytmPostpaid";
    public static final String FREQUENT_ORDERS_VERSION_V4 = "/v4/frequentOrders";
    public static final String API_VERSION_KEY = "API_VERSION";
    public static final String UTILITY_GROUPING_ATTRIBUTE_KEY_PREFIX = "utility__grouping_attr_";
    public static final String UTILITY_SERVICES_KEY = "utility_services";
    public static final String API_RESPONSE_FILTER_KEY="frequentOrdersApiV4_response_filter";
    public static final String ENCRYPTION_ON_CUSTOMERIDS = "encryption_on_customerIds";
    public static final String ENABLE_DATA_ENCRYPTION_ON_CUSTOMER_FLAG = "enable_data_encryption_on_customer_flag";
    public static final String ENABLE_DATA_ENCRYPTION_FLAG = "enable_data_encryption_flag";
    public static final String ALLOWED_ROLLOUT_PERCENTAGE = "allowed_rollout_percentage";
    public static final String AUTOMATIC_CTA_EXCLUDED_LIST = "automatic_cta_excluded_list";
    public static final String FLOW = "flow";
    public static final String CIR_PRODUCT_TYPE = "productType";
    public static final String CIR_USER_ID = "userId";

    public static final String CIR_API_ERROR = "Unable to proceed, please try again";
    public static final String CIR_ERROR_MESSAGE = "Something went wrong. Please try after some time!";
    public static final String CIR_GETBANKNAMES_ERROR_MESSAGE = "getBankNames:: customerId or bureauname is null";
    public static final String CIR_USER_NOT_FOUND = "User not found";
    public static final String CIR_INVALID_CRED = "Invalid mobile number or user name, please try again";
    public static final String CIR_MISSING_MANDATORY_PARAM = "Either first name or phone number is missing";

    public static final String TNC_MISSING_MANDATORY_PARAM = "Either code or version missing for tnc api";
    public static final String CIR_MISSING_MANDATORY_FLAG = "Mandatory flag missing";
    public static final String CIR_KEY_NOT_FOUND = "KEY_NO_FOUND";

    public static final String CIR_SYSTEM_ERROR = "SYSTEM_ERROR";
    public static final String CIR_INVALID_CRED_CODE =  "********";
    public static final String CIR_CREDIT_CARD =  "CREDIT_CARD";
    public static final String CIR_ACTIVE_CARD =  "ACTIVE";
    public static final String SYNC_BUREAU = "syncBureau";
    public static final String BUREAU = "bureau";
    public static final String TNC_JWT_KEY = "67EB50D32778B11F579854C47EE4A876487309D6F9F37D0167B750DC112C2C1B";
    public static final String TNC_JWT_CUSTID = "1611127110357";
    public static final String TNC_CLIENT_ID = "bff";
    public static final String JWT_TOKEN_KEY ="x-jwt-token";
    public static final String TNC_MODE_CLICKTHROUGH = "clickthrough";
    public static final String TNC_CODE = "code";
    public static final String TNC_CODE_VALUE = "ru_ccbp";
    public static final String TNC_VERSION = "version";
    public static final String TNC_ACCEPT = "accept";

    public static final String CIR_MANDATORY_FLAG_MISSING = "get_cir_mandatory_flag_miss";
    public static final String CIR_MANDATORY_PARAM_MISSING = "get_cir_mandatory_param_miss";
    public static final String CIR_MISSING_TNC_PARAMS = "get_cir_mandatory_tnc_param_miss";
    public static final String CIR_ERROR_BUREAU_GET_API = "get_cir_error_bureau_get_api";
    public static final String CIR_ERROR_BUREAU_POST_API = "get_cir_error_bureau_post_api";
    public static final String CIR_MY_BILLS_CLP_PUSH_ERROR = "cir_my_bills_clp_push_error";
    public static final String CIR_TNC_API_ERROR = "get_cir_error_tnc_api";


    public static final String SMS_CARD_NO_AMOUNT = "SMS_CARD_NO_AMOUNT";

    public static final String CIR_WIDGET = "CIR";
    public static final String LANGUAGE = "LANGUAGE_ENGLISH";

    public static final String SMART_RECENT_VALIDATION= "smart_recent_validation";
    public static final String SMART_RECENT_DROPOFF= "smart_recent_dropoff";
    public static final String DUE_DATE = "Due Date";

    public static final String MINIMUM_PAYABLE_AMOUNT = "Minimum Payable Amount";
    public static final String TOTAL_DUE_AMOUNT = "Total Due Amount";
    public static final Integer MIN_DUE_PAYMENT_OPTION_ID = 2;
    public static final Integer TOTAL_DUE_PAYMENT_OPTION_ID =1;

    public static final String CONSUMER_NAME =  "Consumer Name";
    public static final String DISPLAY_VALUE_OUTSTANDING_AMOUNT = "Current Outstanding Amount";
    public final static String RECON_ID = "reconId";

    public static final String REMOVE_PG_TOKEN_API="remove_pg_token_api";

    public static final String REMOVE_PG_TOKEN_ERROR_API="remove_pg_token_error_api";

    public static final String HP_RED_YELLOW_COMBINED_LIMIT = "combinedRedYellowLimit";

    public static final String SMART_RECENT_ENABLED_SERVICES_KEY = "smartRecentsEnabledServices";
    public static final String SMART_RECENT_RECO_ENABLED_SERVICES_KEY = "smartRecentsRecoEnabledServices";
    public static final String CLP_CATEGORY_STATEWISE_DUE_MINS = "clpCategoryStateWiseDueMinsConfig";
    public static final String CLP_CARD_RANK = "clpCardRank";
    public static final List<Integer> INVALID_RECHARGE_NUMBER_ERROR_CODE_LIST = Collections.singletonList(1033);
    public static final String ERROR_MESSAGE_CODE_KEY = "errorMessageCode";
    public static final String OPERATOR_RESPONSE_CODE_KEY = "operatorResponseCode";
    public static final String INVALID_RECHARGE_NUMBER_ERROR_CODE_KEY = "invalid_recharge_number_error_code_list";

    public static final String RENEW_AUTOMATIC="RENEW_AUTOMATIC";
    public static final List<Integer> INACTIVE_PID_STATUS = Arrays.asList(0, 2);

    public  static final String BILL_TYPE = "bill_type";

    public static final String VIEW_ID = "viewId:";
    public static final String RECO_VIEW_ID_COUNT = "recoViewIdCount";

    public static final String PAYTM_IFSC = "PYTM0123456";

    public static final String PPBL_RECENT = "PPBL_RECENT";

    public static final String BBPS = "BBPS";

    //For parallel validation failure error code priority is SUCCESS (0) > NO_BILL (1030) > OTHERS (-1) > INVALID_RECHARGE_NUMBER (1033)
    public static final List<Integer> DEFAULT_PARALLEL_VALIDATION_FAILURE_ERROR_CODE_PRIORITY_LIST = Arrays.asList(0, 1030, -1, 1033);
    public static final String REMINDER_RESPONSE = "reminderResponse";

    public static final String PARALLEL_VALIDATION_FAILURE_ERROR_CODE_PRIORITY_LIST_KEY = "parallel_validation_error_code_priority_list";
    public static final String PARALLEL_VALIDATION_DELETE_FROM_ALL_SYSTEM_MISSING_PARAM="parallel_validation_delete_recent_missing_param";
    public static final String PARALLEL_VALIDATION_DELETE_FROM_ALL_SYSTEM_REJECTED="parallel_validation_delete_recent_reject";
    public static final String PARALLEL_VALIDATION_DELETE_FROM_ALL_SYSTEM_INCORRECT_OLD_PID="parallel_validation_delete_recent_incorrect_old_pid";
    public static final String PARALLEL_VALIDATION_DELETE_FROM_ALL_SYSTEM_FAILURE="parallel_validation_delete_recent_failure";
    public static final String NEW_BILL_TAG_BG_COLOUR = "newBillTagBgColour";
    public static final String NEW_BILL_TAG_TEXT_COLOUR = "newBillTagTextColour";
    public static final String NEW_BILL_TAG_LABEL_KEY = "newBillTagLabelKey";
    public static final String IS_NEW_BILL_IDENTIFIED = "isNewBillIdentified";


    public static final String CAR_DETAILS = "carDetails";
    public static final String CAR_IMAGES = "carImages";
    public static final String FUZZU_COLOR = "fuzzyColor";


    public static final String FASTAG_VRN = "fastagVrn";
    public static final String PLAN_MAPPING = "plan_mapping";
    public static final String PLAN_MAPPING_ERROR_METRIC = "fetch_plan_mapping_error";
    public static final int DISCONTINUED_PLAN_MAPPING_TYPE = 1;
    public static final String PLAN_MAPPING_LOCK_KEY = "PLAN_MAPPING_LOCK";

    public static final String FAVOURITE_ENABLE = "FAVOURITE_ENABLE";
    public static final String BFF_SMART_REMINDER = "BFF_SMART_REMINDER";
    public static final String SPRING_PROFILES_ACTIVE = "SPRING_PROFILES_ACTIVE";
    public static final String PARTIAL_BILL_ENABLED_CATEGORIES = "partialBillEnabledCategoryIds";

    public static final String MOBILE_PREPAID_RECO = "mobile_prepaid_reco";
    public static final String MOBILE_POSTPAID_RECO = "mobile_postpaid_reco";
    
    public static final String BLOCK_REQUEST_EXECUTOR = "blockRequestExecutor";

    public static final String MY_BILLS_TAB_ENABLE = "myBillsHeadingEnabledFromRu";
    public static final String MY_BILLS_TAB_NUMBER_LIMIT = "minCountForMyBillsDueTag";
    public static final String MY_BILLS_TAB_NAME_KEY = "my_bills_tab_name";
    public static final String MY_BILLS_TAB_TYPE_KEY = "my_bills_tab_type";
    public static final String MY_BILLS_TAB_CTA = "my_bills_tab_cta";
    public static final String MY_BILLS_TAB_V_CTA = "my_bills_tab_vcta";
    public static final String MY_BILLS_TAG_NAME = "my_bills_due_tag";
    public static final String MY_BILLS_TAG_SINGLE_DUE = "my_bills_single_due_tag";
    public static final String MY_BILLS_TAG_NO_DUE = "my_bills_no_due_tag";
    public static final String MY_BILLS_TOGGLE_TAG = "my_bills_due_toggle_tag";
    public static final String MY_BILLS_CHEVRON_IMAGE_URL = "my_bills_chevron_image_url";
    public static final String MY_BILLS_TAB_BG_COLOUR = "my_bills_tab_bg_colour";
    public static final String ALLOWED_OPERATORS_FOR_RECO = "allowedOperatorsForReco";
    public static final String DISABLED_OPERATORS_FOR_RECO = "disabledOperatorsForReco";
    public static final String REMIND_ME_LATER_DATE_OFFSET = "getRemindMeLaterDateOffset";
    public static final String REMIND_ME_LATER_VALIDATION_FAILED = "Request validation failed.";
    public static final String REMIND_ME_LATER_FAILURE_MSG = "Something went wrong. Please try after some time!";
    public static final String META_DATA_SKIP_CONV_FEE_KEY="skipConvFee";
    public static final String META_DATA_PLAN_BUCKET_KEY="plan_bucket";
    public static final String SERVICE_CONFIG_SKIP_CONV_FEE_KEY="skipConvFeeConfig";
    public static final String POST_FREQUENT_ORDER_LATENCY = "post_frequent_order_latency";
    public static final String POST_FREQUENT_ORDER_LATENCY_V3 = "post_frequent_order_latency_v3";
    public static final String POST_FREQUENT_ORDER_LATENCY_V4 = "post_frequent_order_latency_v4";
    public static final String IS_AMBIGUOUS_PID = "isAmbiguousPid";
    public static final String RED_YELLOW_LOG_PUSH_ERROR = "red_yellow_log_push_error";
    public static final String MY_BILLS_CLP_LOG_PUSH_ERROR = "my_bills_clp_log_push_error";
    public static final String ONE = "1";
    public static final String GROUP = "group";
    public static final String FLAG_METRIC_TAG = "Flag : ";
    public static final String PRODUCT_METRIC_TAG = "Product : ";
    public static final String ASYNC_VERIFY_NAMESPACE = "async_verify";
    public static final String ASYNC_VERIFY_FLAGS_KEY = "asyncVerifyFlags";
    public static final String IS_AMOUNT_EDITABLE_FLAG = "isAmountEditable";
    public static final String IS_GROUP_DISPLAY_ENABLED_FLAG = "isGroupDisplayEnabled";
    public static final String ASYNC_VERIFY_FLAGS_UPDATE_ALLOWED_SERVICE = "asyncVerifyFlagsUpdateAllowedService";

    public static final String MARK_AS_PAID_SOURCE = "mark_as_paid_source";
    public static final String REMIND_LATER_DAYS_CONFIG = "remindLaterDaysConfig";
    public static final String VERIFY_DISABLE_PAYMODES = "verify_disable_paymodes";
    public static final String VERIFY_DISABLE_PAYMODES_METRIC_PUSH_ERROR = "verify_disable_paymodes_metric_error";
    public static final String VERIFY_DISABLE_PAYMODES_FE = "verify_disable_paymodes_fe";
    public static final String API_VERSION_FOR_FREQUENT_ORDERS="apiVersion";
    public static final String VERSION_5_FOR_FREQUENT_ORDERS="v5";
    public static final String IS_CARD_SKIN_REQUIRED="isCardSkinRequired";
    public static final String IS_CATEGORY_FOR_REDIRECT_PLANS="isCategoryForRedirectPlans";
    public static final String CHECKOUT_SUSBCRIPTION_METRICS = "checkout_subscription";
    public static final String ENRICH_ERROR_DURING_SANITY = "ENRICH_ERROR_DURING_SANITY";
    public static final String SAVE_CONSENT_TO_MASTER_SQL_QUERY = "INSERT INTO bill_fetch_consents (consent_key, customer_id, operator_identifier, is_active, service, is_encrypted) VALUES (?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE is_active = VALUES(is_active), updated_at = NOW()";

    public static final String FETCH_CONSENT_FROM_SLAVE_SQL_QUERY = "SELECT * FROM bill_fetch_consents WHERE consent_key = ? AND customer_id = ? AND operator_identifier = ?";
    public static final String FETCH_CONSENT_FROM_SLAVE_SQL_IN_QUERY = "SELECT * FROM bill_fetch_consents WHERE consent_key IN (?,?) AND customer_id = ? AND operator_identifier = ?";
    public static final String BBPS_CONSENT = "bbpsConsent";
    public static final String BBPS_CONSENT_VALID_TILL = "consentValidTill";
    public static final String BBPS_CONSENT_SAVE_OR_UPDATE_ERROR = "bbpsConsentSaveOrUpdateError";
    public static final String BBPS_CONSENT_DB_FETCH_ERROR = "bbpsConsentDBFetchError";
    public static final int CONSENT_CREATED_OR_UPDATED = 1;
    public static final int CONSENT_ALREADY_PRESENT = 2;
    public static final int CONSENT_NOT_CREATED_OR_UPDATED = 0;
    public static final int CONSENT_VALIDITY_IN_DAYS = 180;
    public static final String CONSENT_KEY = "consent_key";
    public static final String CUSTOMERID = "customer_id";
    public static final String OPERATOR_IDENTIFIER = "operator_identifier";
    public static final String IS_ACTIVE = "is_active";
    public static final String IS_ENCRYPTED = "is_encrypted";

    public static final String CREATED_AT = "created_at";

    public static final String UPDATED_AT = "updated_at";

    public static final String FETCH_CONSENT_FROM_SLAVE = "fetch_consent_from_slave";

    public static final Integer ENRICH_ERROR_MESSAGE_CODE_CONSENT_MISSING = 1500;

    public static final String ERROR_MESSAGE_FOR_CONSENT_MISSING = "Now, all your credit card bills are powered via BharatConnect (operated by NPCI). Never miss paying a bill and keep yourself updated. By proceeding, you allow Paytm to fetch & store bills and remind you whenever a bill is due.";

    public static final String CONSENT_FEATURE_FLAG = "consentFeatureFlag";
    public static final String ELECTRICITY = "electricity";
    public static final String CUSTOMER_CONSENT_KAFKA_ERROR = "CUSTOMER_CONSENT_KAFKA_ERROR";
    public static final String AES256_ENCRYPTION_ERROR = "AES256_ENCRYPTION_ERROR";
    public static final String AES256_DECRYPTION_ERROR = "AES256_DECRYPTION_ERROR";
    public static final String HR_NOT_SAVED_IN_CACHE_CC_TIMEOUT = "hr_not_saved_in_cache_cc_timeout";
    public static final String HR_SAVED_IN_CACHE_SUCCESS = "hr_saved_in_cache_success";
    public static final String HAS_CONSENT_API_ERROR = "HAS_CONSENT_API_ERROR";
    public static final String CREATE_CONSENT_API_ERROR = "CREATE_CONSENT_API_ERROR";
    public static final String BULK_CREATE_CONSENT_API_ERROR = "BULK_CREATE_CONSENT_API_ERROR";

    public static final String  RU_PARTIAL_BILL_RECO_SERVICES = "ru_partial_bill_reco_services";

    public static final String SENSITIVE_FIELDS_CUSTOM_LOGGER = "sensitiveFields_custom_logger";
    // Interstitial API response constants
    public static final String INTERSTITIAL_KAFKA_SUCCESS_STATUS = SUCCESS;
    public static final String INTERSTITIAL_KAFKA_SUCCESS_CODE = "200";
    public static final String INTERSTITIAL_KAFKA_SUCCESS_MSG = "Interstitial event pushed to Kafka successfully.";

    public static final String INTERSTITIAL_IMPRESSION_SUCCESS_STATUS = SUCCESS;
    public static final String INTERSTITIAL_IMPRESSION_SUCCESS_CODE = "200";
    public static final String INTERSTITIAL_IMPRESSION_SUCCESS_MSG = "Interstitial impression registered successfully.";

    public static final String INTERSTITIAL_IMPRESSION_FAILURE_STATUS = FAILURE;
    public static final String INTERSTITIAL_IMPRESSION_FAILURE_CODE = "500";
    public static final String INTERSTITIAL_IMPRESSION_FAILURE_MSG = "Failed to register interstitial impression.";

    // Interstitial config keys
    public static final String INTERSTITIAL_RETRY_COUNT_KEY = "interstitialRetryCount";
    public static final String INTERSTITIAL_AEROSPIKE_UPDATE_COUNT = "interstitialAerospikeUpdateCount";
    public static final String INTERSTITIAL_CACHE_UPDATE_ERROR = "interstitialCacheUpdateError";
    public static final String INTERSTITIAL_DEFAULT_RETRY_COUNT = "3";
    public static final String INTERSTITIAL_BILL_KEY_FORMAT = "%s_%s_%s_%s_%s_%s";
	public static final String DISABLE_PERSONLAISATION_RED_YELLOW_DOT_ICON = "DISABLE_PERSONLAISATION_RED_YELLOW_DOT_ICON";
	public static final String IMAGE_URL = "image_url";
	public static final String BANNER_ID = "bannerId";
	public static final int MODULO_BASE_PERCENTAGE = 100;
	public static final String ROLLOUT_PERSONALISATION_WIDGET_WHITELIST_CUSTIDS = "ROLLOUT_PERSONALISATION_WIDGET_WHITELIST_CUSTIDS";
	public static final String ROLLOUT_PERSONALISATION_WIDGET_PERCENTAGE = "ROLLOUT_PERSONALISATION_WIDGET_PERCENTAGE";
    public static final String CAROUSEL_TICKER = "carousel-ticker";
    public static final String UNKNOWN_RECENT_TYPE= "UNKNOWN_TYPE";

    public static final Map<String, String> LOCALE_MAP = new HashMap<>();

    static {
            LOCALE_MAP.put("en-in", LANGUAGE_ENGLISH);
            LOCALE_MAP.put("hi-in", "hi-IN"); // Hindi
            LOCALE_MAP.put("bn-in", "bn-IN"); // Bengali
            LOCALE_MAP.put("od-in", "od-IN"); // Odia
            LOCALE_MAP.put("mr-in", "mr-IN"); // Marathi
            LOCALE_MAP.put("ml-in", "ml-IN"); // Malayalam
            LOCALE_MAP.put("kn-in", "kn-IN"); // Kannada
            LOCALE_MAP.put("ta-in", "ta-IN"); // Tamil
            LOCALE_MAP.put("te-in", "te-IN"); // Telugu
            LOCALE_MAP.put("gu-in", "gu-IN"); // Gujarati
            LOCALE_MAP.put("pa-in", "pa-IN"); // Punjabi
    }
    public static final String MISSING_LOCALISATION_KEY_METRIC = "missing_localisation_key";
    public static final String DEFAULT_RECENT_TYPE = "DEFAULT_TYPE";
    public static final String RECENT_RESPONSE_PROCESSING_ERROR = "RECENT_RESPONSE_ERROR";

    public static final String  FETCH_CAR_DETAILS_DISABLED = "fetch_car_details_disabled";
    public static final List<String> NEW_ACCOUNT_STATES_LIST = Arrays.asList(EventState.NEW_ACCOUNT.name(),EventState.NEW_ACCOUNT_CONSENT_PENDING.name(),EventState.NEW_ACCOUNT_CONSENT_NOTRECEIVED.name(),EventState.NEW_ACCOUNT_NO_CONSENT.name(),EventState.OLD_BILLER.name());

}

