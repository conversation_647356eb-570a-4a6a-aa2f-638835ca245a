package com.paytm.recharges.bff.repository.impl;

import com.paytm.recharges.bff.client.AerospikeGenericWrapper;
import com.paytm.recharges.bff.constants.Constants;
import com.paytm.recharges.bff.constants.InterstitialConstants;
import com.paytm.recharges.bff.datalayer.cache.ServiceConfigCache;
import com.paytm.recharges.bff.datalayer.dto.InterstitialDataRecord;
import com.paytm.recharges.bff.repository.InterstitialRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Repository
public class AerospikeInterstitialRepository implements InterstitialRepository {

    @Autowired
    private AerospikeGenericWrapper aerospikeGenericWrapper;

    @Override
    public Map<String, InterstitialDataRecord> getInterstitialData(List<String> keys) {
        if (keys == null || keys.isEmpty()) {
            return new HashMap<>();
        }

        try {
            Map<String, InterstitialDataRecord> recordMap;
            recordMap = aerospikeGenericWrapper.getBatchData(
                    Constants.BFF_NAMESPACE,
                    Constants.INTERSTITIAL_SET,
                    keys,
                    ServiceConfigCache.getInstance().getInteger(InterstitialConstants.INTERSTITIAL_BATCH_TIMEOUT_IN_MS),
                    InterstitialDataRecord.class
            );
            return recordMap;
        } catch (Exception e) {
            log.error("Error in batch fetching interstitial data: keys={}, error={}",
                    keys, e.getMessage(), e);
            return null;
        }
    }
}
