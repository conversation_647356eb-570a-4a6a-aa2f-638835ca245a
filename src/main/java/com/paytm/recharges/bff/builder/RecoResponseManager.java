package com.paytm.recharges.bff.builder;

import com.paytm.recharges.bff.constants.RecentConstants;
import com.paytm.recharges.bff.datalayer.model.Product;
import com.paytm.recharges.bff.service.RecoResponseBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.paytm.recharges.bff.constants.Constants.*;

/**
 * Utility class for formatting titles and handling title-related operations
 */
@Component
@Slf4j
public class RecoResponseManager {


    private final List<RecoResponseBuilder> recoResponseBuilders;

    public RecoResponseManager(List<RecoResponseBuilder> recoResponseBuilders) {
        this.recoResponseBuilders = recoResponseBuilders;
    }

    /**
     * Formats the title based on product and configuration
     *
     * @param product The product for which the title is being generated
     * @param config The configuration map containing necessary information
     * @param payload The payload map for storing additional information
     * @return The formatted title string
     */
    public String getFormattedTitle(Product product, HashMap<String, Object> config, Map<String, String> payload) {
        if (config == null || product == null) {
            return "";
        }
        
        // Find the first formatter that can handle this product
        return recoResponseBuilders.stream()
                .filter(formatter -> formatter.canHandle(product))
                .findFirst()
                .map(formatter -> formatter.format(product, config, payload))
                .orElse("");
    }



    /**
     * Creates a properly formatted title key array
     *
     * @param product The product for which the title key is being generated
     * @param payload The payload map for storing additional information
     * @return Array of title key components
     */
    public String[] getTitleKey(Product product, Map<String, String> payload) {
        String operatorLabel = product.getOperatorLabel();
        String transformedOperatorLabel = "";
        if (operatorLabel != null) {
            transformedOperatorLabel = operatorLabel.toLowerCase().replaceAll("\\s+", "");
            payload.put(RecentConstants.OPERATOR_LABEL, operatorLabel);
        }
        log.info("RecoResponseManager.getTitleKey :: product.getServiceKey() :: {}", product.getServiceKey());
        log.info("RecoResponseManager.getTitleKey :: product.getPayTypeKey() :: {}", product.getPayTypeKey());
        log.info("RecoResponseManager.getTitleKey :: transformedOperatorLabel :: {}", transformedOperatorLabel);
        return new String[]{LANGUAGE_ENGLISH, RECO, product.getServiceKey(), product.getPayTypeKey(), transformedOperatorLabel};
    }
} 