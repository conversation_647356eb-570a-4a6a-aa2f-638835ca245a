package com.paytm.recharges.bff.builder;

import com.paytm.recharges.bff.datalayer.dto.response.RecentResponse;
import com.paytm.recharges.bff.datalayer.dto.response.SagaRecentResponse;
import com.paytm.recharges.bff.datalayer.model.HeadingProperties;
import com.paytm.recharges.bff.datalayer.model.RecentWrapper;
import com.paytm.recharges.bff.responsehandler.CommonHeadingHandler;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;

import static com.paytm.recharges.bff.constants.Constants.HEADING1;
import static com.paytm.recharges.bff.constants.Constants.MOBILE;
import static com.paytm.recharges.bff.constants.Constants.RENTPAYMENT;
import static com.paytm.recharges.bff.constants.RecentConstants.COLOR_CONSTANTS.DEFAULT_COLOR;
import static com.paytm.recharges.bff.constants.RecentConstants.CREDITCARD;

@Component
public class Heading1Handler extends CommonHeadingHandler {
    private static final CustomLogger log = CustomLogManager.getLogger(Heading1Handler.class);

    @Override
    protected HeadingProperties create(RecentWrapper recentWrapper, RecentResponse recentResponse, SagaRecentResponse sagaRecentResponse) {
        HeadingProperties headingProperties = new HeadingProperties();
        String service = StringUtils.deleteWhitespace(StringUtils.defaultString(recentResponse.getProduct().getService())).toLowerCase();
        String paytype = StringUtils.deleteWhitespace(StringUtils.defaultString(recentResponse.getProduct().getPayType())).toLowerCase();
        String nicknameOrConsumerName = null;

            if (Objects.nonNull(sagaRecentResponse.getNickName())&&StringUtils.isNotEmpty(sagaRecentResponse.getNickName())) {
                nicknameOrConsumerName = sagaRecentResponse.getNickName();
                headingProperties.setLabel(nicknameOrConsumerName);
                log.debug("Nickname: {}", nicknameOrConsumerName);
            } else if (Objects.nonNull(sagaRecentResponse.getConsumerName())&&StringUtils.isNotEmpty(sagaRecentResponse.getConsumerName())) {
                nicknameOrConsumerName = sagaRecentResponse.getConsumerName();
                headingProperties.setLabel(nicknameOrConsumerName);
                log.debug("Consumer name: {}", nicknameOrConsumerName);
            }

        if (StringUtils.equalsIgnoreCase(paytype, CREDITCARD) || (StringUtils.equalsAnyIgnoreCase(service, MOBILE, RENTPAYMENT))) {
            headingProperties.setLabel(nicknameOrConsumerName);
        } else {
            if (Objects.nonNull(nicknameOrConsumerName)&&!nicknameOrConsumerName.isEmpty()) {
                headingProperties.setLabel(sagaRecentResponse.getRechargeNumber1());
            } else
                headingProperties.setLabel(recentResponse.getProduct().getBrand());
        }

         
        recentWrapper.setLocalisationKeyConstant(HEADING1);
        String heading1Color = recentLocalisationKeyResolver.getMessageByWidgetType(recentWrapper, sagaRecentResponse, recentResponse.getProduct());
        headingProperties.setColor(StringUtils.isNotBlank(heading1Color) ? heading1Color : DEFAULT_COLOR);
        log.debug("Setting heading label to: {}", headingProperties.getLabel());
        log.debug("Setting heading color to: {}", headingProperties.getColor());
        return headingProperties;
    }


}
