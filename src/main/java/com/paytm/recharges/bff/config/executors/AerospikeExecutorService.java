package com.paytm.recharges.bff.config.executors;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.micrometer.core.instrument.binder.jvm.ExecutorServiceMetrics;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Configuration
public class AerospikeExecutorService {

    @Value("${interstitial.executor.minPoolSize:5}")
    private int minPoolSize;

    @Value("${interstitial.executor.maxPoolSize:10}")
    private int maxPoolSize;

    @Value("${interstitial.executor.queueSize:100}")
    private int queueSize;

    @Value("${interstitial.executor.keepAlive:60}")
    private int keepAlive;

    @Bean(name = "aerospikeExecutor")
    public ExecutorService aerospikeExecutor(final MeterRegistry registry) {
        ExecutorService executorService = new ThreadPoolExecutor(
                minPoolSize,
                maxPoolSize,
                keepAlive,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<Runnable>(queueSize)
        );
        return ExecutorServiceMetrics.monitor(
                registry,
                executorService,
                "aerospikeExecutor",
                Tags.of("service", "aerospike")
        );
    }
}
