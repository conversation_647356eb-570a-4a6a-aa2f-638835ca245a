package com.paytm.recharges.bff.config.resttemplate;

import com.paytm.recharges.bff.constants.Constants;
import com.paytm.recharges.bff.monitor.MetricsAgent;
import com.paytm.recharges.bff.service.LocalisationManager;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

import static com.paytm.recharges.bff.constants.Constants.CLEVERTAP_RESTTEMPLATE;
import static com.paytm.recharges.bff.constants.Constants.CONFIGURATION_LOCAL;
import static com.paytm.recharges.bff.constants.Constants.CONNECTTIMEOUT;
import static com.paytm.recharges.bff.constants.Constants.MAXCONNECTION;
import static com.paytm.recharges.bff.constants.Constants.READTIMEOUT;
import static com.paytm.recharges.bff.constants.Constants.REQUESTTIMEOUT;

@Configuration
public class CleverTapTemplate extends AbstractRestConfiguration {
    private static CustomLogger log = CustomLogManager.getLogger(CleverTapTemplate.class);
    @Value("${recharge.ct.config.http.max_connection}")
    private int maxConnection;

    @Value("${recharge.ct.config.http.connect_timeout}")
    private int connectTimeout;

    @Value("${recharge.ct.config.http.read_timeout}")
    private int readTimeout;

    @Value("${recharge.ct.config.http.request_timeout}")
    private int requestTimeout;

    @Autowired
    LocalisationManager localisationManager;

    @Autowired
    MetricsAgent metricsAgent;


    @Bean("clevertapTemplate")
    public RestTemplate getCleverTapTemplate() {
        return new RestTemplate(createRequestFactory());
    }

    @Override
    public RestConnectionDetails getConnectionDetails() {
        String [] keys = new String[] {Constants.LANGUAGE_ENGLISH,CLEVERTAP_RESTTEMPLATE,CONFIGURATION_LOCAL};
        HashMap<String,String> payload=new HashMap<>();
        String cleverTapConfig=localisationManager.getMessage(keys, true, payload);
        Map<String,Integer> configMap=new HashMap<>();
        configMap.put(MAXCONNECTION,maxConnection);
        configMap.put(CONNECTTIMEOUT,connectTimeout);
        configMap.put(READTIMEOUT,readTimeout);
        configMap.put(REQUESTTIMEOUT,requestTimeout);
        if(!StringUtils.isEmpty(cleverTapConfig)) {

            try {
                JSONObject cleverTapObject = new JSONObject(cleverTapConfig);
                Map<String, Object> cleverTapMap = cleverTapObject.toMap();

                configMap.put(MAXCONNECTION, Integer.parseInt(cleverTapMap.get(MAXCONNECTION).toString()));
                configMap.put(CONNECTTIMEOUT, Integer.parseInt(cleverTapMap.get(CONNECTTIMEOUT).toString()));
                configMap.put(READTIMEOUT, Integer.parseInt(cleverTapMap.get(READTIMEOUT).toString()));
                configMap.put(REQUESTTIMEOUT, Integer.parseInt(cleverTapMap.get(REQUESTTIMEOUT).toString()));

            }catch (JSONException | NullPointerException ex){
                log.error("[CleverTapTemplate] : while reading from localization panel" + ex);
            }
        }
        log.info("[CleverTapTemplate] :  Values for clevertTaptemplate is "+cleverTapConfig);

        return new RestConnectionDetails(configMap.get(MAXCONNECTION), configMap.get(CONNECTTIMEOUT), configMap.get(READTIMEOUT), configMap.get(REQUESTTIMEOUT));
    }
}
