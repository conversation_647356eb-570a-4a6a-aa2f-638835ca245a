package com.paytm.recharges.bff.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration("interstitialKafkaConfig")
@ConfigurationProperties("interstitial.kafka")
public class InterstitialKafkaConfig {
    private String bootstrapServers;
    private String topicName15sRetry;
    private String topicName30mRetry;
    private String topicNameDwh;
}
