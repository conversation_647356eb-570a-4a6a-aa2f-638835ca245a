package com.paytm.recharges.bff.config.executors;

import com.paytm.recharges.bff.constants.Constants;
import com.paytm.recharges.bff.monitor.MetricsAgent;
import com.paytm.recharges.bff.service.LocalisationManager;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.micrometer.core.instrument.binder.jvm.ExecutorServiceMetrics;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.paytm.recharges.bff.constants.Constants.CLEVERTAP_EXECUTOR;
import static com.paytm.recharges.bff.constants.Constants.CONFIGURATION_LOCAL;
import static com.paytm.recharges.bff.constants.Constants.KEEPALIVE;
import static com.paytm.recharges.bff.constants.Constants.MAXPOOLSIZE;
import static com.paytm.recharges.bff.constants.Constants.MINPOOLSIZE;
import static com.paytm.recharges.bff.constants.Constants.QUEUESIZE;

@Configuration
public class CleverTapExecutorService {
    private static final CustomLogger log = CustomLogManager.getLogger(CleverTapExecutorService.class);

    @Value("${recharge.ct.config.minPoolSize}")
    private int minPoolSize;

    @Value("${recharge.ct.config.maxPoolSize}")
    private int maxPoolSize;

    @Value("${recharge.ct.config.queueSize}")
    private int queueSize;

    @Value("${recharge.ct.config.keepAlive}")
    private int keepAlive;


    @Autowired
    LocalisationManager localisationManager;

    @Autowired
    MetricsAgent metricsAgent;

    @Bean
    public ExecutorService cleverTapExecutor(final MeterRegistry registry) {
            String [] keys = new String[] {Constants.LANGUAGE_ENGLISH,CLEVERTAP_EXECUTOR,CONFIGURATION_LOCAL};
            HashMap<String,String> payload=new HashMap<>();
            String cleverTapConfig=localisationManager.getMessage(keys, true, payload);
            Map<String,Integer> configMap=new HashMap<>();
            configMap.put(MINPOOLSIZE,minPoolSize);
            configMap.put(MAXPOOLSIZE,maxPoolSize);
            configMap.put(QUEUESIZE,queueSize);
            configMap.put(KEEPALIVE,keepAlive);
            if(!StringUtils.isEmpty(cleverTapConfig)) {
                try {
                    JSONObject cleverTapObject = new JSONObject(cleverTapConfig);
                    Map<String, Object> cleverTapMap = (Map<String, Object>) cleverTapObject.toMap();
                    configMap.put(MINPOOLSIZE, Integer.parseInt(cleverTapMap.get(MINPOOLSIZE).toString()));
                    configMap.put(MAXPOOLSIZE, Integer.parseInt(cleverTapMap.get(MAXPOOLSIZE).toString()));
                    configMap.put(QUEUESIZE, Integer.parseInt(cleverTapMap.get(QUEUESIZE).toString()));
                    configMap.put(KEEPALIVE, Integer.parseInt(cleverTapMap.get(KEEPALIVE).toString()));
                }catch (JSONException  | NullPointerException ex){
                    log.error("[CleverTapExecutorService] : while reading from localization panel" + ex);
                }
            }
            log.info("[CleverTapExecutorService] :  Values for clevertTapexecutorservice is "+cleverTapConfig);

            ExecutorService executorService = new ThreadPoolExecutor(minPoolSize, maxPoolSize, keepAlive, TimeUnit.SECONDS, new LinkedBlockingQueue<Runnable>(queueSize));
            return ExecutorServiceMetrics.monitor(registry,executorService, "cleverTapExecutor", Tags.of("key", "value"));
        }

    }

