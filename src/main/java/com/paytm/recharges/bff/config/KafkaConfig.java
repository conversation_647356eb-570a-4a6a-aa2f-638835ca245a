package com.paytm.recharges.bff.config;

import com.paytm.recharges.bff.config.properties.InterstitialKafkaConfig;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;

import java.util.HashMap;
import java.util.Map;

@Configuration
public class KafkaConfig {

    private final InterstitialKafkaConfig interstitialKafkaConfig;

    @Autowired
    public KafkaConfig(InterstitialKafkaConfig interstitialKafkaConfig) {
        this.interstitialKafkaConfig = interstitialKafkaConfig;
    }

    @Bean
    public KafkaTemplate<String, String> interstitialKafkaTemplate() {
        return new KafkaTemplate<>(interstitialProducerFactory());
    }

    private ProducerFactory<String, String> interstitialProducerFactory() {
        return new DefaultKafkaProducerFactory<>(interstitialKafkaProperties());
    }

    private Map<String, Object> interstitialKafkaProperties() {
        Map<String, Object> props = new HashMap<>();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, interstitialKafkaConfig.getBootstrapServers());
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        return props;
    }

}
