package com.paytm.recharges.bff.utils;

import com.paytm.recharges.bff.config.properties.AES256Configurations;
import com.paytm.recharges.bff.exceptions.AES256Exception;
import com.paytm.recharges.bff.monitor.MetricsAgent;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Objects;

import static com.paytm.recharges.bff.constants.Constants.AES256_ENCRYPTION_ERROR;
import static com.paytm.recharges.bff.constants.Constants.AES256_DECRYPTION_ERROR;

@Component
@DependsOn("vaultConfiguration")
public class AESUtil {
	private static final CustomLogger logger = CustomLogManager.getLogger(AESUtil.class);

	private static final String ALGORITHM = "AES/CTR/NoPadding";
	private final AES256Configurations aes256Configurations;
	private final MetricsAgent metricsAgent;

	@Autowired
	public AESUtil(AES256Configurations aes256Configurations, MetricsAgent metricsAgent) {
		this.aes256Configurations = aes256Configurations;
		this.metricsAgent = metricsAgent;
	}

	public String encrypt(String text) throws AES256Exception {
		try {
			return encrypt(text, aes256Configurations.getEncryptionPassword(), aes256Configurations.getEncryptionIvHex());
		} catch (AES256Exception e) {
			logger.error("Error in encrypting the request", e);
			metricsAgent.incrementEventCount(AES256_ENCRYPTION_ERROR);
			throw new AES256Exception("Error in encrypting the request");
		}
	}

	public static String encrypt(String text, String key, String ivHex) {
		try {
			SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "AES");
			IvParameterSpec iv = new IvParameterSpec(hexStringToByteArray(ivHex));

			Cipher cipher = Cipher.getInstance(ALGORITHM);
			cipher.init(Cipher.ENCRYPT_MODE, secretKey, iv);

			byte[] encryptedBytes = cipher.doFinal(text.getBytes(StandardCharsets.UTF_8));
			return Base64.getEncoder().encodeToString(encryptedBytes);
		} catch (Exception e) {
			throw new AES256Exception("Encryption failed");
		}
	}

	private static byte[] hexStringToByteArray(String s) {
		int len = s.length();
		byte[] data = new byte[len / 2];
		for (int i = 0; i < len; i += 2) {
			data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4)
				+ Character.digit(s.charAt(i + 1), 16));
		}
		return data;
	}

	public String decrypt(String encryptedData) throws AES256Exception{
		if(Objects.isNull(encryptedData)) return null;
		try {
			return decrypt(encryptedData, aes256Configurations.getEncryptionPassword(), aes256Configurations.getEncryptionIvHex());
		} catch (AES256Exception e) {
			logger.error("Error in decryption the request", e);
			metricsAgent.incrementEventCount(AES256_DECRYPTION_ERROR);
			throw new AES256Exception("Error in decrypting the request");
		}
	}

	public static String decrypt(String encryptedText, String key, String ivHex) {
		try {
			SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "AES");
			IvParameterSpec iv = new IvParameterSpec(hexStringToByteArray(ivHex));

			Cipher cipher = Cipher.getInstance(ALGORITHM);
			cipher.init(Cipher.DECRYPT_MODE, secretKey, iv);

			byte[] decodedBytes = Base64.getDecoder().decode(encryptedText);
			byte[] originalBytes = cipher.doFinal(decodedBytes);
			return new String(originalBytes, StandardCharsets.UTF_8);
		} catch (Exception e) {
			logger.error("Error in decrypting the request", e);
			throw new AES256Exception("Decryption failed");
		}
	}
}



