package com.paytm.recharges.bff.utils;

import com.aerospike.client.AerospikeClient;
import com.aerospike.client.Bin;
import com.aerospike.client.Key;
import com.aerospike.client.Operation;
import com.aerospike.client.Record;
import com.aerospike.client.policy.WritePolicy;
import com.paytm.recharges.bff.constants.Constants;
import com.paytm.recharges.bff.constants.InterstitialConstants;
import com.paytm.recharges.bff.datalayer.cache.ServiceConfigCache;
import com.paytm.recharges.bff.datalayer.dto.request.InterstitialRequest;
import com.paytm.recharges.bff.datalayer.dto.response.ApiResponse;
import com.paytm.recharges.bff.datalayer.model.CacheData;
import com.paytm.recharges.bff.monitor.MetricsAgent;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.paytm.recharges.bff.constants.Constants.INTERSTITIAL_AEROSPIKE_UPDATE_COUNT;
import static com.paytm.recharges.bff.constants.Constants.INTERSTITIAL_SEEN_COUNT_BIN;


@Component
public class InterstitialUtils {

    private static final CustomLogger log = CustomLogManager.getLogger(InterstitialUtils.class);

    private final AerospikeClient aerospikeClient;
    private final MetricsAgent metricsAgent;

    @Autowired
    public InterstitialUtils(AerospikeClient aerospikeClient, MetricsAgent metricsAgent) {
        this.aerospikeClient = aerospikeClient;
        this.metricsAgent = metricsAgent;
    }

    public List<CacheData> updateInterstitialCache(InterstitialRequest interstitialRequest) {
        List<CacheData> cacheDataList = new ArrayList<>();
        // Increment Monthly Counter
        cacheDataList.add(updateCounterAndLog(generateMonthKey(interstitialRequest.getCustomerId()), (int) DateUtil.getEndOfMonthTTL()));
        // Increment Daily Counter
        cacheDataList.add(updateCounterAndLog(generateDayKey(interstitialRequest.getCustomerId()), (int) DateUtil.getEndOfDayTTL()));
        // Increment Bill Counter
        String billKey = buildBillKeyFromRequest(interstitialRequest);
        int billTtl = DateUtil.getTTLFromDate(interstitialRequest.getDueDate(), Constants.DUE_DATE_FORMAT);
        cacheDataList.add(updateCounterAndLog(billKey, billTtl));

        // Set bill cool off cache
        Integer coolOffDays = ServiceConfigCache.getInstance().getInteger(Constants.COOL_OFF_DAYS_KEY);
        coolOffDays = Objects.isNull(coolOffDays) || coolOffDays < 1 ? 1 : coolOffDays;// Ensure at least 1 day
        int coolOffTtl = coolOffDays * 24 * 60 * 60;
        cacheDataList.add(updateCounterAndLog(billKey + InterstitialConstants.INTERSTITIAL_BILLKEY_COOL_OFF_SUFFIX, coolOffTtl));
        return cacheDataList;
    }

    public CacheData updateCounterAndLog(String keySuffix, int expiration) {
        Key key = new Key(Constants.BFF_NAMESPACE, Constants.INTERSTITIAL_SET, keySuffix);
        WritePolicy policy = new WritePolicy();
        policy.expiration = expiration;
        Operation increment = Operation.add(new Bin(INTERSTITIAL_SEEN_COUNT_BIN, 1));
        metricsAgent.incrementEventCount(INTERSTITIAL_AEROSPIKE_UPDATE_COUNT);
        Record rec = aerospikeClient.operate(policy, key, increment, Operation.get(INTERSTITIAL_SEEN_COUNT_BIN));
        Integer seenCount = null;
        Integer ttl = null;
        if (rec != null) {
            seenCount = rec.getInt(INTERSTITIAL_SEEN_COUNT_BIN);
            ttl = rec.getTimeToLive();
            log.info("count for {} is {} and TTL is {}", keySuffix, seenCount, ttl);
        }
        return new CacheData(keySuffix, seenCount, ttl);
    }

    /**
     * Builds a string in the format:
     * customerId_rechargeNumber_operator_service_duedate_planbucket
     * from the given InterstitialRequest.
     */
    public static String buildBillKeyFromRequest(InterstitialRequest request) {
        return String.format(Constants.INTERSTITIAL_BILL_KEY_FORMAT,
            request.getCustomerId(),
            request.getRechargeNumber(),
            request.getOperator(),
            request.getService(),
            request.getDueDate(),
            request.getPlanBucket()
        );
    }

    /**
     * Utility to create a success ApiResponse for interstitial impression.
     */
    public static ApiResponse createSuccessResponse() {
        ApiResponse apiResponse = new ApiResponse();
        apiResponse.setStatus(Constants.INTERSTITIAL_IMPRESSION_SUCCESS_STATUS);
        apiResponse.setStatusCode(Constants.INTERSTITIAL_IMPRESSION_SUCCESS_CODE);
        apiResponse.setMessage(Constants.INTERSTITIAL_IMPRESSION_SUCCESS_MSG);
        return apiResponse;
    }

    /**
     * Utility to create a failure ApiResponse for interstitial impression.
     */
    public static ApiResponse createFailureResponse() {
        ApiResponse apiResponse = new ApiResponse();
        apiResponse.setStatus(Constants.INTERSTITIAL_IMPRESSION_FAILURE_STATUS);
        apiResponse.setStatusCode(Constants.INTERSTITIAL_IMPRESSION_FAILURE_CODE);
        apiResponse.setMessage(Constants.INTERSTITIAL_IMPRESSION_FAILURE_MSG);
        return apiResponse;
    }

    /**
     * Utility to create a success ApiResponse for Kafka event.
     */
    public static ApiResponse createKafkaSuccessResponse() {
        ApiResponse apiResponse = new ApiResponse();
        apiResponse.setStatus(Constants.INTERSTITIAL_KAFKA_SUCCESS_STATUS);
        apiResponse.setStatusCode(Constants.INTERSTITIAL_KAFKA_SUCCESS_CODE);
        apiResponse.setMessage(Constants.INTERSTITIAL_KAFKA_SUCCESS_MSG);
        return apiResponse;
    }

    public static String generateMonthKey(Long customerId) {
        return String.format("%s_%s", customerId, DateUtil.getCurrentMonth());  // Format: custId_MMM
    }

    public static String generateDayKey(Long customerId) {
        return String.format("%s_%s", customerId, DateUtil.getCurrentDayOfTheMonth());  // Format: custId_DDMMM
    }

}
