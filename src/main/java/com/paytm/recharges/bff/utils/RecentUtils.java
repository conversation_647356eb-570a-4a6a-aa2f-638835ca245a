package com.paytm.recharges.bff.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.recentutils.service.NickNameService;
import com.paytm.recharges.bff.constants.Constants;
import com.paytm.recharges.bff.constants.DateFormat;
import com.paytm.recharges.bff.datalayer.cache.CVRProductCache;
import com.paytm.recharges.bff.datalayer.cache.ServiceConfigCache;
import com.paytm.recharges.bff.datalayer.dto.ActiveInactivePidMapCache;
import com.paytm.recharges.bff.datalayer.dto.response.SagaRecentResponse;
import com.paytm.recharges.bff.datalayer.model.Product;
import com.paytm.recharges.bff.datalayer.model.RecentWrapper;
import com.paytm.recharges.bff.enums.BillState;
import com.paytm.recharges.bff.enums.EventState;
import com.paytm.recharges.bff.enums.EventType;
import com.paytm.recharges.bff.enums.PayType;
import com.paytm.recharges.bff.enums.ProductCategory;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.recharges.saga_client.datalayer.dto.response.SagaFavResponsePOJO;
import com.paytm.recharges.saga_client.datalayer.dto.response.SagaLastTwoRecentsResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.paytm.recharges.bff.constants.Constants.BILL_DATE_FORMAT_FOR_DUE_BILL;
import static com.paytm.recharges.bff.constants.Constants.CLP_SORTER_ROLLOUT_PERCENTAGE;
import static com.paytm.recharges.bff.constants.Constants.DAY_DIFF_FROM_DUEDATE;
import static com.paytm.recharges.bff.constants.Constants.MOBILE;
import static com.paytm.recharges.bff.constants.Constants.MOBILE_PREPAID;
import static com.paytm.recharges.bff.constants.Constants.PAYTM_POSTPAID_MIN_VERSION;
import static com.paytm.recharges.bff.constants.Constants.PENALTY_AMOUNT;
import static com.paytm.recharges.bff.constants.Constants.RENT_PAYMENT;
import static com.paytm.recharges.bff.constants.Constants.SERVICE_BUSINESS_PAYMENT;
import static com.paytm.recharges.bff.constants.Constants.SERVICE_TUITION_FEES;
import static com.paytm.recharges.bff.constants.DateFormat.DATE_TIME_FORMAT_2;
import static com.paytm.recharges.bff.constants.DateFormat.DATE_TIME_FORMAT_3;
import static com.paytm.recharges.bff.constants.RecentConstants.PayloadRelatedConstants.AUTOMATICDATE;
import static com.paytm.recharges.bff.constants.RecentConstants.PayloadRelatedConstants.AUTOMATIC_AMOUNT;
import static com.paytm.recharges.bff.constants.RecentConstants.PayloadRelatedConstants.BILLDUEDATE;
import static com.paytm.recharges.bff.constants.RecentConstants.PayloadRelatedConstants.CREATEDAT;
import static com.paytm.recharges.bff.constants.RecentConstants.PayloadRelatedConstants.CURRENTOUTSTANDINGAMOUNT;
import static com.paytm.recharges.bff.constants.RecentConstants.PayloadRelatedConstants.DUEAMOUNT;
import static com.paytm.recharges.bff.constants.RecentConstants.PayloadRelatedConstants.MARKASPAIDDATE;
import static com.paytm.recharges.bff.constants.RecentConstants.PayloadRelatedConstants.MINDUEAMOUNT;
import static com.paytm.recharges.bff.constants.RecentConstants.PayloadRelatedConstants.PENALTYAMOUNT;
import static com.paytm.recharges.bff.constants.RecentConstants.PayloadRelatedConstants.TXNAMOUNT;
import static com.paytm.recharges.bff.constants.RecentConstants.PayloadRelatedConstants.TXNDATE;
import static com.paytm.recharges.bff.constants.ServiceConfigConstants.VRN_TTL_NOT_IN_TOP_BANK;

public class RecentUtils {

    private static final CustomLogger log = CustomLogManager.getLogger(RecentUtils.class);

    public static ObjectMapper mapper = new ObjectMapper();

    public static String getUniqueKey(SagaRecentResponse recent, List<String> includeOperatorInKeyService) {
        StringBuilder key = new StringBuilder("");
        Product product = CVRProductCache.getInstance().getProductDetails(Long.valueOf(recent.getPid()));
        if(recent.getEventType().toString().equalsIgnoreCase(EventType.SMART_RECENT.toString())){
            key.append(product.getOperator()).append(Constants.DELIMATOR).append(recent.getEventState()).append(Constants.DELIMATOR).append(product.getService());
        }
        else if (Constants.CREDIT_CARD.equalsIgnoreCase(product.getPayType())) {
            if (Objects.nonNull(CVRProductCache.getInstance().getCustomOperatorForCreditCard(Long.valueOf(recent.getPid())))) {
                key.append(recent.getRechargeNumber1()).append(Constants.DELIMATOR).append(product.getService()).append(Constants.DELIMATOR).append(CVRProductCache.getInstance().getCustomOperatorForCreditCard(Long.valueOf(recent.getPid())));
            }
            else{
                key.append(recent.getRechargeNumber1()).append(Constants.DELIMATOR).append(product.getService()).append(Constants.DELIMATOR).append(product.getOperator());
            }
        } else if (includeOperatorInKeyService.contains(product.getService())) {
            key.append(recent.getRechargeNumber1()).append(Constants.DELIMATOR).append(product.getService()).append(Constants.DELIMATOR).append(product.getOperator());
        } else if(MOBILE.equalsIgnoreCase(product.getService()) && recent.getEventState().startsWith("DATA_EXHAUST_")){
            key.append(recent.getRechargeNumber1())
                    .append(Constants.DELIMATOR).append(product.getService())
                    .append(Constants.DELIMATOR).append(recent.getBill().getPlan_bucket());
        } else if(isWhitelistedCustId(recent.getCustomerId())  && Constants.MOBILE_PREPAID.equalsIgnoreCase(product.getPayType()) && ProductCategory.MOBILE.getName().equalsIgnoreCase(product.getService())){
            log.info("getUniqueKey::isWhitelistedCustId {}",recent.getCustomerId());
            String planBucket=Objects.nonNull(recent.getBill().getPlan_bucket())?recent.getBill().getPlan_bucket():"";
            key.append(recent.getRechargeNumber1()).append(Constants.DELIMATOR).append(product.getService()).append(Constants.DELIMATOR).append(planBucket);
        }else
            key.append(recent.getRechargeNumber1()).append(Constants.DELIMATOR).append(product.getService());


        return key.toString();
    }


    public static boolean isRecentSkippable(SagaRecentResponse recent, List<String> serviceAvailable) {
        Product product = CVRProductCache.getInstance().getProductDetails(Long.valueOf(recent.getPid()));
        // log.info("isRecentSkippable :: product :: " + product);
        log.info("isRecentSkippable :: serviceAvailable :: " + serviceAvailable);
        log.info("isRecentSkippable :: product.getService() :: " + product.getService());
        // log.info("isRecentSkippable :: product.getService().toLowerCase() :: " + product.getService().toLowerCase());
        // log.info("isRecentSkippable :: serviceAvailable.contains(product.getService().toLowerCase()) :: " + serviceAvailable.contains(product.getService().toLowerCase()));
        // log.info("isRecentSkippable :: !CollectionUtils.isEmpty(serviceAvailable) :: " + !CollectionUtils.isEmpty(serviceAvailable));
        // log.info("isRecentSkippable :: !serviceAvailable.contains(product.getService().toLowerCase()) :: " + !serviceAvailable.contains(product.getService().toLowerCase()));
        // log.info("isRecentSkippable :: !CollectionUtils.isEmpty(serviceAvailable) && !serviceAvailable.contains(product.getService().toLowerCase()) :: " + (!CollectionUtils.isEmpty(serviceAvailable) && !serviceAvailable.contains(product.getService().toLowerCase())));

        if(product==null || (!CollectionUtils.isEmpty(serviceAvailable)
                && !serviceAvailable.contains(product.getService().toLowerCase()))){
            return true;
        }

        //Checking If Operator is allowed for reco (this will only exclude reco in ruReminder response, it will not affect thin banner response, to update thin banner response additional dev needs to be done)
        if (Boolean.FALSE.equals(isOperatorAllowedForReco(product))){
            return true;
        }
        //Skipping in case of old bill flow
        if (recent.getEventState().equals(EventState.OLD_BILL_PENALTY.toString()) || recent.getEventState().equals(EventState.OLD_BILL_AVOID_DISCONNECTION.toString())) {
            return false;
        }
        if(recent.getEventType().equals(EventType.SMS_CARD) && (ObjectUtils.isEmpty(recent.getBill()) || StringUtils.isEmpty(recent.getBill().getBillDueDate()))) {
            // partial bill case
            if(Objects.nonNull(CVRProductCache.getInstance().getProductDetails(Long.valueOf(recent.getPid()))) && getPartialBillEnabledCategories().contains(CVRProductCache.getInstance().getProductDetails(Long.valueOf(recent.getPid())).getCategoryId().toString()))
                return false;
            return true;
        }

        if((Objects.nonNull(recent.getAutomaticState()) && recent.getAutomaticState() ==5) || recent.getEventState().equals(com.paytm.recharges.bff.enums.EventState.RENEW_AUTOMATIC.toString()))
            return false;

        //Skip  bill for which are not in  range D-5 and D+3 configuratble
        if ((recent.getEventType().compareTo(EventType.SMART_RECENT))!=0 && Constants.MOBILE_PREPAID.equalsIgnoreCase(product.getPayType()) && isPrepaidBillNotInRange(recent)) {
          //  pushCountToDD(FREQUENT_ORDER, "IGNORE_PREPAID", monitoringClient);
            return true;
        }
        Date dueDate = (recent.getEventType().compareTo(EventType.SMART_RECENT))==0
                        ? DateUtil.stringToDate(recent.getDate(), DateFormat.DATE_TIME_FORMAT_2)
                        : DateUtil.stringToDate(recent.getBill().getBillDueDate(), DateFormat.DATE_TIME_FORMAT_2);
        //skips service wise data which are not in given date range.
       Map<String,Map<String,Integer>> serviceWiseConfig =  getServiceWiseStartEndDay();

        if ((EventState.LOW_BALANCE.name().equals(recent.getEventState()) || EventState.LOW_BALANCE_NO_AMOUNT.name().equals(recent.getEventState())) && serviceWiseConfig.containsKey(product.getService() + "_LOW_BALANCE")) {
            Map<String,Integer> startEndDay = serviceWiseConfig.get(product.getService() + "_LOW_BALANCE");
            Date endDateRange = DateUtils.addHours(new Date(), startEndDay.get("startHours"));
            Date startDateRange = DateUtils.addHours(new Date(), -startEndDay.get("endHours"));
            if(dueDate.compareTo(endDateRange) > 0|| dueDate.compareTo(startDateRange) < 0){
                return true;
            }else{
                return false;
            }
        }

        if(serviceWiseConfig.containsKey(product.getService())){
            Map<String,Integer> startEndDay = serviceWiseConfig.get(product.getService());
            Date endDateRange = DateUtil.getEndOfDayDate(DateUtils.addDays(new Date(), startEndDay.get("startDays")));
            if("mobile".equalsIgnoreCase(product.getService())
                    && Constants.MOBILE_PREPAID.equalsIgnoreCase(product.getPayType())
                    && recent.getEventState().startsWith("DATA_EXHAUST_")){
                return isDataExhaustInvalid(dueDate);
            }
            Date startDateRange = DateUtil.getZeroTimeDate(DateUtils.addDays(new Date(), -startEndDay.get("endDays")));
            if(dueDate.compareTo(endDateRange)>0 || dueDate.compareTo(startDateRange)<0){
                return true;
            }
        }

        Date endDateRange = DateUtil.getEndOfDayDate(DateUtils.addDays(new Date(), getPostpaidStartDay()));

        Date startDateRange = DateUtil.getZeroTimeDate(DateUtils.addDays(new Date(), -getPostpaidEndDay()));
        if("mobile".equalsIgnoreCase(product.getService())
                && Constants.MOBILE_PREPAID.equalsIgnoreCase(product.getPayType())
                && recent.getEventState().startsWith("DATA_EXHAUST_")){
            return isDataExhaustInvalid(dueDate);
        }
        //Skip non prepaid and non configured in service wise bill for which current date not in range D-30 and D+3 configuratble

        if ((!(serviceWiseConfig.keySet().contains(product.getService())))
                && Objects.nonNull(dueDate) && (!(Constants.MOBILE_PREPAID.equalsIgnoreCase(product.getPayType()))
                && (dueDate.compareTo(endDateRange)>0 || dueDate.compareTo(startDateRange)<0))) {
            return true;
        }

        return false;
    }

    private static boolean isOperatorDisabledForReco(Product productInfo){
        Map<String,List<String>> disabledOperatorsConfig = JsonUtil.convertObjectToMap(ServiceConfigCache.getInstance().get(Constants.DISABLED_OPERATORS_FOR_RECO));
        if(CollectionUtils.isEmpty(disabledOperatorsConfig) || CollectionUtils.isEmpty(disabledOperatorsConfig.get(productInfo.getService().toLowerCase()))){
            return false;
        }

        if(disabledOperatorsConfig.get(productInfo.getService().toLowerCase()).contains(productInfo.getOperator().toLowerCase())) {
            return true;
        }
        return false;
    }

    private static boolean isOperatorAllowedForReco(Product productInfo){
        if(isOperatorDisabledForReco(productInfo)) {
            log.info("Operator disabled for reco because it exists in DISABLED_OPERATORS_FOR_RECO config, service = {} and operator ={}", productInfo.getService(), productInfo.getOperator());
            return false;
        }
        Map<String,List<String>> allowedOperatorsConfig = JsonUtil.convertObjectToMap(ServiceConfigCache.getInstance().get(Constants.ALLOWED_OPERATORS_FOR_RECO));
        // There is no support of paytype in config , in future for mobile cases we need to add support for paytype
        if(CollectionUtils.isEmpty(allowedOperatorsConfig) || CollectionUtils.isEmpty(allowedOperatorsConfig.get(productInfo.getService().toLowerCase()))){
            log.info("Operator allowed for reco because either config is null or service is not present in config, service = {} and operator ={}",productInfo.getService(),productInfo.getOperator());
            return true;
        }
        if(allowedOperatorsConfig.get(productInfo.getService().toLowerCase()).contains(productInfo.getOperator().toLowerCase())) {
            log.info("Operator allowed for reco because ot exists in config, service = {} and operator ={}", productInfo.getService(), productInfo.getOperator());
            return true;
        }
        log.info("Operator not allowed, service = {}, operator ={}",productInfo.getService(),productInfo.getOperator());
        return false;
    }

    private static boolean isPrepaidBillNotInRange(SagaRecentResponse recent) {

        Date startDateRange = DateUtil.getZeroTimeDate(DateUtils.addDays(new Date(), -getPrepaidEndDay()));
        Date endDateRange = DateUtil.getEndOfDayDate(DateUtils.addDays(new Date(), getPrepaidStartDay()));
        Date dueDate = DateUtil.stringToDate(recent.getBill().getBillDueDate(), DateFormat.DATE_TIME_FORMAT_2);
        log.debug("startDateRange {} endDateRange {} due date {}", startDateRange, endDateRange, recent.getBill().getBillDueDate());

        return !(dueDate.compareTo(startDateRange) >= 0 && dueDate.compareTo(endDateRange) <= 0);

    }

    public static boolean isDataExhaustInvalid(Date date) {
        Date endOfDayDate = DateUtil.getEndOfDayDate(date);
        Integer ttl = getTTLFromConfigForDataExhaust();
        List<Date> listOfDates=new ArrayList<>();
        Date dateToCompare=null;
        listOfDates.add(endOfDayDate);
        if(Objects.nonNull(ttl) && ttl>0){
            listOfDates.add(DateUtils.addMinutes(date,ttl));
        }else{
            listOfDates.add(date);
        }
        dateToCompare=Collections.min(listOfDates);
        return Math.toIntExact((dateToCompare.getTime() / 1000 - new Date().getTime() / 1000))<0;
    }
    private static Integer getTTLFromConfigForDataExhaust(){
        Integer ttl = null;
        try{
            Object ttlObj = ServiceConfigCache.getInstance().getObject("categoryWiseTtl");
            ObjectMapper mapper = new ObjectMapper();
            if (Objects.nonNull(ttlObj)) {
                Map<String, Integer> ttlmap = mapper.convertValue(ttlObj, Map.class);
                for (Map.Entry<String, Integer> records : ttlmap.entrySet()) {
                    String category = records.getKey();
                    Integer categoryTtl = records.getValue();
                    if ("data_exhaust_minutes".equalsIgnoreCase(category)) {
                        ttl = categoryTtl;
                        break;
                    }
                }
            }
        }catch(Exception e){
            log.error("TTLUtils:: getTTLFromConfig Getting exception while fetching feature config for ttl error",e);
        }
        return ttl;
    }

    private static int getPrepaidEndDay() {

        Integer endDay = ServiceConfigCache.getInstance().getInteger("smartReminderPrepaidEndDays");
        if (endDay == null) {
            endDay = 3;
            log.debug("Invalid config getPrepaidEndDay");
           // pushCountToDD(FREQUENT_ORDER, Constants.INVALID_CONFIG, monitoringClient);
        }
        return endDay;
    }

    private static int getPrepaidStartDay() {

        Integer startDay = ServiceConfigCache.getInstance().getInteger("smartReminderPrepaidStartDays");
        if (startDay == null) {
            startDay = 5;
            log.debug("Invalid config getPrepaidStartDay");
            //pushCountToDD(FREQUENT_ORDER, Constants.INVALID_CONFIG, monitoringClient);
        }
        return startDay;
    }

    private static Map<String, Map<String, Integer>> getServiceWiseStartEndDay() {

        Map<String,Map<String,Integer>> serviceWiseStartEndDay = (Map<String,Map<String,Integer>>)ServiceConfigCache.getInstance().get("serviceWiseStartEndDay");
        if (serviceWiseStartEndDay == null) {
            serviceWiseStartEndDay = new HashMap<>();
            log.debug("Invalid config getPostpaidEndDay");
            // pushCountToDD(FREQUENT_ORDER, Constants.INVALID_CONFIG, monitoringClient);
        }
        return serviceWiseStartEndDay;
    }

    private static int getPostpaidEndDay() {

        Integer endDay = ServiceConfigCache.getInstance().getInteger("smartReminderPostpaidEndDays");
        if (endDay == null) {
            endDay = 3;
            log.debug("Invalid config getPostpaidEndDay");
            // pushCountToDD(FREQUENT_ORDER, Constants.INVALID_CONFIG, monitoringClient);
        }
        return endDay;
    }

    private static int getPostpaidStartDay() {

        Integer endDay = ServiceConfigCache.getInstance().getInteger("smartReminderPostpaidStartDays");
        if (endDay == null) {
            endDay = 31;
            log.debug("Invalid config getPostpaidStartDay");
            // pushCountToDD(FREQUENT_ORDER, Constants.INVALID_CONFIG, monitoringClient);
        }
        return endDay;
    }

    public static int getSmartReminderTTL() {

        Integer ttl = ServiceConfigCache.getInstance().getInteger("smartReminderCacheTTL");
        if (ttl == null) {
            ttl = 30;
            log.debug("Invalid config getSmartReminderTTL");
            // pushCountToDD(FREQUENT_ORDER, Constants.INVALID_CONFIG, monitoringClient);
        }
        return ttl;
    }

    public static int getCarConfigTTL() {

        Integer ttl = ServiceConfigCache.getInstance().getInteger("carConfigCacheTTL");
        if (ttl == null) {
            ttl = 7;
            log.debug("Invalid config getCarConfigTTL");
        }
        return ttl;
    }

    public static int getFuzzyColorConfigTTL() {

        Integer ttl = ServiceConfigCache.getInstance().getInteger("fuzzyColorConfigCacheTTL");
        if (ttl == null) {
            ttl = 30;
            log.debug("Invalid config getCarConfigTTL");
        }
        return ttl;
    }

    public static int getSmartReminderCustomerBillLimit() {

        Integer ttl = ServiceConfigCache.getInstance().getInteger("smartReminderCustomerBillLimit");
        if (ttl == null) {
            ttl = 30;
            log.debug("Invalid config getSmartReminderCustomerBillLimit");
        }
        return ttl;
    }

    public static String getCustomerNameFromSagaResponse(SagaRecentResponse sagaRecentResponse){
        try{
            NickNameService nickNameService = new NickNameService();
            return nickNameService.getNickName(sagaRecentResponse.getNickName(),sagaRecentResponse.getConsumerName(),null);
        }catch(RuntimeException e){
            log.error("[sagaRecentResponse.getConsumerName]:error while fetching nickname ",e);
        }
        return null;
    }


    public static Double gettingPrice(SagaRecentResponse sagaRecentResponse) {
        /*if (NumberFormatUtils.isAmountValid(sagaRecentResponse.getAmount())&&!isNotMarkAsPaidNoDue(sagaRecentResponse)) {
            return sagaRecentResponse.getAmount();
        } else if(NumberFormatUtils.isAmountValid(sagaRecentResponse.getTxnAmount())) {
            return sagaRecentResponse.getTxnAmount();
        }else return null;*/

       /* if (EventType.RECENT.equals(sagaRecentResponse.getEventType()) && NumberFormatUtils.isAmountValid(sagaRecentResponse.getTxnAmount()))
            return sagaRecentResponse.getTxnAmount();
        else {
            return sagaRecentResponse.getAmount();
        }*/
       /* Product product=CVRProductCache.getInstance().getProductDetails(Long.valueOf(sagaRecentResponse.getPid()));
        if(Objects.nonNull(product)&&Objects.nonNull(product.getService())&&product.getService().equalsIgnoreCase("rent payment") ||
                Objects.nonNull(product)&&Objects.nonNull(product.getService())&&product.getService().equalsIgnoreCase("tuition fees") ){
            if(NumberFormatUtils.isAmountValid(sagaRecentResponse.getAmount())){
                return sagaRecentResponse.getAmount();
            }
            return sagaRecentResponse.getTxnAmount();
        }*/

        Product product = CVRProductCache.getInstance().getProductDetails(Long.valueOf(sagaRecentResponse.getPid()));
        if(!(RENT_PAYMENT.equalsIgnoreCase(product.getService()) || SERVICE_BUSINESS_PAYMENT.equalsIgnoreCase(product.getService()) || SERVICE_TUITION_FEES.equalsIgnoreCase(product.getService()))){
            if(NumberFormatUtils.isAmountValid(sagaRecentResponse.getTxnAmount())){
                return sagaRecentResponse.getTxnAmount();
            }
            return sagaRecentResponse.getAmount();
        }else{
            if(NumberFormatUtils.isAmountValid(sagaRecentResponse.getAmount())){
                return sagaRecentResponse.getAmount();
            }
            return sagaRecentResponse.getTxnAmount();
        }

    }

    private static boolean isNotMarkAsPaidNoDue(SagaRecentResponse sagaRecentResponse) {
        if(StringUtils.equalsAnyIgnoreCase(sagaRecentResponse.getBillState(), BillState.NO_DUE.name())&&!StringUtils.equalsAnyIgnoreCase(sagaRecentResponse.getEventState(), EventState.MARK_AS_PAID.name()))
            return true;
        else
            return false;
    }

    public static Map<String, String>  getLocalizationPayload(RecentWrapper recentWrapper, SagaRecentResponse sagaRecentResponse) {
        Map<String, String> payload = recentWrapper.getPayload();

        payload.put(DUEAMOUNT, StringUtils.defaultString(NumberFormatUtils.format(sagaRecentResponse.getAmount())));
        payload.put(AUTOMATIC_AMOUNT,StringUtils.defaultString(NumberFormatUtils.format(sagaRecentResponse.getAutomaticAmount())));
        payload.put(TXNAMOUNT, StringUtils.defaultString(NumberFormatUtils.format(sagaRecentResponse.getTxnAmount())));
        payload.put(AUTOMATICDATE, StringUtils.defaultString(DateUtil.stringDateFormat(sagaRecentResponse.getAutomaticDate(),DATE_TIME_FORMAT_2,DATE_TIME_FORMAT_3)));
        payload.put(TXNDATE, StringUtils.defaultString(DateUtil.stringDateFormat(sagaRecentResponse.getTxnDate(),DATE_TIME_FORMAT_2,DATE_TIME_FORMAT_3)));
        payload.put(CREATEDAT, StringUtils.defaultString(DateUtil.stringDateFormat(sagaRecentResponse.getCreatedAt(),DATE_TIME_FORMAT_2,DATE_TIME_FORMAT_3)));
        payload.put(MARKASPAIDDATE, StringUtils.defaultString(DateUtil.stringDateFormat(sagaRecentResponse.getMarkAsPaidDate(),DATE_TIME_FORMAT_2,DATE_TIME_FORMAT_3)));
        if(sagaRecentResponse.getPid() !=null){
            Product product=CVRProductCache.getInstance().getProductDetails(Long.parseLong(ActiveInactivePidMapCache.getInstance().getActivePid(sagaRecentResponse.getPid())));
            if(Objects.nonNull(product))
                payload.put("operator_label",product.getOperatorLabel());
        }

        if(sagaRecentResponse.getEventState() != null && sagaRecentResponse.getEventState().equals(EventState.OLD_BILL_PENALTY.toString())){
            payload.put(PENALTYAMOUNT, StringUtils.defaultString(NumberFormatUtils.format(Double.valueOf(sagaRecentResponse.getAdditionalInfo().get(PENALTY_AMOUNT)))));
        }

        if(Objects.nonNull(sagaRecentResponse.getBill()) &&  Objects.nonNull(sagaRecentResponse.getBill().getBillAmount())) {
            payload.put(DUEAMOUNT, StringUtils.defaultString(NumberFormatUtils.format(sagaRecentResponse.getBill().getBillAmount())));
        }

        if (Objects.nonNull(sagaRecentResponse.getBill())) {
            log.info(" testing log sagaRecentResponse.getBill() 1: {}", sagaRecentResponse.getBill());
            LocalDateTime dueDate= DateUtil.formatDateIntoLcalDateTime(sagaRecentResponse.getBill().getBillDueDate());
            long dueDateToDayDiff = 0;
            if (dueDate != null) {
                dueDateToDayDiff = Utils.getDayDiffBetweenDates(dueDate.with(LocalTime.MIDNIGHT), LocalDateTime.now().with(LocalTime.MIDNIGHT));
            }
            payload.put(BILLDUEDATE, StringUtils.defaultString(DateUtil.stringDateFormat(sagaRecentResponse.getBill().getBillDueDate(),DATE_TIME_FORMAT_2,BILL_DATE_FORMAT_FOR_DUE_BILL)));
            payload.put(DAY_DIFF_FROM_DUEDATE, String.valueOf(dueDateToDayDiff));
            payload.put(MINDUEAMOUNT, StringUtils.defaultString(NumberFormatUtils.format(sagaRecentResponse.getBill().getMin_due_amount())));
            payload.put(CURRENTOUTSTANDINGAMOUNT, StringUtils.defaultString(NumberFormatUtils.format(sagaRecentResponse.getBill().getCurrentOutstandingAmount())) );
        }
        return payload;
    }

    public static Map<String, String>  getLocalizationPayloadLite(RecentWrapper recentWrapper, SagaLastTwoRecentsResponse sagaRecentResponse) {
        Map<String, String> payload = recentWrapper.getPayload();

        payload.put(DUEAMOUNT, StringUtils.defaultString(NumberFormatUtils.format(sagaRecentResponse.getAmount())));
        payload.put(TXNAMOUNT, StringUtils.defaultString(NumberFormatUtils.format(sagaRecentResponse.getTxnAmount())));
        payload.put(TXNDATE, StringUtils.defaultString(DateUtil.stringDateFormat(sagaRecentResponse.getTxnDate(),DATE_TIME_FORMAT_2,DATE_TIME_FORMAT_3)));
        try{
            payload.put(MARKASPAIDDATE, StringUtils.defaultString(DateUtil.stringDateFormat(sagaRecentResponse.getMarkAsPaidDate(),DATE_TIME_FORMAT_2,DATE_TIME_FORMAT_3)));
        }
        catch(Exception ex){
            log.error("[sagaRecentResponse.getLocalizationPayloadLite]:error  while parsing markaspaid date ",ex);
        }


        if (Objects.nonNull(sagaRecentResponse.getBillDueDate())) {
            log.info(" testing log sagaRecentResponse.getBillDueDate(): {}", sagaRecentResponse.getBillDueDate());
            LocalDateTime dueDate= DateUtil.formatDateIntoLcalDateTime(sagaRecentResponse.getBillDueDate());
            long dueDateToDayDiff = 0;
            if (dueDate != null) {
                dueDateToDayDiff = Utils.getDayDiffBetweenDates(dueDate.with(LocalTime.MIDNIGHT), LocalDateTime.now().with(LocalTime.MIDNIGHT));
            }
            payload.put(BILLDUEDATE, StringUtils.defaultString(DateUtil.stringDateFormat(sagaRecentResponse.getBillDueDate(),DATE_TIME_FORMAT_2,BILL_DATE_FORMAT_FOR_DUE_BILL)));
            payload.put(DAY_DIFF_FROM_DUEDATE, String.valueOf(dueDateToDayDiff));

        }
        return payload;
    }

    public static boolean isRenewAutomaticState(SagaRecentResponse sagaRecentResponse){
        if(Objects.nonNull(sagaRecentResponse.getAutomaticState()) && sagaRecentResponse.getAutomaticState() == 5){
            try{
                if(Objects.nonNull(sagaRecentResponse.getBill()) && Objects.nonNull(sagaRecentResponse.getBill().getBillDueDate())){
                    Date endDateRange = DateUtil.getEndOfDayDate(DateUtils.addDays(new Date(), getPostpaidStartDay()));
                    Date startDateRange = DateUtil.getZeroTimeDate(DateUtils.addDays(new Date(), -getPostpaidEndDay()));
                    Date dueDate = (sagaRecentResponse.getEventType().compareTo(EventType.SMART_RECENT))==0
                            ? DateUtil.stringToDate(sagaRecentResponse.getDate(), DateFormat.DATE_TIME_FORMAT_2)
                            : DateUtil.stringToDate(sagaRecentResponse.getBill().getBillDueDate(), DateFormat.DATE_TIME_FORMAT_2);
                    if (dueDate.compareTo(endDateRange)>0 || dueDate.compareTo(startDateRange)<0) {
                        return true;
                    }

                }
            }
            catch(Exception ex){
                log.error("[sagaRecentResponse.getState]:error  while executing automatic state no due block ",ex);

            }

        }
        return false;
    }

    public static BillState getPartialBillState(SagaRecentResponse sagaRecentResponse) {
        String partialBillState = sagaRecentResponse.getBillState();
        if (Objects.equals(partialBillState, BillState.EXPIRED.name())) {
            return BillState.EXPIRED;
        } else if (Objects.equals(partialBillState, BillState.EXPIRING_SOON.name())) {
            return BillState.EXPIRING_SOON;
        } else if (Objects.equals(partialBillState, BillState.INCOMING_STOPPED.name())) {
            return BillState.INCOMING_STOPPED;
        }
        return null;
    }

    public static boolean isPartialBillState(SagaRecentResponse sagaRecentResponse){
        return Objects.nonNull(CVRProductCache.getInstance().getProductDetails(Long.valueOf(sagaRecentResponse.getPid()))) && getPartialBillEnabledCategories().contains(CVRProductCache.getInstance().getProductDetails(Long.valueOf(sagaRecentResponse.getPid())).getCategoryId().toString()) && sagaRecentResponse.getEventType().equals(EventType.SMS_CARD) && Objects.nonNull(sagaRecentResponse.getBill()) && (Objects.isNull(sagaRecentResponse.getBill().getBillDueDate()) || Objects.isNull(sagaRecentResponse.getBill().getBillAmount()));
    }

    public static boolean isPartialBillStateRU(SagaRecentResponse sagaRecentResponse){
        Product product = CVRProductCache.getInstance().getProductDetails(Long.valueOf(sagaRecentResponse.getPid()));
        return Objects.nonNull(product) && Objects.nonNull(product.getService()) && getRUPartialBillRecoServices().contains(product.getService().toLowerCase()) && sagaRecentResponse.getEventType().equals(EventType.RECENT) && Objects.nonNull(sagaRecentResponse.getBill()) && (Objects.isNull(sagaRecentResponse.getBill().getBillDueDate()));
    }

    public static BillState getState(SagaRecentResponse sagaRecentResponse, SagaFavResponsePOJO sagaFavResponsePOJO) {
        if(Objects.equals(sagaRecentResponse.getEventState() , EventState.PARTIAL_BILL.name())
                && CVRProductCache.getInstance().getProductDetails(Long.valueOf(sagaRecentResponse.getPid())).getService().equalsIgnoreCase(Constants.MOBILE)
                && CVRProductCache.getInstance().getProductDetails(Long.valueOf(sagaRecentResponse.getPid())).getPayType().equalsIgnoreCase(MOBILE_PREPAID)){
            BillState partialBillState = getPartialBillState(sagaRecentResponse);
            if(partialBillState != null){
                return partialBillState;
            }
        }

        if(isRenewAutomaticState(sagaRecentResponse)){
            sagaFavResponsePOJO.setEventState(String.valueOf(EventState.RENEW_AUTOMATIC));
            sagaFavResponsePOJO.setBill(null);
            return BillState.NO_DUE;
        }
        if(isPartialBillState(sagaRecentResponse) || isPartialBillStateRU(sagaRecentResponse)){
            return BillState.valueOf(sagaRecentResponse.getBillState().toUpperCase());
        }

        if(isAutomaticValid(sagaRecentResponse)) {
            return BillState.valueOf(sagaRecentResponse.getBillState().toUpperCase());
        }

        return Objects.nonNull(sagaRecentResponse.getBill()) && Objects.nonNull(sagaRecentResponse.getBill().getBillDueDate())
                    ? getState(DateUtil.stringToDate(sagaRecentResponse.getBill().getBillDueDate(),DateFormat.DATE_TIME_FORMAT_2)
                        , CVRProductCache.getInstance().getProductDetails(Long.valueOf(sagaRecentResponse.getPid())).getPayType()
                        , isAutomaticValid(sagaRecentResponse)) : BillState.NO_DUE;
    }

    public static boolean isAutomaticValid(SagaRecentResponse sagaRecentResponse) {
        return sagaRecentResponse!= null
                && sagaRecentResponse.getAutomaticState() != null
                && 1 == sagaRecentResponse.getAutomaticState().intValue();
    }

    private static BillState getState(Date date, String payType, boolean isAutomatic) {

        if (date == null)
            return BillState.NO_DUE;

        LocalDateTime dueDate = DateUtil.convertToLocalDateTime(date);

        if (!PayType.PREPAID.value.equalsIgnoreCase(payType)) {
            dueDate = dueDate.toLocalDate().atTime(LocalTime.MAX);
        }

        long dayDiff = DateUtil.getDayDiffBetweenDates(LocalDateTime.now(), dueDate);

        if (dayDiff < 0) {

            return BillState.ALREADY_EXPIRED;

        } else if (dayDiff == 0) {

            if (PayType.PREPAID.value.equalsIgnoreCase(payType) && dueDate.isBefore(LocalDateTime.now())) {
                return BillState.ALREADY_EXPIRED_TODAY;
            }

            if (isAutomatic)
                return BillState.EXPIRES_TODAY_AUTOMATIC;

            return BillState.EXPIRES_TODAY;

        } else if (dayDiff >= 2) {

            if (isAutomatic)
                return BillState.WILL_EXPIRE_AUTOMATIC;

            return BillState.WILL_EXPIRE;

        } else if (dayDiff == 1) {

            if (isAutomatic)
                return BillState.EXPIRES_TOMORROW_AUTOMATIC;

            return BillState.EXPIRES_TOMORROW;
        }

        return BillState.NO_DUE;
    }

    public static boolean isPaytmPostpaidCustomerEligible(Long customerId,int percentage,String appVersion){
        try{
            String customerIdStr=customerId.toString();
            boolean response = customerIdStr.length() >= 2 && Integer.parseInt(customerIdStr.subSequence(customerIdStr.length()-2,customerIdStr.length()).toString()) < percentage ? true : false;
            String minVersion=ServiceConfigCache.getInstance().getString(PAYTM_POSTPAID_MIN_VERSION);
            if(response
                    && Objects.nonNull(minVersion)) {
                return isLiveOnVersion(appVersion,minVersion);
            }
            return response;
        }catch (Exception e){
            return false;
        }
    }

    public static boolean isWhitelistedCustId(Long customerId) {
        List<String> whitelistedCustIds=ServiceConfigCache.getInstance().getList("whitelistedCustIdsForMobileNonRu");
        if(Objects.isNull(whitelistedCustIds) || Objects.isNull(customerId)){
            return false;
        }
        return whitelistedCustIds.isEmpty() || whitelistedCustIds.contains(String.valueOf(customerId)) || isLiveForTraffic(customerId);
    }

    private  static boolean isLiveForTraffic(Long customerId){
        Integer percentForLiveTraffic=ServiceConfigCache.getInstance().getInteger("percentForLiveTrafficForMobileNonRu");
        if(Objects.isNull(percentForLiveTraffic) || percentForLiveTraffic <=0 || percentForLiveTraffic >100 ){
            return false;
        }
        int inputPercent=(int) (customerId%100);
        return inputPercent < percentForLiveTraffic;
    }

    public static boolean isLiveForClpSorter(Long customerId){
        Integer percentForLiveTraffic=ServiceConfigCache.getInstance().getInteger(CLP_SORTER_ROLLOUT_PERCENTAGE);
        if(Objects.isNull(percentForLiveTraffic) || percentForLiveTraffic <=0 || percentForLiveTraffic >100 ){
            return false;
        }
        int inputPercent=(int) (customerId%100);
        return inputPercent < percentForLiveTraffic;
    }

    private static boolean isLiveOnVersion(String version, String minVersion) {
        VersionComparable requestVersion = new VersionComparable(version);
        VersionComparable configMinVersion = new VersionComparable(minVersion);
        return requestVersion.compareTo(configMinVersion) >= 0;
    }

    public static ObjectMapper getMapper(){
        return mapper;
    }

    public static List<String> getPartialBillEnabledCategories() {
        List<String> partialBillEnabledCategoryList = JsonUtil.convertObjectToList(ServiceConfigCache.getInstance().get(Constants.PARTIAL_BILL_ENABLED_CATEGORIES));
        if(Objects.nonNull(partialBillEnabledCategoryList)){
            return partialBillEnabledCategoryList;
        }
        return Collections.emptyList();

    }

    public static Integer getVRNTTLNotInTopBank() {

        Integer ttl = ServiceConfigCache.getInstance().getInteger(VRN_TTL_NOT_IN_TOP_BANK);
        if (ttl == null) {
            ttl = 72 * 3600;
            log.debug("Invalid config getVRNTTLNotInTopBank");
        }
        return ttl;
    }

    public static List<String> getRUPartialBillRecoServices() {
        return ServiceConfigCache.getInstance().getRuPartialBillRecoServices();
    }
}
