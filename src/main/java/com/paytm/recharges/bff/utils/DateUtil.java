package com.paytm.recharges.bff.utils;

import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.Objects;
import java.util.TimeZone;

import static com.paytm.recharges.bff.constants.DateFormat.DATE_TIME_FORMAT_2;

public class DateUtil {

    private static final CustomLogger log = CustomLogManager.getLogger(DateUtil.class);

    public static Date stringToDate(String dateStr,String pattern){
        try{
            Date date = new SimpleDateFormat(pattern).parse(dateStr);
            return date;
        }catch(Exception e){
            log.error("DateUtil :: stringToDate error in parsing date dateStr {} pattern {}",dateStr,pattern);
        }
        return null;

    }
    public static String stringDateFormat(String fromDateStr,String frmPattern,String toPattern){
        if(Objects.isNull(fromDateStr)) return null;
        try{
            Date date = new SimpleDateFormat(frmPattern).parse(fromDateStr);
            DateFormat dateFormat = new SimpleDateFormat(toPattern);
            String strDate = dateFormat.format(date);
            return strDate;
        }catch(Exception e){
            log.error("DateUtil :: stringDateFormat error in parsing date fromDateStr {} frmPattern {} toPattern {}",fromDateStr,frmPattern,toPattern);
        }
        return null;

    }

    public static String formatDate(Date date, String format) {
        if (date != null) {
            DateFormat formatter = new SimpleDateFormat(format);
            return formatter.format(date);
        }
        return null;
    }
    public static String formatDate1(String date){
        try{
            Date date1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(date);
            DateFormat dateFormat = new SimpleDateFormat("EEE, dd MMM");
            return dateFormat.format(date1);
        }catch(Exception e){
            log.error("DateUtil.formatDate1 :: Error in parsing date {}",date);
        }
        return null;
    }
    public static String formatDay(String date){
        try{
            Date date1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(date);
            DateFormat dateFormat = new SimpleDateFormat("EEEE");
            return dateFormat.format(date1);
        }catch(Exception e){
            log.error("DateUtil.formatDay :: Error is parsing date to day {}",date);
        }
        return null;
    }
    public static String formatDateTimeinGMT(String date){
        if(Objects.isNull(date))return null;
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
        Date newDate = null;
        try
        {
            newDate = format.parse(date);
        } catch (ParseException e)
        {
            log.error("date utils : execption {}",e);
        }

        SimpleDateFormat gmtFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");

        gmtFormat.setTimeZone(TimeZone.getTimeZone("GMT"));

        return gmtFormat.format(newDate);
    }

    public static String formatDateTimeInGMT(Date date) {
        if(date == null) {
            return null;
        }
        SimpleDateFormat formatter = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss z", Locale.ENGLISH);
        formatter.setTimeZone(TimeZone.getTimeZone("GMT"));
        return formatter.format(date);
    }
    public static Date getZeroTimeDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        date = calendar.getTime();
        return date;
    }

    public static Date getEndOfDayDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        date = calendar.getTime();
        return date;
    }

    public static String formatTodayDate() {

        return LocalDate.now().toString();

    }

    public static LocalDateTime formatDateIntoLcalDateTime(String date) {
        LocalDateTime formattedDate = null;
        try {
            formattedDate = LocalDateTime.parse(date, DateTimeFormatter.ofPattern(DATE_TIME_FORMAT_2));
        } catch (Exception e) {
            log.error("Exception on createAndSetPayload function {}", e);
        }

        return formattedDate;
    }

    public static String formatDate(LocalDate localDate, String pattern) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
            return localDate.format(formatter);
        } catch (Exception e) {
            log.error("Exception on formatDate function {}", e);
        }
        return null;
    }

    public static String formatDateTime(LocalDateTime localDateTime, String pattern) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
            return localDateTime.format(formatter);
        } catch (Exception e) {
            log.error("Exception on formatDateTime function {}", e);
        }
        return null;
    }

    public static LocalDateTime convertToLocalDateTime(Date dateToConvert) {
        return dateToConvert.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
    }

    public static long getDayDiffBetweenDates(LocalDateTime date1, LocalDateTime date2){
        return  ChronoUnit.DAYS.between(date1.toLocalDate(),date2.toLocalDate());
    }

    public static String getCurrentMonth(){
        return LocalDate.now().getMonth().toString();
    }

    public static String getCurrentDayOfTheMonth(){
        return String.valueOf(LocalDate.now().getDayOfMonth());
    }

    // Get TTL for the end of the day (23:59:59)
    public static long getEndOfDayTTL() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime endOfDay = now.withHour(23).withMinute(59).withSecond(59).withNano(0);
        return Duration.between(now, endOfDay).getSeconds();
    }

    // Get TTL for the end of the month (last day of the month, 23:59:59)
    public static long getEndOfMonthTTL() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime endOfMonth = now.with(TemporalAdjusters.lastDayOfMonth()).withHour(23).withMinute(59).withSecond(59).withNano(0);
        return Duration.between(now, endOfMonth).getSeconds();
    }

    /**
     * Returns the TTL (in seconds) from now until the given dueDate string (in the given pattern).
     * If dueDate is in the past or invalid, returns 0.
     */
    public static int getTTLFromDate(String dueDate, String pattern) {
        try {
            LocalDateTime now = LocalDateTime.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
            LocalDate dueLocalDate = LocalDate.parse(dueDate, formatter);
            LocalDateTime dueDateTime = dueLocalDate.atTime(23, 59, 59);
            long seconds = Duration.between(now, dueDateTime).getSeconds();
            return (int) Math.max(seconds, 10);
        } catch (Exception e) {
            log.error("DateUtil.getTTLFromDate :: Error parsing dueDate {} with pattern {}", dueDate, pattern, e);
            return (int) getEndOfMonthTTL();
        }
    }

}

