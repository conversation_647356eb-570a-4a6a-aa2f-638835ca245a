package com.paytm.recharges.bff.utils;

import com.paytm.recharges.bff.constants.Constants;
import com.paytm.recharges.bff.datalayer.dto.request.RechargeVerifyRequest;
import com.paytm.recharges.bff.datalayer.dto.response.RechargeItem;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;

import static com.paytm.recharges.bff.constants.Constants.LANGUAGE_ENGLISH;
import static com.paytm.recharges.bff.constants.Constants.LOCALE_MAP;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;

public class Validator {

    private static final CustomLogger log = CustomLogManager.getLogger(Validator.class);

    public  static boolean isValid(RechargeVerifyRequest verifyRequest,String ... args) {

        if (Objects.isNull(verifyRequest.getCartItems()) || verifyRequest.getCartItems().isEmpty()) {
            return false;
        }

        for (RechargeItem item : verifyRequest.getCartItems()) {
            try {
                Object obj=null;
                for (String arg : args) {
                    if(Arrays.asList(Constants.CONFIGURATION_FIELDS).contains(arg)){
                        Method method = item.getClass().getDeclaredMethod(Constants.GET_CONFIGURATION);
                        Map<String, Object> configurationKey= (Map<String, Object>) method.invoke(item);
                         obj=configurationKey.get(Constants.PRICE);
                    }
                    else{
                        Method method = item.getClass().getDeclaredMethod(arg);
                         obj = method.invoke(item);
                    }
                    if (Objects.isNull(obj)||obj.toString().isEmpty()) {
                        return false;
                    }
                }
            } catch (NoSuchMethodException | InvocationTargetException | IllegalAccessException ex) {
                log.error("No field exist for comparing");
            }


        }
        return true;
    }

    public static String validateAndGetLocale(String locale) {
        if(StringUtils.isNotBlank(locale)) {
            return LOCALE_MAP.getOrDefault(locale.toLowerCase(), LANGUAGE_ENGLISH);
        }

        return LANGUAGE_ENGLISH;
    }


}
