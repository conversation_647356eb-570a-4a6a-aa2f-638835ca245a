package com.paytm.recharges.bff.controller;

import com.paytm.recharges.bff.constants.Constants;
import org.springframework.web.bind.annotation.*;
import com.aerospike.client.AerospikeClient;
import com.aerospike.client.Key;
import com.aerospike.client.Bin;
import com.aerospike.client.Record;
import com.aerospike.client.policy.WritePolicy;
import com.aerospike.client.policy.BatchPolicy;
import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;

@RestController
@RequestMapping("/aerospike-poc")
public class AerospikePocController {

    private final AerospikeClient aerospikeClient;

    @Autowired
    public AerospikePocController(AerospikeClient aerospikeClient) {
        this.aerospikeClient = aerospikeClient;
    }

    @GetMapping("/ping")
    public String ping() {
        return "Aerospike POC Controller is up";
    }

    @PostMapping("/test")
    public TestAerospikeResponse testAerospike(@RequestBody TestAerospikeRequest request) {
        TestAerospikeResponse response = new TestAerospikeResponse();
        response.setBasekey(request.getBasekey());
        response.setOp(request.getOp());
        response.setNok(request.getNok());

        String namespace = Constants.SMARTREMINDER;
        String set = "poc";
        int ttl = 24 * 60 * 60; // 1 day in seconds

        long startTime = System.currentTimeMillis();

        try {
            if (request.isUseBatching() && "get".equalsIgnoreCase(request.getOp())) {
                // Batch GET
                List<Key> keys = new ArrayList<>();
                for (int i = 0; i < request.getNok(); i++) {
                    String keyStr = request.getBasekey() + "_" + i;
                    keys.add(new Key(namespace, set, keyStr));
                }
                BatchPolicy batchPolicy = new BatchPolicy();
                aerospikeClient.get(batchPolicy, keys.toArray(new Key[0]));
            } else if (request.isUseBatching() && "put".equalsIgnoreCase(request.getOp())) {
                // Batch PUT is not natively supported by Aerospike Java client, so do sequential put
                WritePolicy policy = new WritePolicy();
                policy.expiration = ttl;
                for (int i = 0; i < request.getNok(); i++) {
                    String keyStr = request.getBasekey() + "_" + i;
                    Key key = new Key(namespace, set, keyStr);
                    Bin bin = new Bin("val", "value_" + i);
                    aerospikeClient.put(policy, key, bin);
                }
            } else {
                // Non-batch mode
                for (int i = 0; i < request.getNok(); i++) {
                    String keyStr = request.getBasekey() + "_" + i;
                    Key key = new Key(namespace, set, keyStr);
                    if ("put".equalsIgnoreCase(request.getOp())) {
                        WritePolicy policy = new WritePolicy();
                        policy.expiration = ttl;
                        Bin bin = new Bin("val", "value_" + i);
                        aerospikeClient.put(policy, key, bin);
                    } else if ("get".equalsIgnoreCase(request.getOp())) {
                        aerospikeClient.get(null, key);
                    }
                }
            }
        } catch (Exception e) {
            response.setMessage("Error during batch operation: " + e.getMessage());
            long endTime = System.currentTimeMillis();
            response.setTimeTakenMs(endTime - startTime);
            return response;
        }

        long endTime = System.currentTimeMillis();
        response.setMessage(String.format("basekey=%s, op=%s, nok=%d, useBatching=%s, timeTakenMs=%d",
                request.getBasekey(), request.getOp(), request.getNok(), request.isUseBatching(), (endTime - startTime)));
        response.setTimeTakenMs(endTime - startTime);
        return response;
    }

    public static class TestAerospikeRequest {
        private String basekey;
        private String op;
        private int nok;
        private boolean useBatching;

        // getters and setters
        public String getBasekey() { return basekey; }
        public void setBasekey(String basekey) { this.basekey = basekey; }
        public String getOp() { return op; }
        public void setOp(String op) { this.op = op; }
        public int getNok() { return nok; }
        public void setNok(int nok) { this.nok = nok; }
        public boolean isUseBatching() { return useBatching; }
        public void setUseBatching(boolean useBatching) { this.useBatching = useBatching; }
    }

    public static class TestAerospikeResponse {
        private String basekey;
        private String op;
        private int nok;
        private String message;
        private long timeTakenMs;

        // getters and setters
        public String getBasekey() { return basekey; }
        public void setBasekey(String basekey) { this.basekey = basekey; }
        public String getOp() { return op; }
        public void setOp(String op) { this.op = op; }
        public int getNok() { return nok; }
        public void setNok(int nok) { this.nok = nok; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public long getTimeTakenMs() { return timeTakenMs; }
        public void setTimeTakenMs(long timeTakenMs) { this.timeTakenMs = timeTakenMs; }
    }
}
