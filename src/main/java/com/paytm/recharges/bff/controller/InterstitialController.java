package com.paytm.recharges.bff.controller;

import com.paytm.recharges.bff.constants.Constants;
import com.paytm.recharges.bff.constants.InterstitialConstants;
import com.paytm.recharges.bff.constants.Routes;
import com.paytm.recharges.bff.datalayer.dto.request.InterstitialRequest;
import com.paytm.recharges.bff.datalayer.dto.response.ApiResponse;
import com.paytm.recharges.bff.service.InterstitialService;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping(path = Routes.FAVOURITE_BASE_URL)
public class InterstitialController {
    private static final CustomLogger log = CustomLogManager.getLogger(InterstitialController.class);

    private final InterstitialService interstitialService;

    public InterstitialController(InterstitialService interstitialService) {
        this.interstitialService = interstitialService;
    }

    @GetMapping({Routes.INTERSTITIAL_URL, Routes.INTERSTITIAL_URL_RETRY})
    public ResponseEntity<ApiResponse> interstitialImpression(
            @RequestParam(value = InterstitialConstants.PARAM_RECHARGE_NUMBER, required = false) String rechargeNumber,
            @RequestParam(value = InterstitialConstants.PARAM_OPERATOR, required = false) String operator,
            @RequestParam(value = InterstitialConstants.PARAM_CUSTOMER_ID, required = false) Long customerId,
            @RequestParam(value = InterstitialConstants.PARAM_PAY_TYPE, required = false) String payType,
            @RequestParam(value = InterstitialConstants.PARAM_SERVICE, required = false) String service,
            @RequestParam(value = InterstitialConstants.PARAM_PRODUCT_ID, required = false) Long productId,
            @RequestParam(value = InterstitialConstants.PARAM_PLAN_BUCKET, required = false) String planBucket,
            @RequestParam(value = InterstitialConstants.PARAM_AMOUNT, required = false) Double amount,
            @RequestParam(value = InterstitialConstants.PARAM_CIRCLE, required = false) String circle,
            @RequestParam(value = InterstitialConstants.PARAM_EXPIRY, required = false) String expiry,
            @RequestParam(value = InterstitialConstants.PARAM_DUE_DATE, required = false) String dueDate,
            @RequestParam(value = InterstitialConstants.PARAM_REFERENCE_ID, required = false) String referenceId,
            @RequestParam(value = InterstitialConstants.PARAM_RETRY_COUNT, required = false) Integer retryCount
    ){
        log.info("Interstitial impression request for customer: {}, number: {}, operator: {}", 
                customerId, rechargeNumber, operator);
        
        // Validate mandatory string parameters
        List<String> missingParams = validateMandatoryStringParams(
            new String[][]{
                {InterstitialConstants.PARAM_RECHARGE_NUMBER, rechargeNumber},
                {InterstitialConstants.PARAM_CUSTOMER_ID, String.valueOf(customerId)},
                {InterstitialConstants.PARAM_OPERATOR, operator},
                {InterstitialConstants.PARAM_PAY_TYPE, payType},
                {InterstitialConstants.PARAM_SERVICE, service},
                {InterstitialConstants.PARAM_PRODUCT_ID, String.valueOf(productId)},
                {InterstitialConstants.PARAM_DUE_DATE, dueDate}
            }
        );
        
        if (!missingParams.isEmpty()) {
            ApiResponse errorResponse = new ApiResponse();
            errorResponse.setStatus(Constants.FAILURE);
            errorResponse.setStatusCode(HttpStatus.BAD_REQUEST.toString());
            errorResponse.setMessage("Missing or empty mandatory parameters: " + String.join(", ", missingParams));
            log.error("Interstitial request validation failed: {}", errorResponse.getMessage());
            return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
        }
                
        InterstitialRequest interstitialRequest = new InterstitialRequest();
        interstitialRequest.setRechargeNumber(rechargeNumber);
        interstitialRequest.setOperator(operator);
        interstitialRequest.setCustomerId(customerId);
        interstitialRequest.setPayType(payType);
        interstitialRequest.setService(service);
        interstitialRequest.setProductId(productId);
        interstitialRequest.setPlanBucket(planBucket);
        interstitialRequest.setAmount(amount);
        interstitialRequest.setCircle(circle);
        interstitialRequest.setExpiry(expiry);
        interstitialRequest.setDueDate(dueDate);
        interstitialRequest.setReferenceId(referenceId);
        
        // Only set retry count if this is a retry request
        if (retryCount != null) {
            retryCount = Math.max(retryCount, 1);
            interstitialRequest.setRetryCount(retryCount);
            log.info("Processing retry request with count: {}", retryCount);
        }

        return new ResponseEntity<>(interstitialService.registerInterstitialImpression(interstitialRequest), HttpStatus.OK);
    }
    
    /**
     * Validates that mandatory string parameters are not null or empty
     * @param params Array of parameter name and value pairs
     * @return List of parameter names that are missing or empty
     */
    private List<String> validateMandatoryStringParams(String[][] params) {
        List<String> missingParams = new ArrayList<>();
        for (String[] param : params) {
            String paramName = param[0];
            String paramValue = param[1];
            if (paramValue == null || paramValue.trim().isEmpty()) {
                missingParams.add(paramName);
            }
        }
        return missingParams;
    }
}