package com.paytm.recharges.bff.controller;

import com.paytm.recharges.bff.constants.Constants;
import com.paytm.recharges.bff.constants.ErrorTitle;
import com.paytm.recharges.bff.datalayer.dto.request.DeleteFromAllSystemsRequest;
import com.paytm.recharges.bff.datalayer.dto.request.FastagResponse;
import com.paytm.recharges.bff.datalayer.dto.request.FastagVerifyRequest;
import com.paytm.recharges.bff.datalayer.dto.response.CheckoutErrorResponse;
import com.paytm.recharges.bff.datalayer.dto.response.DeleteRecordFromAllSystemsResponse;
import com.paytm.recharges.bff.datalayer.dto.response.FastagApiResponse;
import com.paytm.recharges.bff.datalayer.dto.response.FastagVerifyResponse;
import com.paytm.recharges.bff.datalayer.dto.response.RechargeVerifyResponse;
import com.paytm.recharges.bff.exceptions.RechargesException;
import com.paytm.recharges.bff.exceptions.RestClientException;
import com.paytm.recharges.bff.monitor.MetricsAgent;
import com.paytm.recharges.bff.requesthandler.CheckoutRequestHandler;
import com.paytm.recharges.bff.requesthandler.FastagRequestHandler;
import com.paytm.recharges.bff.requesthandler.VerifyRequestHandler;
import com.paytm.recharges.bff.service.FastTagService;
import com.paytm.recharges.bff.service.impl.DeleteFromAllSystemServiceImpl;
import com.paytm.recharges.bff.utils.JsonUtil;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.recharges.custom_logger.service.LoggerThreadContext;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Enumeration;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;

import static com.paytm.recharges.bff.constants.Constants.CART_VERSION_V4;
import static com.paytm.recharges.bff.constants.Constants.X_USER_ID;
import static com.paytm.recharges.bff.constants.ErrorMessage.ERROR_MESSAGE;

@RestController
@RequestMapping
public class RechargesCheckoutController {

    private static final CustomLogger log = CustomLogManager.getLogger(RechargesCheckoutController.class);

    @Autowired
    private VerifyRequestHandler verifyRequestHandler;

    @Autowired
    private CheckoutRequestHandler checkoutRequestHandler;
    @Autowired
    private DeleteFromAllSystemServiceImpl deleteFromAllSystemService;
    @Autowired
    private MetricsAgent metricsAgent;
    @Autowired
    private FastTagService fastTagService;
    @Lazy
    @Autowired
    private FastagRequestHandler fastagRequestHandler;

    private ResponseEntity<Object> verifyAll(HttpServletRequest request, HttpEntity<Object> httpEntity,
                                             @RequestParam Map<String, Object> requestParams) {
        MultiValueMap<String, String> headers = getHeaders(request);
        bffIndentifierExist(headers, "verify");
        Object body = httpEntity.getBody();

        log.info("controller::verify:: request params{}", requestParams);

        ResponseEntity<Object> response = null;
        try {
            response = verifyRequestHandler.doVerify(body, requestParams, headers);
        } catch (RestClientException e) {
            log.error("controller::verify:: http error {} statusCode {}", e.getMessage(), e.getHttpStatus());
            RechargeVerifyResponse verifyResponse = null;
            try {
                verifyResponse = JsonUtil.fromJson(e.getMessage(), RechargeVerifyResponse.class);
                if(null!=verifyResponse&&null!=verifyResponse.getStatus()){
                    verifyResponse.getStatus().setCode(verifyResponse.getCode());
                    log.info("controller::verifyResponse:: getting statusCode {} ",verifyResponse.getCode());
                }
            } catch (Exception ex) {
                log.info("controller::verify::unable to parse error message {}", e.getMessage());
                log.info("controller::verify::getting status code for exception {}", e.getHttpStatus().value());
                verifyResponse = new RechargeVerifyResponse(ERROR_MESSAGE, ErrorTitle.FAILURE, e.getHttpStatus().value(),
                        CheckoutErrorResponse.getResponseStatus(false, ERROR_MESSAGE, e.getHttpStatus().value()));            }
            response = new ResponseEntity<Object>(verifyResponse, e.getHttpStatus());
        } catch (RechargesException e) {
            log.error("controller::verify:: error occured errorMessage ", e);
            RechargeVerifyResponse verifyResponse = null;
            try {
                verifyResponse = new RechargeVerifyResponse(e.getMessage(), ErrorTitle.FAILURE, e.getHttpStatus().value(),
                        CheckoutErrorResponse.getResponseStatus(false, e.getMessage(), e.getHttpStatus().value()));
                if(null!=verifyResponse&&null!=verifyResponse.getStatus()){
                    verifyResponse.getStatus().setCode(verifyResponse.getCode());
                    log.info("controller::verifyResponse:: getting statusCode {} ",verifyResponse.getCode());
                }
            } catch (Exception ex) {
                log.info("controller::verify:: unable to parse error message {}", ex.getMessage());
                log.info("controller::verify::getting status code for exception {}", e.getHttpStatus().value());
                verifyResponse = new RechargeVerifyResponse(ERROR_MESSAGE, ErrorTitle.FAILURE, e.getHttpStatus().value(),
                        CheckoutErrorResponse.getResponseStatus(false, ERROR_MESSAGE, e.getHttpStatus().value()));
            }
            response = new ResponseEntity<Object>(verifyResponse, e.getHttpStatus());
        } catch (Exception e) {
            log.error("controller::verify:: unknown error occured", e);
            RechargeVerifyResponse verifyResponse = new RechargeVerifyResponse(ERROR_MESSAGE, ErrorTitle.FAILURE,
                    HttpStatus.INTERNAL_SERVER_ERROR.value(), CheckoutErrorResponse.getResponseStatus(false,
                    ERROR_MESSAGE, HttpStatus.INTERNAL_SERVER_ERROR.value()));
            response = new ResponseEntity<Object>(verifyResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        }
        log.info("controller::verifyresponse status code value {}",response.getStatusCode());
        return response;
    }

    private ResponseEntity<Object> checkoutAll(HttpServletRequest request, HttpEntity<Object> httpEntity,
                                           @RequestParam Map<String, Object> requestParams) {

        MultiValueMap<String, String> headers = getHeaders(request);
        bffIndentifierExist(headers, "checkout");
        Object body = httpEntity.getBody();

        ResponseEntity<Object> response = null;
        try {
            response = checkoutRequestHandler.doCheckout(body, requestParams, headers);
        } catch (RestClientException e) {
            log.error("controller::checkout:: error occured for requestParams {} error msg {} statuscode {}", requestParams, e.getMessage(), e.getHttpStatus());
            CheckoutErrorResponse genericResponse = null;
            try {
                genericResponse = JsonUtil.fromJson(e.getMessage(), CheckoutErrorResponse.class);
                if(null!=genericResponse&&null!=genericResponse.getStatus()){
                    genericResponse.getStatus().setCode(genericResponse.getCode());
                }
            } catch (Exception ex) {
                log.info("controller::checkout::uanble to parse error message {}", e.getMessage());
                genericResponse = new CheckoutErrorResponse(ERROR_MESSAGE, ErrorTitle.FAILURE,
                        e.getHttpStatus().value(),
                        CheckoutErrorResponse.getResponseStatus(false, ERROR_MESSAGE, e.getHttpStatus().value()));
            }
            response = new ResponseEntity<Object>(genericResponse, e.getHttpStatus());
        } catch (RechargesException e) {
            log.error("controller::checkout:: error occured errorMessage {}", e.getMessage());
            CheckoutErrorResponse genericResponse = null;
            try {
                genericResponse = JsonUtil.fromJson(e.getMessage(), CheckoutErrorResponse.class);
                if(null!=genericResponse&&null!=genericResponse.getStatus()){
                    genericResponse.getStatus().setCode(genericResponse.getCode());
                }
            } catch (Exception ex) {
                log.info("controller::checkout::uanble to parse error message {}", e.getMessage());
                genericResponse = new CheckoutErrorResponse(ERROR_MESSAGE, ErrorTitle.FAILURE,
                        e.getHttpStatus().value(),
                        CheckoutErrorResponse.getResponseStatus(false, ERROR_MESSAGE, e.getHttpStatus().value()));
            }
            response = new ResponseEntity<Object>(genericResponse, e.getHttpStatus());
        } catch (Exception e) {
            log.error("controller::checkout:: unknown error occured ", e);
            CheckoutErrorResponse genericResponse = new CheckoutErrorResponse(ERROR_MESSAGE, ErrorTitle.FAILURE,
                    HttpStatus.INTERNAL_SERVER_ERROR.value(), CheckoutErrorResponse.getResponseStatus(false,
                    ERROR_MESSAGE, HttpStatus.INTERNAL_SERVER_ERROR.value()));
            response = new ResponseEntity<Object>(genericResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        }
        log.info("controller::checkoutresponse status code value {}",response.getStatusCode());
        return response;
    }

    @PostMapping(value = "/v1/expressrecharge/verify", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Object> verify(HttpServletRequest request, HttpEntity<Object> httpEntity,
            @RequestParam Map<String, Object> requestParams) {

        ResponseEntity<Object> response = verifyAll(request, httpEntity, requestParams);
        return response;
    }

    @PostMapping(value = "/v2/expressrecharge/verify", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Object> verifyV2(HttpServletRequest request, HttpEntity<Object> httpEntity,
                                         @RequestParam Map<String, Object> requestParams) {

        requestParams.put(CART_VERSION_V4, true);

        ResponseEntity<Object> response = verifyAll(request, httpEntity, requestParams);

        return response;
    }

    @PostMapping("/v1/expressrecharge/insurance/verify")
    public ResponseEntity<Object> insuraneverifyV1(@RequestBody FastagVerifyRequest payload, HttpServletRequest request,
                                           @RequestParam Map<String, Object> requestParams) {

        requestParams.put(CART_VERSION_V4, true);

        ResponseEntity<Object> response = verifyAllForFasttag(request, payload, requestParams);

        return response;
    }

    @PostMapping(value = "/v1/expressrecharge/parallel/verify", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Object> parallelVerifyV1(HttpServletRequest request, HttpEntity<Object> httpEntity,
                                           @RequestParam Map<String, Object> requestParams) {

        requestParams.put(CART_VERSION_V4, true);
        requestParams.put(Constants.PARALLEL_VERIFY_FLAG, true);

        ResponseEntity<Object> response = verifyAll(request, httpEntity, requestParams);

        return response;
    }

    @PostMapping(value = "/v1/expressrecharge/checkout", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Object> checkout(HttpServletRequest request, HttpEntity<Object> httpEntity,
            @RequestParam Map<String, Object> requestParams) {

        ResponseEntity<Object> response = checkoutAll(request, httpEntity, requestParams);
        return response;
    }

    @PostMapping(value = "/v2/expressrecharge/checkout", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Object> checkoutV2(HttpServletRequest request, HttpEntity<Object> httpEntity,
                                           @RequestParam Map<String, Object> requestParams) {

        requestParams.put(CART_VERSION_V4, true);
        ResponseEntity<Object> response = checkoutAll(request, httpEntity, requestParams);
        return response;
    }

    @PostMapping(value = "/v1/expressrecharge/deletefromallsystems", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<DeleteRecordFromAllSystemsResponse> deleteFromAllSystems(HttpServletRequest request,
                                                                                   @RequestBody DeleteFromAllSystemsRequest deleteFromAllSystemsRequest, @RequestParam Map<String, String> requestParams) {
        return deleteFromAllSystemsCommon(request,deleteFromAllSystemsRequest,requestParams,false);
    }

    @PostMapping(value = "/v1/s2s/expressrecharge/deletefromallsystems", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<DeleteRecordFromAllSystemsResponse> deleteFromAllSystemsS2S(HttpServletRequest request,
                                                                                   @RequestBody DeleteFromAllSystemsRequest deleteFromAllSystemsRequest, @RequestParam Map<String, String> requestParams) {
        return deleteFromAllSystemsCommon(request,deleteFromAllSystemsRequest,requestParams,true);
    }

    public ResponseEntity<DeleteRecordFromAllSystemsResponse> deleteFromAllSystemsCommon(HttpServletRequest request,
                                                                                         @RequestBody DeleteFromAllSystemsRequest deleteFromAllSystemsRequest, @RequestParam Map<String, String> requestParams, boolean isS2sCall) {

        if(!isS2sCall) {
            // excusion allowed only for s2s call
            deleteFromAllSystemsRequest.setExcludedServices(null);
        }
        LoggerThreadContext.setServiceName(deleteFromAllSystemsRequest.getService());
        MultiValueMap<String, String> headers = getHeaders(request);
        bffIndentifierExist(headers, "deletefromallsystems");
        if(headers.get(X_USER_ID) != null){
            deleteFromAllSystemsRequest.setCustomerId(Long.valueOf(headers.get(X_USER_ID).get(0)));
        }
        log.info("controller::deleteFromAllSystems:: request body {}", deleteFromAllSystemsRequest);
        DeleteRecordFromAllSystemsResponse response = null;
        try {
            response = deleteFromAllSystemService.removeRecord(deleteFromAllSystemsRequest,requestParams,headers);
        } catch (RestClientException e) {
            log.error("controller::deleteFromAllSystems:: http error {} statusCode {}", e.getMessage(), e.getHttpStatus());
        } catch (Exception e) {
            log.error("controller::deleteFromAllSystems:: unknown error occured ", e);
        }
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    @GetMapping(value = "/v1/expressrecharge/deletefromallsystems/reco", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public DeleteRecordFromAllSystemsResponse deleteFromAllSystemsV2(HttpServletRequest request, @RequestParam Map<String, String> requestParams) {

        MultiValueMap<String, String> headers = getHeaders(request);
        bffIndentifierExist(headers, "deletefromallsystems");
        DeleteFromAllSystemsRequest deleteFromAllSystemsRequest = new DeleteFromAllSystemsRequest();
        if (Objects.nonNull(requestParams.get(Constants.BILLER_ACCOUNT_ID)))
            deleteFromAllSystemsRequest.setBillerAccountId(Integer.parseInt(requestParams.get(Constants.BILLER_ACCOUNT_ID)));
        if (Objects.nonNull(requestParams.get(Constants.OPERATOR)))
            deleteFromAllSystemsRequest.setOperator(requestParams.get(Constants.OPERATOR));
        if (Objects.nonNull(requestParams.get(Constants.SERVICE)))
            deleteFromAllSystemsRequest.setService(requestParams.get(Constants.SERVICE));
        if (Objects.nonNull(requestParams.get(Constants.RECHARGE_NUMBER)))
            deleteFromAllSystemsRequest.setRechargeNumber(requestParams.get(Constants.RECHARGE_NUMBER));
        if (Objects.nonNull(requestParams.get(Constants.RECENT_PRODUCT_ID)))
            deleteFromAllSystemsRequest.setProductId(Long.parseLong(requestParams.get(Constants.RECENT_PRODUCT_ID)));
        if (Objects.nonNull(requestParams.get(Constants.CIN)))
            deleteFromAllSystemsRequest.setCin(requestParams.get(Constants.CIN));
        if (Objects.nonNull(requestParams.get(Constants.AMOUNT)))
            deleteFromAllSystemsRequest.setAmount(requestParams.get(Constants.AMOUNT));
        if (Objects.nonNull(requestParams.get(Constants.PAR_ID)))
            deleteFromAllSystemsRequest.setParId(requestParams.get(Constants.PAR_ID));
        if (Objects.nonNull(requestParams.get(Constants.ITEM_CATEGORY_ID)))
            deleteFromAllSystemsRequest.setCategoryId(Integer.parseInt(requestParams.get(Constants.ITEM_CATEGORY_ID)));
        if (Objects.nonNull(requestParams.get(Constants.TIN)))
            deleteFromAllSystemsRequest.setTin(requestParams.get(Constants.TIN));
        if (Objects.nonNull(requestParams.get(Constants.NEW_CUSTOMER_ID)))
            deleteFromAllSystemsRequest.setCustomerId(Long.parseLong(requestParams.get(Constants.NEW_CUSTOMER_ID)));

        LoggerThreadContext.setServiceName(deleteFromAllSystemsRequest.getService());
        log.info("controller::deleteFromAllSystems:: request body {}", deleteFromAllSystemsRequest);
        DeleteRecordFromAllSystemsResponse response = null;
        try {
            response = deleteFromAllSystemService.removeRecord(deleteFromAllSystemsRequest,requestParams,headers);
        } catch (RestClientException e) {
            log.error("controller::deleteFromAllSystems:: http error {} statusCode {}", e.getMessage(), e.getHttpStatus());
        } catch (Exception e) {
            log.error("controller::deleteFromAllSystems:: unknown error occured ", e);
        }
        return response;
    }
    @PostMapping(value = "/v1/expressrecharge/deletefromallsystems/reco", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public DeleteRecordFromAllSystemsResponse deleteFromAllSystemsV3(HttpServletRequest request, @RequestParam Map<String, String> requestParams) {

        MultiValueMap<String, String> headers = getHeaders(request);
        bffIndentifierExist(headers, "deletefromallsystems");

        DeleteFromAllSystemsRequest deleteFromAllSystemsRequest = new DeleteFromAllSystemsRequest();
        if (Objects.nonNull(requestParams.get(Constants.BILLER_ACCOUNT_ID)))
            deleteFromAllSystemsRequest.setBillerAccountId(Integer.parseInt(requestParams.get(Constants.BILLER_ACCOUNT_ID)));
        if (Objects.nonNull(requestParams.get(Constants.OPERATOR)))
            deleteFromAllSystemsRequest.setOperator(requestParams.get(Constants.OPERATOR));
        if (Objects.nonNull(requestParams.get(Constants.SERVICE)))
            deleteFromAllSystemsRequest.setService(requestParams.get(Constants.SERVICE));
        if (Objects.nonNull(requestParams.get(Constants.RECHARGE_NUMBER)))
            deleteFromAllSystemsRequest.setRechargeNumber(requestParams.get(Constants.RECHARGE_NUMBER));
        if (Objects.nonNull(requestParams.get(Constants.RECENT_PRODUCT_ID)))
            deleteFromAllSystemsRequest.setProductId(Long.parseLong(requestParams.get(Constants.RECENT_PRODUCT_ID)));
        if (Objects.nonNull(requestParams.get(Constants.CIN)))
            deleteFromAllSystemsRequest.setCin(requestParams.get(Constants.CIN));
        if (Objects.nonNull(requestParams.get(Constants.AMOUNT)))
            deleteFromAllSystemsRequest.setAmount(requestParams.get(Constants.AMOUNT));
        if (Objects.nonNull(requestParams.get(Constants.PAR_ID)))
            deleteFromAllSystemsRequest.setParId(requestParams.get(Constants.PAR_ID));
        if (Objects.nonNull(requestParams.get(Constants.ITEM_CATEGORY_ID)))
            deleteFromAllSystemsRequest.setCategoryId(Integer.parseInt(requestParams.get(Constants.ITEM_CATEGORY_ID)));
        if (Objects.nonNull(requestParams.get(Constants.TIN)))
            deleteFromAllSystemsRequest.setTin(requestParams.get(Constants.TIN));
        if (Objects.nonNull(requestParams.get(Constants.NEW_CUSTOMER_ID)))
            deleteFromAllSystemsRequest.setCustomerId(Long.parseLong(requestParams.get(Constants.NEW_CUSTOMER_ID)));

        LoggerThreadContext.setServiceName(deleteFromAllSystemsRequest.getService());
        log.info("controller::deleteFromAllSystems:: request body {}", deleteFromAllSystemsRequest);
        DeleteRecordFromAllSystemsResponse response = null;
        try {
            response = deleteFromAllSystemService.removeRecord(deleteFromAllSystemsRequest,requestParams,headers);
        } catch (RestClientException e) {
            log.error("controller::deleteFromAllSystems:: http error {} statusCode {}", e.getMessage(), e.getHttpStatus());
        } catch (Exception e) {
            log.error("controller::deleteFromAllSystems:: unknown error occured ", e);
        }
        return response;
    }

    private void bffIndentifierExist(MultiValueMap<String, String> headers, String requestType) {

        String BFF_INDENTIFIER = "bff_request";

        if (Objects.nonNull(headers.get(BFF_INDENTIFIER))) {
            List<String> bffIndvalue = headers.get(BFF_INDENTIFIER);
            log.info("bffIndentifierExist:: BFF_INDENTIFIER exist. Cyclic call {}", JsonUtil.toJson(bffIndvalue));
            log.info("bffIndentifierExist:: headers {}", JsonUtil.toJson(headers));
            metricsAgent.incrementEventCount("cyclic_request_at_bff_" + requestType);
        } else {
            headers.add(BFF_INDENTIFIER, requestType);
        }

    }

    private MultiValueMap<String, String> getHeaders(HttpServletRequest request) {
        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        Enumeration<String> names = request.getHeaderNames();
        while (names.hasMoreElements()) {
            String name = names.nextElement();
            headers.add(name, request.getHeader(name));
        }
        return headers;
    }


    // this will handle smart fastag CLP with image
    private ResponseEntity<Object> verifyAllForFasttag(HttpServletRequest request, FastagVerifyRequest payload,
                                                       Map<String, Object> requestParams) {

        if(CollectionUtils.isNotEmpty(payload.getCartItems()) && payload.getCartItems().get(0).getProductId() != null && payload.getCartItems().get(0).getProductId() > 0) {
            ResponseEntity<Object> response = verifyAll(request, new HttpEntity<>(payload), requestParams);
            try {
                FastagVerifyResponse result = new FastagVerifyResponse();
                if (response.getStatusCode().is2xxSuccessful()) {
                    RechargeVerifyResponse verifyResponse = (RechargeVerifyResponse) response.getBody();

                    if (Objects.nonNull(verifyResponse) && StringUtils.isEmpty(verifyResponse.getError()) && StringUtils.isEmpty(verifyResponse.getErrorCode()) && StringUtils.isEmpty(verifyResponse.getErrorTitle())
                            && StringUtils.isEmpty(verifyResponse.getCart().getError()) && StringUtils.isEmpty(verifyResponse.getCart().getErrorCode())
                            && ObjectUtils.isEmpty(verifyResponse.getCart().getErrorInfo())) {

                        BeanUtils.copyProperties(verifyResponse, result);
                        log.info("verify verifyResponse {}", verifyResponse);
                        FastagResponse fastagResponse = null;
                        FastagApiResponse insuranceResponse = null;
                        fastagResponse = payload.getFastag();
                        if(Objects.nonNull(fastagResponse)) {
                            insuranceResponse = new FastagApiResponse();
                            insuranceResponse.setModel(fastagResponse.getModelId());
                            insuranceResponse.setMake(fastagResponse.getMakeId());
                            if(Objects.nonNull(fastagResponse.getVariantId())) {
                                insuranceResponse.setVariantId(fastagResponse.getVariantId().intValue());
                            }
                            insuranceResponse.setVehicleColor(fastagResponse.getColour());
                        }
                        fastTagService.createRecent(verifyResponse, insuranceResponse);// it will create recent in saga no need to waif for response
                        fastagResponse.setFastTagBalance(insuranceResponse.getFastTagBalance());

                        result.setFastag(fastagResponse);
                        log.info("verify result after verifyResponse {}", result);
                        response = ResponseEntity.status(response.getStatusCode()).body(result);
                    }else{
                        BeanUtils.copyProperties(verifyResponse, result);
                        result.setFastag(payload.getFastag());
                        response = ResponseEntity.status(response.getStatusCode()).body(result);
                    }
                }
            }
            catch (Exception e){
                log.error("Exception while fetching data ", e);
            }

            return response;
        }else{
            return fastagRequestHandler.verifyAsyncFastag(request,payload,requestParams);
        }

    }




}
