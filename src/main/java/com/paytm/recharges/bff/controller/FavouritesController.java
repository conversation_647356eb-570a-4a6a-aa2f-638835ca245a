package com.paytm.recharges.bff.controller;

import com.google.common.base.Strings;
import com.paytm.recharges.bff.config.properties.HomeReminderProperties;
import com.paytm.recharges.bff.constants.Constants;
import com.paytm.recharges.bff.constants.ErrorMessage;
import com.paytm.recharges.bff.constants.Routes;
import com.paytm.recharges.bff.datalayer.dto.request.BulkConsentRequestItem;
import com.paytm.recharges.bff.datalayer.dto.request.FrequentOrdersRequest;
import com.paytm.recharges.bff.datalayer.dto.request.InterstitialRequest;
import com.paytm.recharges.bff.datalayer.dto.request.RURecoRequest;
import com.paytm.recharges.bff.datalayer.dto.request.UpdateNicknameRequest;
import com.paytm.recharges.bff.datalayer.dto.request.WhatsappReminderLaterRequest;
import com.paytm.recharges.bff.datalayer.dto.response.ApiResponse;
import com.paytm.recharges.bff.datalayer.dto.response.HomeReminderResponse;
import com.paytm.recharges.bff.datalayer.dto.response.RuWidgetResponse;
import com.paytm.recharges.bff.datalayer.dto.response.ShortnerApiResponse;
import com.paytm.recharges.bff.datalayer.dto.response.UpdateNicknameResponse;
import com.paytm.recharges.bff.datalayer.dto.response.WhatsappReminderLaterResponse;
import com.paytm.recharges.bff.exceptions.InvalidTokenException;
import com.paytm.recharges.bff.monitor.MetricsAgent;
import com.paytm.recharges.bff.service.InterstitialService;
import com.paytm.recharges.bff.service.RecentServiceManager;
import com.paytm.recharges.bff.service.WhatsappRemindLaterService;
import com.paytm.recharges.bff.service.impl.CcbpConsentService;
import com.paytm.recharges.bff.service.impl.FavouriteManagerImpl;
import com.paytm.recharges.bff.utils.JwtUtil;
import com.paytm.recharges.bff.utils.Validator;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.recharges.custom_logger.service.LoggerThreadContext;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;

import static com.paytm.recharges.bff.constants.Constants.API_VERSION_FOR_FREQUENT_ORDERS;
import static com.paytm.recharges.bff.constants.Constants.CLIENT;
import static com.paytm.recharges.bff.constants.Constants.DEEPLINK;
import static com.paytm.recharges.bff.constants.Constants.HOME_REMINDER_RESPONSE_COUNT;
import static com.paytm.recharges.bff.constants.Constants.LOCALE;
import static com.paytm.recharges.bff.constants.Constants.RU_REMINDER_RESPONSE_COUNT;
import static com.paytm.recharges.bff.constants.Constants.VERSION;
import static com.paytm.recharges.bff.constants.Constants.VERSION_5_FOR_FREQUENT_ORDERS;
import static com.paytm.recharges.bff.utils.Utils.getHeaders;

@RestController
@RequestMapping(path = Routes.FAVOURITE_BASE_URL)
public class FavouritesController {
    private static final CustomLogger log = CustomLogManager.getLogger(FavouritesController.class);

    @Autowired
    private FavouriteManagerImpl favouriteManager;

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private HomeReminderProperties homeReminderProperties;

    @Autowired
    private MetricsAgent metricsAgent;

    @Autowired
    private RecentServiceManager recentServiceManager;
    @Autowired
    private WhatsappRemindLaterService whatsappRemindLaterServiceV1;
    @Autowired
    private WhatsappRemindLaterService whatsappRemindLaterServiceV2;
    @Autowired
    private CcbpConsentService ccbpConsentService;
    @Autowired
    private InterstitialService interstitialService;


    @PostMapping(path = Routes.FREQUENT_ORDER_URL)
    public ResponseEntity<Object> getFrequentOrder(HttpServletRequest request, @RequestBody FrequentOrdersRequest frequentOrdersRequest, @RequestParam HashMap<String, String> allRequestParams) {
        LoggerThreadContext.setServiceName(Constants.FINANCIAL_SERVICES);
        Long startTime = System.currentTimeMillis();
        Map<String, String> headers = getHeaders(request);
        Long customerId = Long.parseLong(headers.get(Constants.CUSTOMER_ID));
        allRequestParams.put(Constants.CUSTOMER_ID,headers.get(Constants.CUSTOMER_ID));
        frequentOrdersRequest.setMethodType("POST");
        ArrayList<HashMap<String, Object>> frequentOrdersResponse = favouriteManager.getFrequentOrdersByFilter(customerId, frequentOrdersRequest, allRequestParams, false);
        metricsAgent.recordExecutionTimeServiceWise(Constants.POST_FREQUENT_ORDER_LATENCY, System.currentTimeMillis()-startTime, frequentOrdersRequest.getService());
        log.info("FavouritesController.getFrequentOrder POST:: recent response for headers :: {} frequentOrdersRequest {} , response size :: {}", headers,frequentOrdersRequest, frequentOrdersResponse.size());
        favouriteManager.pushMyBillsAndClpLogs(frequentOrdersResponse, "POST v2/frequentOrders", customerId);
        return new ResponseEntity<>(frequentOrdersResponse, HttpStatus.OK);
    }

    @GetMapping(path = Routes.FREQUENT_ORDER_URL)
    public ResponseEntity<Object> getFrequentOrders(HttpServletRequest request, @RequestParam HashMap<String, String> allRequestParams) {

        LoggerThreadContext.setServiceName(Constants.FINANCIAL_SERVICES);
        Map<String, String> headers = getHeaders(request);

        Long customerId = Long.parseLong(headers.get(Constants.CUSTOMER_ID));
        allRequestParams.put(Constants.CUSTOMER_ID,headers.get(Constants.CUSTOMER_ID));
        ArrayList<HashMap<String, Object>> frequentOrdersResponse = favouriteManager.getFrequentOrdersByCustomerId(customerId, allRequestParams);

        log.info("FavouritesController.getFrequentOrder GET:: recent response for customerId :: {} , response count :: {}", customerId, frequentOrdersResponse.size());
        favouriteManager.pushMyBillsAndClpLogs(frequentOrdersResponse, "GET v2/frequentOrders", customerId);
        return new ResponseEntity<>(frequentOrdersResponse, HttpStatus.OK);
    }

    @GetMapping(path = Routes.FREQUENT_ORDER_URL_V3)
    public ResponseEntity<Object> getFrequentOrdersV3(HttpServletRequest request, @RequestParam HashMap<String, String> allRequestParams) {

        LoggerThreadContext.setServiceName(Constants.FINANCIAL_SERVICES);
        Map<String, String> headers = getHeaders(request);

        Long customerId = Long.parseLong(headers.get(Constants.CUSTOMER_ID));
        allRequestParams.put(Constants.CUSTOMER_ID,headers.get(Constants.CUSTOMER_ID));
        log.info("FavouritesController.getFrequentOrderV3 POST :: getting response for customerId :: {}", customerId);

        ArrayList<HashMap<String, Object>> frequentOrdersResponse = favouriteManager.getFrequentOrdersByCustomerIdV2(customerId, allRequestParams);
        favouriteManager.pushMyBillsAndClpLogs(frequentOrdersResponse, "GET v3/frequentOrders",customerId );

        log.trace(" FavouritesController.getFrequentOrdersV3 GET:: recent response for customerId :: {} , response count :: {}", customerId, frequentOrdersResponse.size());
        return new ResponseEntity<>(frequentOrdersResponse, HttpStatus.OK);
    }

    @PutMapping(path = Routes.UPDATE_RECENT_URL)
    public ResponseEntity<Object> updateRecent(HttpServletRequest request, @RequestBody UpdateNicknameRequest updateNicknameRequest, @RequestParam HashMap<String, String> allRequestParams) throws ExecutionException, InterruptedException, TimeoutException, ExecutionException, TimeoutException {

        // this code is added after removing favourite recentupdate hit
        //TO-DO need to remove this part and handle the response on the basis of response received from saga
        LoggerThreadContext.setServiceName(updateNicknameRequest.getService());
        UpdateNicknameResponse updateNicknameResponse = new UpdateNicknameResponse();
        updateNicknameResponse.setStatusCode("00");
        updateNicknameResponse.setMessage(ErrorMessage.SUCCESS_MESSAGE);
        recentServiceManager.updateNickName(updateNicknameRequest, allRequestParams);


        return new ResponseEntity<>(updateNicknameResponse, HttpStatus.OK);
    }

    @PostMapping(path = Routes.FREQUENT_ORDER_URL_V3)
    public ResponseEntity<Object> getFrequentOrderV3(HttpServletRequest request, @RequestBody FrequentOrdersRequest frequentOrdersRequest, @RequestParam HashMap<String, String> allRequestParams) {

        LoggerThreadContext.setServiceName(Constants.FINANCIAL_SERVICES);
        Long startTime = System.currentTimeMillis();
        Map<String, String> headers = getHeaders(request);

        Long customerId = Long.parseLong(headers.get(Constants.CUSTOMER_ID));
        allRequestParams.put(Constants.CUSTOMER_ID,headers.get(Constants.CUSTOMER_ID));
        frequentOrdersRequest.setMethodType("POST");
        ArrayList<HashMap<String, Object>> frequentOrdersResponse = favouriteManager.getFrequentOrdersByFilter(customerId, frequentOrdersRequest, allRequestParams, true);
        metricsAgent.recordExecutionTimeServiceWise(Constants.POST_FREQUENT_ORDER_LATENCY_V3, System.currentTimeMillis()-startTime, frequentOrdersRequest.getService());
        log.info("FavouritesController.getFrequentOrderV3 POST :: recent response for headers :: {} frequentOrdersRequest {} , response size :: {}", headers,frequentOrdersRequest, frequentOrdersResponse.size());
        favouriteManager.pushMyBillsAndClpLogs(frequentOrdersResponse, "POST v3/frequentOrders", customerId);
        return new ResponseEntity<>(frequentOrdersResponse, HttpStatus.OK);
    }

    @PostMapping(path = Routes.FREQUENT_ORDER_URL_V4)
    public ResponseEntity<Object> getFrequentOrderV4(HttpServletRequest request, @RequestBody FrequentOrdersRequest frequentOrdersRequest, @RequestParam HashMap<String, String> allRequestParams) {
        LoggerThreadContext.setServiceName(Constants.FINANCIAL_SERVICES);
        Long startTime = System.currentTimeMillis();
        Map<String, String> headers = getHeaders(request);
        Long customerId = Long.parseLong(headers.get(Constants.CUSTOMER_ID));
        frequentOrdersRequest.setMethodType("POST");
        allRequestParams.put(Constants.CUSTOMER_ID,headers.get(Constants.CUSTOMER_ID));
        allRequestParams.put(Constants.API_VERSION_KEY, Constants.FREQUENT_ORDERS_VERSION_V4);
        ArrayList<HashMap<String, Object>> frequentOrdersResponse = favouriteManager.getFrequentOrdersByFilter(customerId, frequentOrdersRequest, allRequestParams, true);
        if(Objects.equals(frequentOrdersRequest.getService(), "Mobile")) {
            favouriteManager.populateChangeUserPlansOperator(frequentOrdersResponse,null,null);
        }
        metricsAgent.recordExecutionTimeServiceWise(Constants.POST_FREQUENT_ORDER_LATENCY_V4, System.currentTimeMillis()-startTime, frequentOrdersRequest.getService());
        log.info("FavouritesController.getFrequentOrderV4 POST :: recent response for headers :: {}  frequentOrdersRequest {} response size :: {}", headers,frequentOrdersRequest, frequentOrdersResponse.size());
        favouriteManager.pushMyBillsAndClpLogs(frequentOrdersResponse, "POST v4/frequentOrders", customerId);
        return new ResponseEntity<>(frequentOrdersResponse, HttpStatus.OK);
    }
    @GetMapping(path = Routes.FREQUENT_ORDER_URL_HR)
    public ResponseEntity<Object> getHomeRemainder(HttpServletRequest request, @RequestParam HashMap<String, String> allRequestParams) throws IOException, ParseException {
        LoggerThreadContext.setServiceName(Constants.FINANCIAL_SERVICES);
        Map<String, String> headers = getHeaders(request);
        Long customerId = null;
        try {
            customerId = jwtUtil.validateTokenAndGetCustomerId(headers.get("authorization"), homeReminderProperties.getSecretKey());
        } catch (InvalidTokenException e) {
            return new ResponseEntity<>(null, HttpStatus.UNAUTHORIZED);
        }
        allRequestParams.put(Constants.CUSTOMER_ID,customerId.toString());
        ArrayList<HomeReminderResponse> frequentOrdersResponse = favouriteManager.getHomeReminderResponse(customerId, allRequestParams);
        metricsAgent.increaseBatchCount(frequentOrdersResponse.size(),HOME_REMINDER_RESPONSE_COUNT);
        return new ResponseEntity<>(frequentOrdersResponse, HttpStatus.OK);
    }
    @PostMapping(path = Routes.FREQUENT_ORDER_URL_HR)
    public ResponseEntity<Object> getHomeRemainderV2(HttpServletRequest request, @RequestParam HashMap<String, String> allRequestParams, @RequestBody RURecoRequest ruRecoRequest) throws Exception {
        LoggerThreadContext.setServiceName(Constants.FINANCIAL_SERVICES);
        Map<String, String> headers = getHeaders(request);
        Long customerId = null;
        
        try {
            customerId = jwtUtil.validateTokenAndGetCustomerId(headers.get("authorization"), homeReminderProperties.getSecretKey());
        } catch (InvalidTokenException e) {
            return new ResponseEntity<>(null, HttpStatus.UNAUTHORIZED);
        }
        
        allRequestParams.put(Constants.CUSTOMER_ID,customerId.toString());
        allRequestParams.put(CLIENT,ruRecoRequest.getClient());
        allRequestParams.put(VERSION,ruRecoRequest.getVersion());
        allRequestParams.put(LOCALE, Validator.validateAndGetLocale(ruRecoRequest.getLocale()));

        log.info("FavouritesController.getHomeRemainderV2 POST :: reco request :: {}",allRequestParams);
        RuWidgetResponse ruWidgetResponse = favouriteManager.getHomeReminderResponseV3(customerId, allRequestParams, ruRecoRequest);
        
        // Add custom properties to the first two items in the ruReminderResponseList
        if(Objects.nonNull(ruWidgetResponse) && Objects.nonNull(ruWidgetResponse.getRuReminderResponseList()) 
                && !ruWidgetResponse.getRuReminderResponseList().isEmpty()) {
            List<HomeReminderResponse> reminderList = ruWidgetResponse.getRuReminderResponseList();
            
            metricsAgent.increaseBatchCount(ruWidgetResponse.getRuReminderResponseList().size(),RU_REMINDER_RESPONSE_COUNT);
        }
        log.info("FavouritesController.getHomeRemainderV2 POST :: reco response for allRequestParams :: {} ruWidgetResponse {}",allRequestParams,ruWidgetResponse.toString());
        return new ResponseEntity<>(ruWidgetResponse, HttpStatus.OK);
    }

    @GetMapping(path = Routes.SHORTEN_API)
    public ResponseEntity<Object> getShortenApi(HttpServletRequest request, @RequestParam HashMap<String, String> allRequestParams) {

        Map<String, String> headers = getHeaders(request);
        Map<String, Object> requestBody = new HashMap<>();

        if (headers.isEmpty() || headers.get("deeplink").isEmpty()) {
            ShortnerApiResponse shortnerApiResponse = new ShortnerApiResponse();
            shortnerApiResponse.setCode(400);
            shortnerApiResponse.setStatus("BAD REQUEST");
            shortnerApiResponse.setMessage("Encoded can't be null/invalid");
            ResponseEntity<Object> response = new ResponseEntity<>(shortnerApiResponse, HttpStatus.BAD_REQUEST);
            return response;
        } else {
            requestBody.put(DEEPLINK, headers.get(DEEPLINK));
        }
        ShortnerApiResponse shortnerApiResponse = favouriteManager.shortnerApi(requestBody);

        return new ResponseEntity<>(shortnerApiResponse, HttpStatus.valueOf(shortnerApiResponse.getCode()));
    }

    @PostMapping(path = Routes.FETCH_RECENTS)
    public ResponseEntity<Object> fetchRecentOrders(HttpServletRequest request, @RequestBody FrequentOrdersRequest frequentOrdersRequest, @RequestParam HashMap<String, String> allRequestParams) {

        LoggerThreadContext.setServiceName(Constants.FINANCIAL_SERVICES);
        Map<String, String> headers = getHeaders(request);

        Long customerId = Long.parseLong(headers.get(Constants.CUSTOMER_ID));
        allRequestParams.put(Constants.CUSTOMER_ID,headers.get(Constants.CUSTOMER_ID));

        log.info("FavouritesController.fetchRecentOrders GET :: getting response for customerId :: {}", customerId);

        ArrayList<HashMap<String, Object>> recentOrdersResponse = favouriteManager.getRecentOrders(customerId, frequentOrdersRequest, allRequestParams);

        log.trace(" FavouritesController.fetchRecentOrders GET:: recent response for customerId :: {} , response count :: {}", customerId, recentOrdersResponse.size());
        return new ResponseEntity<>(recentOrdersResponse, HttpStatus.OK);
    }

    @PostMapping(path = Routes.FETCH_V2_RECENTS)
    public ResponseEntity<Object> fetchRecentOrdersV2(HttpServletRequest request, @RequestBody FrequentOrdersRequest frequentOrdersRequest, @RequestParam HashMap<String, String> allRequestParams) {

        LoggerThreadContext.setServiceName(Constants.FINANCIAL_SERVICES);
        Map<String, String> headers = getHeaders(request);

        Long customerId = Long.parseLong(headers.get(Constants.CUSTOMER_ID));
        allRequestParams.put(Constants.CUSTOMER_ID,headers.get(Constants.CUSTOMER_ID));

        log.info("FavouritesController.fetchRecentOrdersV2 GET :: getting response for customerId :: {} frequentOrdersRequest {}", customerId,frequentOrdersRequest);

        Map<String,ArrayList<HashMap<String, Object>>> recentOrdersResponse = favouriteManager.getRecentOrdersV2(customerId, frequentOrdersRequest, allRequestParams);

        log.trace(" FavouritesController.fetchRecentOrdersV2 GET:: recent response for customerId :: {} , response count :: {}", customerId, recentOrdersResponse.size());
        return new ResponseEntity<>(recentOrdersResponse, HttpStatus.OK);
    }

    @PostMapping(path = Routes.REMIND_ME_LATER)
    public ResponseEntity<Object> setRemindMeLaterDate(HttpServletRequest request, @RequestBody WhatsappReminderLaterRequest whatsappReminderLaterRequest, @RequestParam HashMap<String, String> allRequestParams) {
        log.info("FavouritesController.setRemindMeLaterDate POST :: hitting for customerId :: {}", whatsappReminderLaterRequest.getCustomerId());
        LoggerThreadContext.setServiceName(whatsappReminderLaterRequest.getService());

        WhatsappReminderLaterResponse whatsappReminderLaterResponse = whatsappRemindLaterServiceV1.setReminderMeLaterDate(whatsappReminderLaterRequest);

        return new ResponseEntity<>(whatsappReminderLaterResponse, HttpStatus.OK);
    }

    @PostMapping(path = Routes.REMIND_ME_LATER_V2)
    public ResponseEntity<Object> setRemindMeLaterDatev2(HttpServletRequest request, @RequestBody WhatsappReminderLaterRequest whatsappReminderLaterRequest, @RequestParam HashMap<String, String> allRequestParams) {
        log.info("FavouritesController.setRemindMeLaterDateV2 POST :: hitting for customerId :: {}", whatsappReminderLaterRequest.getCustomerId());
        LoggerThreadContext.setServiceName(whatsappReminderLaterRequest.getService());

        WhatsappReminderLaterResponse whatsappReminderLaterResponse = whatsappRemindLaterServiceV2.setReminderMeLaterDate(whatsappReminderLaterRequest);

        return new ResponseEntity<>(whatsappReminderLaterResponse, HttpStatus.OK);
    }

    @GetMapping(path = Routes.FREQUENT_ORDER_URL_V5)
    public ResponseEntity<Object> getFrequentOrdersV5(HttpServletRequest request, @RequestParam HashMap<String, String> allRequestParams) {

        LoggerThreadContext.setServiceName(Constants.FINANCIAL_SERVICES);
        Map<String, String> headers = getHeaders(request);

        Long customerId = Long.parseLong(headers.get(Constants.CUSTOMER_ID));
        
        allRequestParams.put(Constants.CUSTOMER_ID,headers.get(Constants.CUSTOMER_ID));
        allRequestParams.put(API_VERSION_FOR_FREQUENT_ORDERS,VERSION_5_FOR_FREQUENT_ORDERS);
        allRequestParams.put(LOCALE, Validator.validateAndGetLocale(allRequestParams.get(LOCALE)));

        log.info("FavouritesController.getFrequentOrderV5 GET :: getting response for customerId :: {}", customerId);

        ArrayList<HashMap<String, Object>> frequentOrdersResponse = favouriteManager.getFrequentOrdersByCustomerIdV2(customerId, allRequestParams);
        favouriteManager.pushMyBillsAndClpLogs(frequentOrdersResponse, "GET v5/frequentOrders",customerId );

        log.trace(" FavouritesController.getFrequentOrdersV5 GET:: recent response for customerId :: {} , response count :: {}", customerId, frequentOrdersResponse.size());
        return new ResponseEntity<>(frequentOrdersResponse, HttpStatus.OK);
    }

    @PostMapping(path = Routes.CREATE_OR_UPDATE_BULK_CONSENT_V1)
    public ResponseEntity<Object> createOrUpdateBulkConsent(HttpServletRequest request, @Valid @RequestBody List<BulkConsentRequestItem> bulkConsentRequestItemList) {
        LoggerThreadContext.setServiceName(Constants.FINANCIAL_SERVICES);
        Map<String, String> headers = getHeaders(request);
        String customerId = headers.get(Constants.CUSTOMER_ID);
        if(Strings.isNullOrEmpty(customerId)) {
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }
        log.info("Processing bulk consent request for customer_id : {}", customerId);
        ResponseEntity<Object> response = ccbpConsentService.createOrUpdateBulkConsent(bulkConsentRequestItemList, customerId);
        LoggerThreadContext.clear();
        return response;
    }


}
