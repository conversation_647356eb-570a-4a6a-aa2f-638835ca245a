package com.paytm.recharges.bff.service;

import com.paytm.recharges.bff.config.properties.InterstitialKafkaConfig;
import com.paytm.recharges.bff.constants.Constants;
import com.paytm.recharges.bff.constants.Routes;
import com.paytm.recharges.bff.datalayer.cache.ServiceConfigCache;
import com.paytm.recharges.bff.datalayer.dto.request.InterstitialRequest;
import com.paytm.recharges.bff.datalayer.dto.response.ApiResponse;
import com.paytm.recharges.bff.datalayer.model.CacheData;
import com.paytm.recharges.bff.datalayer.model.DwhInterstitialPayload;
import com.paytm.recharges.bff.datalayer.model.InterstitialRetryKafkaPayload;
import com.paytm.recharges.bff.utils.JsonUtil;
import com.paytm.recharges.bff.utils.InterstitialUtils;
import com.paytm.recharges.bff.monitor.MetricsAgent;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Objects;

@Service
public class InterstitialService {

    private static final CustomLogger log = CustomLogManager.getLogger(InterstitialService.class);

    private final InterstitialUtils interstitialUtils;
    private final GenericKafkaProducer interstitialKafkaProducer;
    private final InterstitialKafkaConfig interstitialKafkaConfig;
    private final MetricsAgent metricsAgent;

    @Autowired
    public InterstitialService(InterstitialUtils interstitialUtils,
                               GenericKafkaProducer interstitialKafkaProducer,
                               InterstitialKafkaConfig interstitialKafkaConfig,
                               MetricsAgent metricsAgent) {
        this.interstitialUtils = interstitialUtils;
        this.interstitialKafkaProducer = interstitialKafkaProducer;
        this.interstitialKafkaConfig = interstitialKafkaConfig;
        this.metricsAgent = metricsAgent;
    }

    public ApiResponse pushInterstitialKafkaRetry(InterstitialRequest interstitialRequest) {
        InterstitialRetryKafkaPayload interstitialRetryKafkaPayload = new InterstitialRetryKafkaPayload();
        try {
            String topicName;
            interstitialRetryKafkaPayload.setApiUrl(Routes.FAVOURITE_BASE_URL + Routes.INTERSTITIAL_URL_RETRY);
            interstitialRetryKafkaPayload.setInterstitialRequest(interstitialRequest);

            if (Objects.nonNull(interstitialRequest.getRetryCount()) && interstitialRequest.getRetryCount() > 0) {
                interstitialRetryKafkaPayload.setRetryCount(interstitialRequest.getRetryCount() + 1);
                topicName = interstitialKafkaConfig.getTopicName30mRetry();
            } else {
                interstitialRetryKafkaPayload.setRetryCount(1);
                topicName = interstitialKafkaConfig.getTopicName15sRetry();
            }
            interstitialKafkaProducer.sendInterstitialMessage(JsonUtil.toJson(interstitialRetryKafkaPayload), topicName);
            return InterstitialUtils.createKafkaSuccessResponse();
        } catch (Exception e) {
            log.error("Error while pushing interstitial event to Kafka for customerId: {} and payload : {}"
                    , interstitialRequest.getCustomerId(), interstitialRetryKafkaPayload, e);
            metricsAgent.incrementEventCount(Constants.INTERSTITIAL_RETRY_KAFKA_ERROR);
            return InterstitialUtils.createFailureResponse();
        }
    }

    public ApiResponse registerInterstitialImpression(InterstitialRequest interstitialRequest) {
        ApiResponse apiResponse = new ApiResponse();
        Integer maxAttempts = ServiceConfigCache.getInstance().getInteger(Constants.INTERSTITIAL_RETRY_COUNT_KEY);
        if (maxAttempts == null || maxAttempts < 1) {
            maxAttempts = Integer.parseInt(Constants.INTERSTITIAL_DEFAULT_RETRY_COUNT); // fallback default
        }
        int attempt = 0;
        boolean success = false;
        List<CacheData> cacheDataList = null;

        while (attempt < maxAttempts && !success) {
            try {
                // Update Aerospike data
                cacheDataList = interstitialUtils.updateInterstitialCache(interstitialRequest);
                log.info("Aerospike data updated successfully for customerId: {}", interstitialRequest.getCustomerId());

                apiResponse = InterstitialUtils.createSuccessResponse();
                success = true;
            } catch (Exception e) {
                metricsAgent.incrementEventCount(Constants.INTERSTITIAL_CACHE_UPDATE_ERROR,
                        Constants.CUSTOMER_ID + ":" + interstitialRequest.getCustomerId());
                attempt++;
                log.error("Error while registering interstitial impression for customerId: {} (attempt {}/{}): {}", 
                    interstitialRequest.getCustomerId(), attempt, maxAttempts, e.getMessage(), e);
                if (attempt >= maxAttempts) {
                    apiResponse = InterstitialUtils.createFailureResponse();
                }
            }
        }

        if (!success) {
            log.error("Failed to register interstitial impression after {} attempts for customerId: {}", maxAttempts, interstitialRequest.getCustomerId());
            apiResponse = pushInterstitialKafkaRetry(interstitialRequest);
        }

        // Push the request to DWH Kafka topic
        pushToDwhInterstitialKafka(interstitialRequest, success, cacheDataList);
        log.info("Kafka event pushed successfully for customerId: {}", interstitialRequest.getCustomerId());

        return apiResponse;
    }

    /**
     * Pushes interstitial data to the DWH Kafka topic for analytics
     *
     * @param interstitialRequest The original interstitial request
     * @param success Whether the interstitial impression was successfully registered
     * @param cacheDataList The list of cache data
     */
    private void pushToDwhInterstitialKafka(InterstitialRequest interstitialRequest, boolean success, List<CacheData> cacheDataList) {
        try {
            DwhInterstitialPayload dwhPayload = new DwhInterstitialPayload();
            dwhPayload.setInterstitialRequest(interstitialRequest);
            dwhPayload.setSuccess(success);
            dwhPayload.setTimestamp(System.currentTimeMillis());
            dwhPayload.setCacheData(cacheDataList);

            String topicName = interstitialKafkaConfig.getTopicNameDwh();
            if (topicName != null && !topicName.isEmpty()) {
                interstitialKafkaProducer.sendInterstitialMessage(JsonUtil.toJson(dwhPayload), topicName);
                log.info("DWH interstitial data pushed to Kafka for customerId: {}", interstitialRequest.getCustomerId());
            } else {
                log.warn("DWH interstitial Kafka topic name is not configured");
            }
        } catch (Exception e) {
            log.error("Error while pushing DWH interstitial data to Kafka for customerId: {}",
                    interstitialRequest.getCustomerId(), e);
            metricsAgent.incrementEventCount("interstitial_dwh_kafka_error");
        }
    }
}
