package com.paytm.recharges.bff.service.impl;

import com.aerospike.client.Key;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.recharges.bff.aop.annotations.MethodLatencyMetricsAction;
import com.paytm.recharges.bff.client.AerospikeGenericWrapper;
import com.paytm.recharges.bff.client.CartClient;
import com.paytm.recharges.bff.client.FulfillmentRechargeClient;
import com.paytm.recharges.bff.client.RechargeExpressClient;
import com.paytm.recharges.bff.client.RpsRestClient;
import com.paytm.recharges.bff.client.SagaClient;
import com.paytm.recharges.bff.constants.Constants;
import com.paytm.recharges.bff.datalayer.cache.CVRProductCache;
import com.paytm.recharges.bff.datalayer.dto.request.CustomerNameRequest;
import com.paytm.recharges.bff.datalayer.dto.request.RechargeVerifyRequest;
import com.paytm.recharges.bff.datalayer.dto.request.ValidateCartItem;
import com.paytm.recharges.bff.datalayer.dto.response.FFRValidateResponse;
import com.paytm.recharges.bff.datalayer.dto.response.PlatformMetaData;
import com.paytm.recharges.bff.datalayer.dto.response.RechargeCart;
import com.paytm.recharges.bff.datalayer.dto.response.RechargeItem;
import com.paytm.recharges.bff.datalayer.dto.response.RechargeVerifyResponse;
import com.paytm.recharges.bff.datalayer.dto.response.RpsPlanResponse;
import com.paytm.recharges.bff.datalayer.dto.response.SagaCustomerNameResponse;
import com.paytm.recharges.bff.datalayer.model.Product;
import com.paytm.recharges.bff.enums.FeatureConfigName;
import com.paytm.recharges.bff.exceptions.RechargesException;
import com.paytm.recharges.bff.exceptions.RestClientException;
import com.paytm.recharges.bff.mapper.CartVerifyRequestMapper;
import com.paytm.recharges.bff.mapper.CartVerifyResponseMapper;
import com.paytm.recharges.bff.monitor.MetricsAgent;
import com.paytm.recharges.bff.requesthandler.CartParallelVerifyRequestHandler;
import com.paytm.recharges.bff.requesthandler.CreditCardRiskPayloadHandler;
import com.paytm.recharges.bff.service.RechargeValidationService;
import com.paytm.recharges.bff.service.RechargeVerifyService;
import com.paytm.recharges.bff.utils.CartUtil;
import com.paytm.recharges.bff.utils.EncryptionUtil;
import com.paytm.recharges.bff.utils.JsonUtil;
import com.paytm.recharges.bff.utils.Utils;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytmmall.cart.checkout.dto.Cart;
import com.paytmmall.cart.checkout.dto.Item;
import com.paytmmall.cart.checkout.dto.PaymentIntent;
import com.paytmmall.cart.checkout.dto.PaymentMode;
import com.paytmmall.cart.checkout.dto.VerifyRequest;
import com.paytmmall.cart.checkout.dto.VerifyResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import static com.paytm.recharges.bff.constants.Constants.ACCESS_TOKEN_VALUE;
import static com.paytm.recharges.bff.constants.Constants.AMOUNT;
import static com.paytm.recharges.bff.constants.Constants.BENEFICIARY_NAME;
import static com.paytm.recharges.bff.constants.Constants.BFF_NAMESPACE;
import static com.paytm.recharges.bff.constants.Constants.CART_VERSION_V4;
import static com.paytm.recharges.bff.constants.Constants.CCF;
import static com.paytm.recharges.bff.constants.Constants.CLEVERTAP_EXECUTOR_THREAD;
import static com.paytm.recharges.bff.constants.Constants.CLIENT;
import static com.paytm.recharges.bff.constants.Constants.CONFIGURATION_LOCAL;
import static com.paytm.recharges.bff.constants.Constants.CONV_FEE_INFO;
import static com.paytm.recharges.bff.constants.Constants.DELIMATOR;
import static com.paytm.recharges.bff.constants.Constants.END_BENEFICIARY_NAME;
import static com.paytm.recharges.bff.constants.Constants.GROUP;
import static com.paytm.recharges.bff.constants.Constants.ISRISK;
import static com.paytm.recharges.bff.constants.Constants.IS_AMOUNT_EDITABLE_FLAG;
import static com.paytm.recharges.bff.constants.Constants.IS_GROUP_DISPLAY_ENABLED_FLAG;
import static com.paytm.recharges.bff.constants.Constants.IS_NAME_REQUIRED;
import static com.paytm.recharges.bff.constants.Constants.LANGUAGE_ENGLISH;
import static com.paytm.recharges.bff.constants.Constants.MOBILE;
import static com.paytm.recharges.bff.constants.Constants.ONE;
import static com.paytm.recharges.bff.constants.Constants.ORDER_COUNT_SET;
import static com.paytm.recharges.bff.constants.Constants.PARALLEL_VERIFY_FLAG;
import static com.paytm.recharges.bff.constants.Constants.PLATFORM_FEE;
import static com.paytm.recharges.bff.constants.Constants.PLATFORM_FEE_ADDITIONAL_LABEL;
import static com.paytm.recharges.bff.constants.Constants.PLATFORM_FEE_DEFAULT_INFO;
import static com.paytm.recharges.bff.constants.Constants.PLATFORM_FEE_FLAG;
import static com.paytm.recharges.bff.constants.Constants.PLATFORM_FEE_HASH;
import static com.paytm.recharges.bff.constants.Constants.PLATFORM_FEE_INFO;
import static com.paytm.recharges.bff.constants.Constants.PLATFORM_FEE_LABEL;
import static com.paytm.recharges.bff.constants.Constants.PLATFORM_FEE_LABEL_NEW;
import static com.paytm.recharges.bff.constants.Constants.PLATFORM_INFO;
import static com.paytm.recharges.bff.constants.Constants.PLATFORM_LABEL_V2;
import static com.paytm.recharges.bff.constants.Constants.PREPAID;
import static com.paytm.recharges.bff.constants.Constants.PRICE;
import static com.paytm.recharges.bff.constants.Constants.Risk_Segment_Identifier;
import static com.paytm.recharges.bff.constants.Constants.SEGMENT_ID;
import static com.paytm.recharges.bff.constants.Constants.SMARTREMINDER;
import static com.paytm.recharges.bff.constants.Constants.SUBS_INFO;
import static com.paytm.recharges.bff.constants.Constants.TXNCOUNT_DELIMITER;
import static com.paytm.recharges.bff.constants.Constants.TXN_COUNT;
import static com.paytm.recharges.bff.constants.Constants.VERSION;
import static com.paytm.recharges.bff.constants.Constants.X_USER_ID;

@Service
public class RechargeVerifyServiceImpl extends CheckoutCommonService implements RechargeVerifyService {
    private static final CustomLogger log = CustomLogManager.getLogger(RechargeVerifyServiceImpl.class);

    @Autowired
    private FulfillmentRechargeClient fulfillmentRechargeClient;

    @Autowired
    private SagaClient sagaClient;

    @Autowired
    private CleverTapServiceImpl cleverTapService;

    @Autowired
    private PolicyRuleEngineServiceImpl policyRuleEngineService;

    @Autowired
    private CartClient cartClient;

    @Autowired
    private RpsRestClient rpsRestClient;

    @Autowired
    private CartVerifyRequestMapper cartVerifyRequestMapper;

    @Autowired
    private CartVerifyResponseMapper cartVerifyResponseMapper;

    @Autowired
    private RechargeExpressClient rechargeExpressClient;

    @Autowired
    private RechargeHelperService rechargeHelperService;

    @Autowired
    private RechargeValidationService rechargeValidationService;

    @Autowired
    private ExecutorService rechargeVerifyExecutor;

    @Autowired
    private ExecutorService rpsExecutor;

    @Autowired
    private ExecutorService sagaExecutor;

    @Autowired
    private CreditCardRiskPayloadHandler creditCardRiskPayloadHandler;

    @Autowired
    private FFRParallelValidationService ffrParallelValidationService;

    @Autowired
    private
    CartParallelVerifyRequestHandler cartParallelVerifyRequestHandler;

    @Autowired
    private DeleteFromAllSystemServiceImpl deleteFromAllSystemService;


    @Value("${recharges.express.http.threadTimeout}")
    private int threadTimeout;

    @Value("${saga.customername.config.http.thread_timeout}")
    private int threadTimeoutSaga;


    @Autowired
    private MetricsAgent metricsAgent;

    @Autowired
    private ExecutorService cleverTapExecutor;

    @Autowired
    private ExecutorService policyRuleEngineExecutor;

    @Autowired
    private SagaManagerImpl sagaManager;

    @Autowired
    private AerospikeGenericWrapper aerospikeGenericWrapper;

    @Value("${recharge.bff.checkoutUrl}")
    private String checkoutUrl;

    @Value("${recharge.ct.config.thread_timeout}")
    private int threadTimeoutCTapi;

    @Value("${policyruleengine.config.thread_timeout}")
    private int threadTimeoutPREapi;

    @Override
    @MethodLatencyMetricsAction(metricsName = "do_verify_express")
    public ResponseEntity<Object> doVerifyExpress(Object body, Map<String, Object> requestParams,
                                                  MultiValueMap<String, String> headers) {

        Object updatedBody = rechargeHelperService.addProductInfo(body);

        try {
            Future<ResponseEntity<Object>> futureResponse = rechargeVerifyExecutor
                    .submit(EncryptionUtil.wrapCallableWithLTC(() ->
                            rechargeExpressClient.verify(rechargeHelperService.addOnDemandFlag(updatedBody), requestParams, headers)));
            return futureResponse.get(threadTimeout, TimeUnit.MILLISECONDS);
        } catch (ExecutionException e) {
            log.error("doVerifyExpress:: Error occurred requestParams {} ", requestParams, e);
            Throwable cause = e.getCause();
            if (cause instanceof RestClientException) {
                throw new RechargesException((((RestClientException) cause).getHttpStatus()), Utils.getRootCause(e).getMessage());
            }
            throw new RechargesException(HttpStatus.REQUEST_TIMEOUT, Utils.getRootCause(e).getMessage());
        } catch (InterruptedException | TimeoutException | RuntimeException e) {
            log.error("doVerifyExpress:: Error occurred requestParams {} ", requestParams, e);
            throw new RechargesException(HttpStatus.REQUEST_TIMEOUT, Utils.getRootCause(e).getMessage());
        }
    }

    private FFRValidateResponse futureCallForFFRValidate(RechargeVerifyRequest request, Map<String, Object> requestParams, Set<Long> rechargeProductIds) throws InterruptedException, ExecutionException, TimeoutException {
        if(requestParams.containsKey(PARALLEL_VERIFY_FLAG) && requestParams.get(PARALLEL_VERIFY_FLAG).equals(true)) {
            log.info("Going to call parallel validate for FFR");
            return ffrParallelValidationService.validate(request, requestParams, rechargeProductIds);
        }
        Future<FFRValidateResponse> futureValidateResponse = rechargeVerifyExecutor.submit(EncryptionUtil.wrapCallableWithLTC(() ->
                fulfillmentRechargeClient.validate(request, requestParams, rechargeProductIds)));

        return futureValidateResponse.get(threadTimeout, TimeUnit.MILLISECONDS);
    }

    private Map<String, Object> getResponseFromClevertap(Future<Map<String, Object>> future) {
        try {
            return future.get(threadTimeoutCTapi, TimeUnit.MILLISECONDS);

        } catch (Exception ex) {
            log.info("clever tap exception occured here {}", ex);

        }
        return null;
    }

    public void updateCartRequestSegmentId(VerifyRequest cartRequest, Long txnCount) {
        if((txnCount > 0L) && Objects.nonNull(cartRequest.getItems())) {
            if(Objects.nonNull(cartRequest.getItems().get(0).getConfiguration()) && !StringUtils.isEmpty(cartRequest.getItems().get(0).getConfiguration().get(SEGMENT_ID))) {
                String segmentId = ((String) cartRequest.getItems().get(0).getConfiguration().get(SEGMENT_ID)).concat(TXNCOUNT_DELIMITER).concat(String.valueOf(txnCount));
                cartRequest.getItems().get(0).getConfiguration().put(SEGMENT_ID, segmentId);
            }
            if(Objects.nonNull(cartRequest.getItems().get(0).getMetaData()) && !StringUtils.isEmpty(cartRequest.getItems().get(0).getMetaData().get(SEGMENT_ID))) {
                String segmentId = ((String) cartRequest.getItems().get(0).getMetaData().get(SEGMENT_ID)).concat(TXNCOUNT_DELIMITER).concat(String.valueOf(txnCount));
                cartRequest.getItems().get(0).getMetaData().put(SEGMENT_ID, segmentId);
            }
        }
    }

    public Long getTxnCount(Long categoryId, MultiValueMap<String, String> headers, RechargeVerifyRequest request) {
        Long txnCount = 0L;
        if(this.isPaymodeCountSupportEnabled(categoryId)) {
            String customerId = String.valueOf(headers.get(X_USER_ID).get(0));
            String identifier = customerId.concat(DELIMATOR).concat(String.valueOf(categoryId));
            Key key = new Key(SMARTREMINDER, ORDER_COUNT_SET, identifier); //customerid_categoryId
            txnCount = this.getTxnCountFromAerospike(key);
            for(RechargeItem rechargeItem : request.getCartItems()) {
                rechargeItem.getMetaData().put(TXN_COUNT, txnCount);
            }
        }
        return txnCount;
    }

    @Override
    @MethodLatencyMetricsAction(metricsName = "do_verify_with_prevalidation")
    public RechargeVerifyResponse doVerifyWithPreValidation(RechargeVerifyRequest request,
                                                            Map<String, Object> requestParams, MultiValueMap<String, String> headers)
            throws RechargesException {
        log.info("validate :: request received   {}", JsonUtil.toJson(request));
        Set<Long> rechargeProductIds = rechargeHelperService.filterRechargeItem(request);

        validateRequest(request, requestParams, headers);

        String [] keys = new String[] {Constants.LANGUAGE_ENGLISH,CLEVERTAP_EXECUTOR_THREAD,CONFIGURATION_LOCAL};
        HashMap<String,String> payload=new HashMap<>();
        String cleverTapConfigThreadTimeout=localisationManager.getMessage(keys, true, payload); //threadTimeout = 1000

        if(!StringUtils.isEmpty(cleverTapConfigThreadTimeout)) {
            try {
                threadTimeoutCTapi = Integer.parseInt(cleverTapConfigThreadTimeout);
            }catch ( NumberFormatException| NullPointerException ex){
                log.error("doVerifyWithPreValidation :: while reading threadtimeout from localization {}",ex);
            }
        }
        log.info("CleverTap thread : "+ threadTimeoutCTapi);

        try {
            boolean isNameRequired = false;
            SagaCustomerNameResponse sagaCustomerNameResponse = null ;
            try{
                if (requestParams.get(IS_NAME_REQUIRED) != null) {
                    isNameRequired = Boolean.parseBoolean((String) requestParams.get(IS_NAME_REQUIRED));
                }
                if(isNameRequired) {
                    String service =  CVRProductCache.getInstance().getProductDetails(request.getCartItems().get(0).getProductId()).getService();
                    String operator =  CVRProductCache.getInstance().getProductDetails(request.getCartItems().get(0).getProductId()).getOperator();
                    Long productId = CVRProductCache.getInstance().getProductDetails(request.getCartItems().get(0).getProductId()).getProductId();
                    CustomerNameRequest customerNameRequest = new CustomerNameRequest();
                    customerNameRequest.setCustomer_id(Long.valueOf(headers.get("x-user-id").get(0)));
                    customerNameRequest.setRecharge_number((String) request.getCartItems().get(0).getConfiguration().get("recharge_number"));
                    customerNameRequest.setService(service.toLowerCase());
                    customerNameRequest.setPid(productId);
                    customerNameRequest.setOperator(operator.toLowerCase());
                    Future<SagaCustomerNameResponse> customerNameResponseFuture = sagaExecutor.submit(EncryptionUtil.wrapCallableWithLTC(() ->
                            sagaManager.getCustomerNameFromSaga(customerNameRequest)));
                    try{
                        sagaCustomerNameResponse = customerNameResponseFuture.get(threadTimeoutSaga, TimeUnit.MILLISECONDS);
                    }catch (Exception ex) {
                        log.error("Customer Name  exception occured here {}",ex);
                    }
                }
            }catch (RuntimeException e){
                log.info("Error occured while calling SagaCustomer Name API {} ", requestParams, e);
            }

            VerifyRequest cartRequest = cartVerifyRequestMapper.map(request);

            //SETTING DISABLED_PAYMENT_MODES SENT FROM FRONTEND
            //TODO: We need to remove this manual setting of nested objects from verufyrequest to cartrequest, we should implement a way in which the verifyrequest can be deep copied to cartRequest. Currently we are using beanUtils.copyProperties but it does not copy the nested objects
            if(Objects.nonNull(request.getDisabledPaymentMethods())){
                cartRequest.setDisabledPaymentMethods(request.getDisabledPaymentMethods());
                metricsAgent.incrementEventCount(Constants.VERIFY_DISABLE_PAYMODES_FE);
            }
            //SETTING PAYMENT INTENTS IN CART REQUEST
            List<PaymentIntent> paymanetIntentList = null;
            if (request.getPaymentIntent() != null) {

                try {
                    ObjectMapper mapper = new ObjectMapper();
                    paymanetIntentList = request.getPaymentIntent() instanceof String ? mapper.readValue(request.getPaymentIntent().toString(), new TypeReference<List<PaymentIntent>>() {
                    }) : mapper.convertValue(request.getPaymentIntent(), new TypeReference<List<PaymentIntent>>() {
                    });
                } catch (Exception ex) {
                    log.info("error occurred during parsing paymentIntent ", ex);

                }
                cartRequest.setPaymentIntents(paymanetIntentList);
            }

            Long productId = cartRequest.getItems().get(0).getProductId();
            Product product =  CVRProductCache.getInstance().getProductDetails(productId);
            String client = String.valueOf(requestParams.get(CLIENT));
            String version = String.valueOf(requestParams.get(VERSION));

            String rechargeNumber = getRechargeNumber(cartRequest, 0, Constants.RECHARGE_NUMBER);
            Long categoryId = Long.valueOf(product.getCategoryId());

            boolean isSegmentId = false;
            if(Objects.nonNull(request.getCartItems())&& Objects.nonNull(request.getCartItems().get(0).getMetaData())&& request.getCartItems().get(0).getMetaData().containsKey(SEGMENT_ID)){
                isSegmentId=true;
            }
            //Getting segment Id from CT Pull API
            Future<Map<String, Object>> cleverTapPullResponseFuture = null;

            FFRValidateResponse validateResponse = new FFRValidateResponse();
            Map<String, Object> ctResponse=null;
            List<String> segmentIds = null;
            Long txnCount = 0L;
            List<String> additionalSegIdentifierList = new ArrayList<>();
            boolean isRisk = false;

            if(checkRiskCategory(productId, rechargeNumber)) {
                additionalSegIdentifierList.add(Risk_Segment_Identifier);
            }


            if(!isSegmentId){
                cleverTapPullResponseFuture = cleverTapExecutor.submit(EncryptionUtil.wrapCallableWithLTC(() ->
                        cleverTapService.getSegmentIdsViaCTApi(requestParams, headers, request, additionalSegIdentifierList)));
            }

            txnCount = getTxnCount(categoryId, headers, request);

            if(Objects.nonNull(cleverTapPullResponseFuture)) {
                ctResponse = getResponseFromClevertap(cleverTapPullResponseFuture);
            }
            log.info("Clevertap Response from get from future : {}", ctResponse);


            if(checkRiskCategory(productId, rechargeNumber)) {
                if(Objects.nonNull(ctResponse) && Objects.nonNull(ctResponse.get(Risk_Segment_Identifier))) {
                    List<String> riskSegment = (List<String>) ctResponse.get(Risk_Segment_Identifier);
                    if(Objects.nonNull(riskSegment) && (!riskSegment.isEmpty()) && Objects.equals(riskSegment.get(0), "1")) {
                        isRisk = true;
                    }
                }
                if(Objects.isNull(request.getPaymentIntent())) {
                    for(RechargeItem rechargeItem : request.getCartItems()) {
                        rechargeItem.getMetaData().put(ISRISK, isRisk);
                    }
                }
            }

            //has to check for rent category


            if(Objects.nonNull(ctResponse) && (ctResponse.size() > 0)) {
                segmentIds = (List<String>) ctResponse.get(CCF);
            }


            log.info("RechargeVerifyServiceImpl.doVerifyWithPreValidation :: cleverTap response {}", segmentIds);

            if (Objects.nonNull(request.getCartItems()) && Objects.nonNull(request.getCartItems().get(0).getConfiguration())){
                if (Objects.isNull(request.getCartItems().get(0).getMetaData())) {
                    request.getCartItems().get(0).setMetaData(new HashMap<>());
                }
                if(isSegmentId && !String.valueOf(request.getCartItems().get(0).getMetaData().get(SEGMENT_ID)).isEmpty()){
                    request.getCartItems().get(0).getConfiguration().put(SEGMENT_ID, request.getCartItems().get(0).getMetaData().get(SEGMENT_ID));
                    segmentIds = new ArrayList<>();
                    segmentIds.add((String) request.getCartItems().get(0).getMetaData().get(SEGMENT_ID));
                }else  if(Objects.nonNull(segmentIds)&&segmentIds.size()>0){
                    request.getCartItems().get(0).getConfiguration().put(SEGMENT_ID, String.valueOf(segmentIds.get(0)));
                    request.getCartItems().get(0).getMetaData().put(SEGMENT_ID, String.valueOf(segmentIds.get(0)));
                }else
                    request.getCartItems().get(0).getMetaData().put(SEGMENT_ID, "");
            }

            // set skip prevalidation
            cartRequest.getItems().forEach(item -> {
                if (rechargeProductIds.contains(item.getProductId())) {
                    item.setSkipPrevalidation(true);
                }
            });

            //GETTING PLATFORM FEE FROM POLICY ENGINE && FFR Response
            boolean isCallFFR = true;
            for(RechargeItem item : request.getCartItems()){
                if(Objects.nonNull(item.getConfiguration())&&Objects.nonNull(item.getConfiguration().get(Constants.PNS))&&(Integer.parseInt(item.getConfiguration().get(Constants.PNS).toString()) == 2)){
                    isCallFFR = false; break;
                }
            }
            if(isCallFFR) {
                log.info("FFR Request :: {}", request);
                validateResponse = futureCallForFFRValidate(request, requestParams, rechargeProductIds);
                log.info("get validation response::{}",validateResponse);
            }

            Future<RpsPlanResponse> futureRpsPlanResponse = null;
            if((MOBILE.equalsIgnoreCase(product.getService())) && PREPAID.equalsIgnoreCase(product.getPayType())) {
                Map<String, Object> metaData = request.getCartItems().get(0).getMetaData();
                if(metaData != null && (metaData.get("recharge_benefits") == null || metaData.get("plan_bucket") == null
                    || metaData.get("recharge_benefits") == "" || metaData.get("plan_bucket") == "")) {
                    log.info("RPS call for recharge number: {}", rechargeNumber);
                    futureRpsPlanResponse = rpsExecutor.submit(EncryptionUtil.wrapCallableWithLTC(() ->
                            rpsRestClient.getPlans(product.getOperator(), product.getCircle(),
                                Utils.doubleValue(request.getCartItems().get(0).getConfiguration().get(PRICE), 0.0),
                                rechargeNumber, productId)));
                    RpsPlanResponse rpsPlanResponse = futureRpsPlanResponse.get(threadTimeout, TimeUnit.MILLISECONDS);
                    log.info("Rps response : {}", rpsPlanResponse);
                    setPlanBucketAndRechargeBenefitsInMetaData(validateResponse, rpsPlanResponse);
                }
            }



            RechargeItem itemForPlatFormFee=null;
            RechargeItem subscriptionItemForPlatFormFee=null;
            String serviceRUItem=null;
//subscription item
            Double amount = 0.0;

            for (RechargeItem rechargeItem : request.getCartItems()) {
                Product productFromCache= CVRProductCache.getInstance().getProductDetails(rechargeItem.getProductId());
                if((Objects.isNull(cartRequest.getSkipConvFee())
                        || !cartRequest.getSkipConvFee())
                        && Objects.nonNull(productFromCache)){
                    Map<String, Object> metaData = rechargeItem.getMetaData();
                    if(Objects.nonNull(metaData)){
                        metaData.put("requestType","verify");
                    }
                    cartRequest.setSkipConvFee(CartUtil.getInstance(metricsAgent).skipConvFee(productFromCache.getCategoryId(),rechargeItem.getMetaData(),rechargeItem.getPrice()));
                }
                Long fsID= null;
                if(Objects.nonNull(productFromCache)){
                    fsID=productFromCache.getFulfillmentService();
                }
                if(Objects.nonNull(rechargeItem.getIsSubs()) && rechargeItem.getIsSubs()) {
                    if (Objects.isNull(subscriptionItemForPlatFormFee)) {
                        subscriptionItemForPlatFormFee = rechargeItem;
                    }
                }else if(this.isRechargesFsid(fsID)) {
                    if (Objects.isNull(itemForPlatFormFee)) {
                        itemForPlatFormFee = rechargeItem;
                    }
                    amount += rechargeItem.getPrice();
                }
            }

            boolean isFeatureLive=rechargeHelperService.isFeatureLive(rechargeProductIds,
                    FeatureConfigName.platformFeeEnable.name().toLowerCase(), client, version);
            boolean isFeatureLiveForSubscription = false;
            log.info("RechargeVerifyServiceImpl.isFeatureLive :: isFeatureLive {}", isFeatureLive);

            if(Objects.nonNull(subscriptionItemForPlatFormFee)) {
                 isFeatureLiveForSubscription = rechargeHelperService.isFeatureLive(Collections.singleton(subscriptionItemForPlatFormFee.getProductId()),
                        FeatureConfigName.platformFeeEnable.name().toLowerCase(), client, version);
            }
            if(!isFeatureLive) itemForPlatFormFee=null;
            if(!isFeatureLiveForSubscription) subscriptionItemForPlatFormFee=null;

            if(Objects.nonNull(itemForPlatFormFee) || Objects.nonNull(subscriptionItemForPlatFormFee)){
                if(Objects.isNull(itemForPlatFormFee)){
                    log.info("RU item is null");
                }else{
                    serviceRUItem=CVRProductCache.getInstance().getProductDetails(itemForPlatFormFee.getProductId()).getService();
                }
                if(Objects.isNull(subscriptionItemForPlatFormFee)){
                    log.info("Subscription item is null");
                }

                this.validateAndSetPlatformFee(requestParams,cartRequest,itemForPlatFormFee, subscriptionItemForPlatFormFee,segmentIds,amount);

                if( ((requestParams.containsKey(PLATFORM_INFO)&&Objects.nonNull(requestParams.get(PLATFORM_INFO))&&String.valueOf(requestParams.get(PLATFORM_INFO)).equalsIgnoreCase(PLATFORM_FEE_FLAG))||
                            (Objects.nonNull(serviceRUItem) && serviceRUItem.equalsIgnoreCase(MOBILE) ) )) {
                        setPaltformLabelV2(cartRequest);
                }
            }


            // set disable payment option in cart request
            if(isCallFFR) {
                List<PaymentMode> disableModes = rechargeHelperService.disablePaymentMethods(validateResponse);
                if (!disableModes.isEmpty()) {
                    log.info("Overriding payment methods in cart request by gateway = {}",disableModes);
                    cartRequest.setDisabledPaymentMethods(disableModes);
                    metricsAgent.incrementEventCount("overriding_disable_paymodes" );
                }
            }

            Map<String, String> benificieryInfo = new HashMap<>();
            Map attr = new HashMap<>();

            try {
                if (Objects.nonNull(product)&&Objects.nonNull(product.getAttributes())) {
                    attr = (new ObjectMapper().convertValue(product.getAttributes(), Map.class));
                }} catch (Exception  e) {
                log.error("Error while Fetching attributes "+e);
            }
            if (Objects.nonNull(attr)&&attr.containsKey(BENEFICIARY_NAME)&& Objects.nonNull(attr.get(BENEFICIARY_NAME)) && !attr.get(BENEFICIARY_NAME).toString().isEmpty()) {
                benificieryInfo.put(END_BENEFICIARY_NAME, (String) attr.get(BENEFICIARY_NAME));
                cartRequest.setBeneficiaryInfo(benificieryInfo);
            }
            else if (Objects.nonNull(product)&& Objects.nonNull(product.getBrand())&& !product.getBrand().isEmpty()) {
                benificieryInfo.put(END_BENEFICIARY_NAME, product.getBrand());
                cartRequest.setBeneficiaryInfo(benificieryInfo);
            }

            if(Objects.nonNull(cartRequest.getPaymentIntents())){
                Map<String,String> labels=this.getPlatformLabel(cartRequest.getPaymentIntents(), request.getCartItems().get(0));
                setLabelToMetaData(cartRequest,PLATFORM_FEE_LABEL,labels.get(PLATFORM_FEE_LABEL));
                setLabelToMetaData(cartRequest,PLATFORM_FEE_ADDITIONAL_LABEL,labels.get(PLATFORM_FEE_ADDITIONAL_LABEL));
                cartRequest.setPgConvFeeLabel(labels.get(PLATFORM_FEE_LABEL));

            }


//            UPDATING SEGMENT ID WITH SEGMENTID-tTXNCOUNT IN CART REQUEST;
//            changes in cartrequest will also be reflected in request as cartrequest is referance copy of request
            updateCartRequestSegmentId(cartRequest, txnCount);

//            cartRequest.setXReleaseEnvironment("dev4");

            if(requestParams.containsKey(PARALLEL_VERIFY_FLAG) && requestParams.get(PARALLEL_VERIFY_FLAG).equals(true)) {
                log.info("ParallelValidation : going to filter cartRequest as per : {} ", JsonUtil.toJson(cartRequest));
                cartParallelVerifyRequestHandler.filterOutCartItemsAsPerFFRValidateResponse(validateResponse, cartRequest);
            }

            log.info("doVerify :: cartRequest {} ", JsonUtil.toJson(cartRequest));
            Map data = getFlagsFromValidateResponse(validateResponse, categoryId);
            Long startTime = System.currentTimeMillis();
            Future<VerifyResponse> futureVerifyResponse = rechargeVerifyExecutor.submit(EncryptionUtil.wrapCallableWithLTC(() -> {
                if(requestParams.containsKey(CART_VERSION_V4) && requestParams.get(CART_VERSION_V4).equals(true)) {
                    return cartClient.verifyV4(cartRequest);
                }
                return cartClient.verifyV2(cartRequest);
            }));
            Future<String> futureAerospike = null;
            try {
                futureAerospike = rechargeVerifyExecutor.submit(EncryptionUtil.wrapCallableWithLTC(() -> {
                    if(!data.isEmpty()) {
                        aerospikeGenericWrapper.putData(BFF_NAMESPACE, Constants.ASYNC_VERIFY_FLAGS_KEY,
                                String.valueOf(productId), 7 * 24 * 3600, 3000, data);
                    }
                    return "";
                }));
            } catch (Exception e) {
                log.error("doVerifyWithPreValidation :: Exception while creating future object for aerospike put operation", e);
            }

            VerifyResponse cartResponse = futureVerifyResponse.get(threadTimeout, TimeUnit.MILLISECONDS);
            if (Objects.nonNull(futureAerospike)) {
                futureAerospike.get(threadTimeout, TimeUnit.MILLISECONDS);
            }
            Long timeTaken = System.currentTimeMillis()- startTime;

            log.info("doVerify :: cartResponse time taken {} ", timeTaken);
            log.info("doVerify :: cartResponse {} ", JsonUtil.toJson(cartResponse));
            RechargeVerifyResponse rechargeVerifyResponse = cartVerifyResponseMapper.map(cartResponse);
            if(Objects.nonNull(rechargeVerifyResponse.getHttpCode())) {
                metricsAgent.recordResponseCodeCountEvent("CartClientResponse_Verify", rechargeVerifyResponse.getHttpCode());
            }

            //added platform_meta_data

            if( (Objects.nonNull(itemForPlatFormFee) || Objects.nonNull(subscriptionItemForPlatFormFee))
                    &&( (requestParams.containsKey(PLATFORM_INFO)&&Objects.nonNull(requestParams.get(PLATFORM_INFO))&&String.valueOf(requestParams.get(PLATFORM_INFO)).equalsIgnoreCase(PLATFORM_FEE_FLAG))
                        || (Objects.nonNull(serviceRUItem) && serviceRUItem.equalsIgnoreCase(MOBILE) ))){
                settingPlatformMetaData(cartResponse, rechargeVerifyResponse, cartRequest, request);
                if(isCallFFR) {
                    for(RechargeItem rechargeItem:validateResponse.getCartItems()){
                        Product productFromCache= CVRProductCache.getInstance().getProductDetails(rechargeItem.getProductId());
                        Long fsID= null;
                        if(Objects.nonNull(productFromCache)){
                            fsID= productFromCache.getFulfillmentService();
                        }
                        if(Objects.nonNull(rechargeItem.getMetaData()) && Objects.nonNull(itemForPlatFormFee) && this.isRechargesFsid(fsID) && Objects.nonNull(itemForPlatFormFee.getMetaData().get(PLATFORM_FEE_HASH)) && Objects.nonNull(itemForPlatFormFee.getMetaData().get(PLATFORM_FEE))){
                            rechargeItem.getMetaData().put(PLATFORM_FEE_HASH,itemForPlatFormFee.getMetaData().get(PLATFORM_FEE_HASH));
                            rechargeItem.getMetaData().put(PLATFORM_FEE,itemForPlatFormFee.getMetaData().get(PLATFORM_FEE));
                        }
                        if(Objects.nonNull(rechargeItem.getMetaData()) && Objects.nonNull(subscriptionItemForPlatFormFee) && rechargeItem.getProductId().equals(subscriptionItemForPlatFormFee.getProductId()) && Objects.nonNull(subscriptionItemForPlatFormFee.getMetaData().get(PLATFORM_FEE_HASH)) && Objects.nonNull(subscriptionItemForPlatFormFee.getMetaData().get(PLATFORM_FEE))){
                            rechargeItem.getMetaData().put(PLATFORM_FEE_HASH,subscriptionItemForPlatFormFee.getMetaData().get(PLATFORM_FEE_HASH));
                            rechargeItem.getMetaData().put(PLATFORM_FEE,subscriptionItemForPlatFormFee.getMetaData().get(PLATFORM_FEE));
                        }
                    }
                }
            }

            setAccessTokenValueInResponse(rechargeVerifyResponse);

            if(requestParams.containsKey(PARALLEL_VERIFY_FLAG) && requestParams.get(PARALLEL_VERIFY_FLAG).equals(true)) {
                if(Objects.nonNull(request.getCartItems().get(0)) && Objects.nonNull(request.getCartItems().get(0).getOldProductId())) {
                    requestParams.put(Constants.OLD_PID, request.getCartItems().get(0).getOldProductId().toString());
                    deleteFromAllSystemService.deleteFromAllSystems(validateResponse, headers, requestParams, cartResponse);
                } else {
                    log.error("parallelVerify :: Old product id missing in RechargeVerifyRequest payload {} ", request);
                }
            }

            mergeResponse(validateResponse, rechargeVerifyResponse);
            if(Objects.nonNull(rechargeVerifyResponse)&&Objects.nonNull(rechargeVerifyResponse.getCart())&&Objects.nonNull(rechargeVerifyResponse.getCart().getItems())&& Objects.nonNull(rechargeVerifyResponse.getCart().getItems().get(0))&& Objects.nonNull(rechargeVerifyResponse.getCart().getItems().get(0).getMetaData()))
                rechargeVerifyResponse.getCart().getItems().get(0).getMetaData().put(SEGMENT_ID,request.getCartItems().get(0).getMetaData().get(SEGMENT_ID));
            try {
                if (isNameRequired) {
                    if (sagaCustomerNameResponse != null && sagaCustomerNameResponse.getCustomer_name() != null && rechargeVerifyResponse != null) {
                        rechargeVerifyResponse.getCart().setCustomer_name(sagaCustomerNameResponse.getCustomer_name());
                    }
                }
            }catch(RuntimeException exception){
                log.error("CustomerName :: Error occurred requestParams {} ", requestParams, exception);
            }

            updateSegmentId(rechargeVerifyResponse);
            return rechargeVerifyResponse;
        } catch (ExecutionException e) {
            log.error("doVerifyWithPreValidation:: Error occurred requestParams {} ", requestParams, e);
            Throwable cause = e.getCause();
            if (cause instanceof RestClientException) {
                throw new RechargesException((((RestClientException) cause).getHttpStatus()), Utils.getRootCause(e).getMessage());
            }
            if (cause instanceof RechargesException) {
                throw (RechargesException) cause;
            }
            throw new RechargesException(HttpStatus.REQUEST_TIMEOUT, Utils.getRootCause(e).getMessage());

        } catch (RechargesException e) {
            log.error("doVerifyWithPreValidation:: Error occurred requestParams {} ", requestParams, e);
            throw e;
        } catch (RestClientException e) {
            log.error("doVerifyWithPreValidation:: Error occurred requestParams {} ", requestParams, e);
            throw new RechargesException(e.getHttpStatus(), Utils.getRootCause(e).getMessage());
        } catch (InterruptedException | TimeoutException | RuntimeException e) {
            log.error("doVerifyWithPreValidation:: Error occurred requestParams {} ", requestParams, e);
            throw new RechargesException(HttpStatus.REQUEST_TIMEOUT, Utils.getRootCause(e).getMessage());
        }
    }

    private void setPlanBucketAndRechargeBenefitsInMetaData(FFRValidateResponse validateResponse,
                                                            RpsPlanResponse rpsPlanResponse) {
        if(validateResponse == null) {
            log.info("validation response is null so not setting rpsplan response");
            return;
        }
        List<ValidateCartItem> validateCartItems = validateResponse.getCartItems();
        if(!CollectionUtils.isEmpty(validateCartItems) && validateCartItems.get(0) != null) {
            ValidateCartItem validateCartItem = validateCartItems.get(0);
            Map<String, Object> metaData = validateCartItem.getMetaData();
            if(metaData == null) {
                metaData = new HashMap<>();
                validateCartItem.setMetaData(metaData);
            }
            if(!CollectionUtils.isEmpty(rpsPlanResponse.getProductList())) {
                List<RpsPlanResponse.RpsProduct> productList = rpsPlanResponse.getProductList();
                RpsPlanResponse.RpsProduct rpsProduct = productList.get(0);

                metaData.put("plan_id", rpsProduct.getProductId());
                metaData.put("producttype", rpsProduct.getProducttype());
                metaData.put("plan_bucket", rpsProduct.getPlanBucket());
                metaData.put("amount_plan_desc", rpsProduct.getAmountPlanDesc());

                Map<String, Object> rechargeBenefits = new HashMap<>();
                rechargeBenefits.put("validity", rpsProduct.getValidity());
                rechargeBenefits.put("talktime", rpsProduct.getTalktime());
                rechargeBenefits.put("couponDescription", rpsProduct.getDescription());
                rechargeBenefits.put("data", rpsProduct.getData());
                rechargeBenefits.put("sms", rpsProduct.getSms());
                rechargeBenefits.put("addon_benefit", rpsProduct.getAddon_benefit());
                rechargeBenefits.put("addon_benefit_v2", rpsProduct.getAddonBenefitV2());

                metaData.put("recharge_benefits",rechargeBenefits);

            }
        }
    }

    private void updateSegmentId(RechargeVerifyResponse rechargeVerifyResponse) {
        if(Objects.nonNull(rechargeVerifyResponse.getCart()) && Objects.nonNull(rechargeVerifyResponse.getCart().getItems())) {
            for (RechargeItem item : rechargeVerifyResponse.getCart().getItems()) {
                if(Objects.nonNull(item.getConfiguration()) && !StringUtils.isEmpty(item.getConfiguration().get(Constants.SEGMENT_ID))) {
                    String segmentId = (String) item.getConfiguration().get(Constants.SEGMENT_ID);
                    if(!StringUtils.isEmpty(segmentId) && segmentId.contains(Constants.TXNCOUNT_DELIMITER)) {
                        item.getConfiguration().put(Constants.SEGMENT_ID, segmentId.split(Constants.TXNCOUNT_DELIMITER)[0]);
                    }
                }
                if(Objects.nonNull(item.getMetaData()) && !StringUtils.isEmpty(item.getMetaData().get(Constants.SEGMENT_ID))) {
                    String segmentId = (String) item.getMetaData().get(Constants.SEGMENT_ID);
                    if(!StringUtils.isEmpty(segmentId) && segmentId.contains(Constants.TXNCOUNT_DELIMITER)) {
                        item.getMetaData().put(Constants.SEGMENT_ID, segmentId.split(Constants.TXNCOUNT_DELIMITER)[0]);
                    }
                }
            }
        }
    }

    /**
     * merge validation & cart response by ref_item_id
     *
     * @param validateResponse
     * @param cartResponse
     */
    public void mergeResponse(FFRValidateResponse validateResponse, RechargeVerifyResponse cartResponse) {
//        log.debug("mergeResponse::rechargeVerifyResponse {} validateResponse {}", JsonUtil.toJson(cartResponse),
//                JsonUtil.toJson(validateResponse));

        if (Objects.isNull(validateResponse) || Objects.isNull(cartResponse.getCart()) || !StringUtils.isEmpty(cartResponse.getCart().getError())) {
            return;
        }
        try {
            RechargeCart cart = cartResponse.getCart();
            if (!StringUtils.isEmpty(validateResponse.getError())) {
                cart.setError(validateResponse.getError());
                cart.setErrorInfo(validateResponse.getErrorInfo());
            }

            setGwParamsToPass(validateResponse, cart);

            boolean serviceOptions = false;
            if (Objects.nonNull(cart.getItems())) {
                Map<String, ValidateCartItem> itemMap = createItemMap(validateResponse);
                for (RechargeItem item : cart.getItems()) {
                    if (item.getConfiguration() != null && itemMap.containsKey(item.getConfiguration().get("ref_item_id"))) {
                        ValidateCartItem validationItem = itemMap.get(item.getConfiguration().get("ref_item_id"));
                        if (copyFFRData(item, validationItem)) {
                            serviceOptions = true;
                        }
                    }
                }
            }

            cartResponse.getCart().setPlaceOrderUrl(checkoutUrl);
            cartResponse.getCart().setServiceOptions(serviceOptions);
        } catch (Exception ex) {
            log.info("mergeResponse exception occurred" + ex);
        }

    }

    public void setAccessTokenValueInResponse(RechargeVerifyResponse rechargeVerifyResponse){
        if(Objects.nonNull(rechargeVerifyResponse)&&Objects.nonNull(rechargeVerifyResponse.getCart())&&Objects.nonNull(rechargeVerifyResponse.getCart().getPaymentInstruments())) {
            Map<String, Object> paymentInstruments = (Map<String, Object>) rechargeVerifyResponse.getCart().getPaymentInstruments();
            if(Objects.nonNull(paymentInstruments.get("body"))){
                Map<String, Object> paymentInstrumentsBody = (Map<String, Object>) paymentInstruments.get("body");
                if(Objects.nonNull(paymentInstrumentsBody.get(ACCESS_TOKEN_VALUE))) {
                    for(RechargeItem item : rechargeVerifyResponse.getCart().getItems()) {
                        if(Objects.nonNull(item.getMetaData().get(SUBS_INFO))) {
                            Map<String, Object> subscribe_info = (Map<String, Object>) item.getMetaData().get(SUBS_INFO);
                            subscribe_info.put(ACCESS_TOKEN_VALUE, paymentInstrumentsBody.get(ACCESS_TOKEN_VALUE));
                            item.getMetaData().put(SUBS_INFO, subscribe_info);
                        }
                    }
                }
            }
        }
    }

    public boolean copyFFRData(RechargeItem item, ValidateCartItem validationItem) {

        boolean serviceOptions = false;
        if (Objects.nonNull(validationItem)) {
            item.setServiceOptions(validationItem.getServiceOptions());
            item.setMetaData(validationItem.getMetaData());
            // merge metaData in meta_data
            if (Objects.nonNull(validationItem.getMetaDataOld())) {
                if (Objects.isNull(item.getMetaData())) {
                    item.setMetaData(new HashMap<>());
                }
                item.getMetaData().putAll(validationItem.getMetaDataOld());

            }

            if (Objects.nonNull(validationItem.getServiceOptions())) {
                serviceOptions = true;
            }
        }
        if (item.getConfiguration().containsKey("ref_item_id")) {
            item.getConfiguration().remove("ref_item_id");
        }
        item.setSkipPrevalidation(null);

        return serviceOptions;
    }

    private Map<String, ValidateCartItem> createItemMap(FFRValidateResponse validateResponse) {
        Map<String, ValidateCartItem> itemMap = new HashMap<>();
        for (ValidateCartItem item : validateResponse.getCartItems()) {
            // ref_item_id will be present in item.item_id
            itemMap.put(item.getItemId(), item);
        }
        return itemMap;
    }

    private void setLabelToMetaData(VerifyRequest input,String label,String value){
        for (Item rechargeItem : input.getItems()) {
            if(Objects.isNull(rechargeItem.getMetaData())){
                rechargeItem.setMetaData(new HashMap());
            }
            rechargeItem.getMetaData().put(label,value);

        }
    }


    //implemented new key-value pair in localisation panel
    private Map<String, String> preparePlatformAndConvenienceFeeLabel(Cart cart){
        Map<String,String> labels=new HashMap<String,String>();
        Map<String, String> payload=new HashMap<>();

        String [] platformFeeInfoKey = new String[] {LANGUAGE_ENGLISH, PLATFORM_FEE_INFO};
        labels.put(PLATFORM_FEE_INFO,localisationManager.getMessage(platformFeeInfoKey, true, payload));


        if(StringUtils.isEmpty(labels.get(PLATFORM_FEE_INFO))){
            labels.put(PLATFORM_FEE_INFO,localisationManager.getMessage(platformFeeInfoKey, true, payload));
            String label=(StringUtils.isEmpty(labels.get(PLATFORM_FEE_INFO)))? PLATFORM_FEE_DEFAULT_INFO:labels.get(PLATFORM_FEE_INFO);
            labels.put(PLATFORM_FEE_INFO,label);
        }

        if(Objects.nonNull(cart.getPlatformFeeLabel()))
            labels.put(PLATFORM_FEE_LABEL_NEW,cart.getPlatformFeeLabel());

        return labels;
    }

    private void settingPlatformMetaData(VerifyResponse cartResponse, RechargeVerifyResponse rechargeVerifyResponse, VerifyRequest cartRequest, RechargeVerifyRequest request) {
        if(Objects.nonNull(cartResponse.getVerify()) && Objects.nonNull(cartResponse.getVerify().getCart()) && Objects.nonNull(cartResponse.getVerify().getCart().getAggregatePlatformFee())) {
            Float aggPlatFee = cartResponse.getVerify().getCart().getAggregatePlatformFee();
            PlatformMetaData platformMetaData = new PlatformMetaData();
            platformMetaData.setAggregatePlatformFee(aggPlatFee);
            rechargeVerifyResponse.getCart().setPlatformMetaData(platformMetaData);
            addPlatFeeInformationInPlatformMetaData(rechargeVerifyResponse.getCart(),cartResponse.getVerify().getCart());
            addConvFeeInformationInPlatformMetaData(rechargeVerifyResponse.getCart(), cartRequest, request);
        }

    }
    private void addConvFeeInformationInPlatformMetaData(RechargeCart rechargeCart, VerifyRequest cartRequest, RechargeVerifyRequest request) {
        PlatformMetaData platformMetaData = rechargeCart.getPlatformMetaData();
        Map<String,String> labels=this.getPlatformLabel(cartRequest.getPaymentIntents(), request.getCartItems().get(0));
        platformMetaData.setConvFeeInfo(labels.get(CONV_FEE_INFO));
        platformMetaData.setConvFeeLabel(labels.get(PLATFORM_FEE_ADDITIONAL_LABEL));


        rechargeCart.setPlatformMetaData(platformMetaData);
    }

    private void addPlatFeeInformationInPlatformMetaData(RechargeCart rechargeCart, Cart cart ) {
        PlatformMetaData platformMetaData = rechargeCart.getPlatformMetaData();

        Map<String,String> labels = preparePlatformAndConvenienceFeeLabel(cart);

        platformMetaData.setPlatformFeeInfo(labels.get(PLATFORM_FEE_INFO));
        platformMetaData.setPlatformFeeLabel(labels.get(PLATFORM_FEE_LABEL_NEW));

        rechargeCart.setPlatformMetaData(platformMetaData);
    }

    private void setPaltformLabelV2(VerifyRequest cartRequest) {
        HashMap<String,String> payload=new HashMap<>();
        String [] platformLabelKey = new String[] {Constants.LANGUAGE_ENGLISH, Constants.PLATFORM_LABEL_V2};
        String locMessage = localisationManager.getMessage(platformLabelKey, true, payload);
        String value = (StringUtils.isEmpty(locMessage))?Constants.PLATFORM_DEFAULT_LABEL_V2:locMessage;
        setLabelToMetaData(cartRequest, PLATFORM_LABEL_V2, value);
        cartRequest.setPlatformFeeLabel(value);
    }


    private Map getFlagsFromValidateResponse(FFRValidateResponse ffrValidateResponse, Long categoryId) {
        Map data = new HashMap();
        try {
            if (Utils.isAsyncVerifyFlagUpdateAllowed(String.valueOf(categoryId))) {
                Map<String, Object> customerDataResponse =
                        (Map<String, Object>) ffrValidateResponse.getCartItems().get(0).getCustomerDataResponse();
                log.info("getFlagsFromValidateResponse :: Fetched Async Verify flags successfully");
                if(Objects.nonNull(customerDataResponse)) {
                    if(customerDataResponse.containsKey(IS_AMOUNT_EDITABLE_FLAG))
                        data.put(AMOUNT, ONE.equals(customerDataResponse.get(IS_AMOUNT_EDITABLE_FLAG)));
                    if(customerDataResponse.containsKey(IS_GROUP_DISPLAY_ENABLED_FLAG))
                        data.put(GROUP, ONE.equals(customerDataResponse.get(IS_GROUP_DISPLAY_ENABLED_FLAG)));
                }
            }
        } catch (Exception e) {
            log.error("getFlagsFromValidateResponse :: Exception while fetching Async Verify flags", e);
        }
        return data;
    }

    private void setGwParamsToPass(FFRValidateResponse validateResponse, RechargeCart cart) {
        try{
            if(!validateResponse.getCartItems().isEmpty()){
                Map<String, Object> validationGwResponse = validateResponse.getCartItems().get(0).getValidationGwResponse();
                if(validationGwResponse != null){
                    Object gwParamsToPassObj = validationGwResponse.get("gwParamsToPass");
                    if (gwParamsToPassObj instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> gwParamsToPass = (Map<String, Object>) gwParamsToPassObj;
                        cart.setGwParamsToPass(gwParamsToPass);
                    }
                }
            }
        } catch (Exception ex) {
            log.error("mergeResponse :: setGwParamsToPass exception occurred", ex);
        }
    }

}
