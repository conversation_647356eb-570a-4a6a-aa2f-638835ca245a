package com.paytm.recharges.bff.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.paytm.recharges.bff.client.AerospikeGenericWrapper;
import com.paytm.recharges.bff.constants.Constants;
import com.paytm.recharges.bff.constants.DateFormat;
import com.paytm.recharges.bff.datalayer.cache.ServiceConfigCache;
import com.paytm.recharges.bff.datalayer.dto.RequestBlockObj;
import com.paytm.recharges.bff.datalayer.dto.response.BlockRequestRespDto;
import com.paytm.recharges.bff.enums.RequestType;
import com.paytm.recharges.bff.monitor.MetricsAgent;
import com.paytm.recharges.bff.service.RequestBlocker;
import com.paytm.recharges.bff.utils.DateUtil;
import com.paytm.recharges.bff.utils.JsonUtil;
import com.paytm.recharges.bff.utils.Utils;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.MultiValueMap;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;


@Service
public class RequestBlockServiceImpl implements RequestBlocker {

	private static final CustomLogger log = CustomLogManager.getLogger(RequestBlockServiceImpl.class);

	@Autowired
	private MetricsAgent metricsAgent;

	@Autowired
	private AerospikeGenericWrapper aerospikeGenericWrapper;

	@Autowired
	private CustomValidateBlockLogger logger;

	private ServiceConfigCache serviceConfigCache = ServiceConfigCache.getInstance();

	public boolean shouldBlockValidateRequest(MultiValueMap<String, String> headers, Map<String, Object> requestParams,
			RequestType requestType) {

		// If channel doesn't match, then check for blacklist status flag.The check for
		// validationCount threshold and validation/recharges ratio.
		if (Objects.nonNull(headers) && ObjectUtils.isNotEmpty(headers.get(Constants.CUSTOMER_ID))
				&& Objects.nonNull(headers.get(Constants.CUSTOMER_ID).get(0))) {
			return validateBlockRequest(headers.get(Constants.CUSTOMER_ID).get(0), true, requestType);
		}

		return false;
	}

	private boolean validateBlockRequest(String customerId, boolean isBlockRequest, RequestType requestType) {
		int validationCount, rechargeCount;
		validationCount = rechargeCount = 0;

		BlockRequestRespDto currentBlockRequestData = new BlockRequestRespDto();

		for (int i = 0; i < serviceConfigCache.getBlockRequestConfig().getBlackListCalcDays(); i++) {

			// calculate date
			String localDate = DateUtil.formatDate(LocalDate.now().minusDays(i), DateFormat.DATE_TIME_FORMAT_6);

			// Cache key
			String key = customerId + "_" + localDate;

			// Data retrieved from aerospike
			BlockRequestRespDto blockRequestData = aerospikeGenericWrapper.getData(Constants.BFF_NAMESPACE,
					Constants.BLOCK_REQUEST_SET, key, serviceConfigCache.getBlockRequestConfig().getCacheTimeoutInMs(), BlockRequestRespDto.class);

			log.info("###### 12 : blockRequestData : {}", blockRequestData);
			if (Objects.nonNull(blockRequestData)) {
				if (i == 0) {
					if (Objects.equals(blockRequestData.getBlacklistStatus(), 1)) {
						if (isBlockRequest) {
							log.info(
									"Customer blocked for validation based on blacklist status :: customerId: {}, validationCount: {}, rechargeCount: {}",
									customerId, blockRequestData.getValidationCount(),
									blockRequestData.getRechargeCount());

							logBlockedRequestData(Long.valueOf(customerId), localDate,
									System.currentTimeMillis() / 1000, requestType.toString(),
									blockRequestData.getValidationCount());

							List<String> tags = new ArrayList<>();
							tags.add("cust_id:" + customerId);

							metricsAgent.incrementEventCount("block_validate_req", tags);
						}
						return true;
					}
					currentBlockRequestData = blockRequestData;
				}
				validationCount += blockRequestData.getValidationCount();
				rechargeCount += blockRequestData.getRechargeCount();
			}
		}

		return validateBlockRequestLogic(customerId, isBlockRequest, requestType, validationCount, rechargeCount,
				currentBlockRequestData);
	}

	private boolean validateBlockRequestLogic(String customerId, boolean isBlockRequest, RequestType requestType,
			int validationCount, int rechargeCount, BlockRequestRespDto currentBlockRequestData) {
		
		if (validationCount > serviceConfigCache.getBlockRequestConfig().getValidationThreshold()
				&& (rechargeCount == 0 || Utils.roundToDecimalPlaces((double) validationCount / rechargeCount, 2) > serviceConfigCache
						.getBlockRequestConfig().getValidationRechargeRatio())) {

			if (isBlockRequest) {
				log.info(
						"Customer blacklisted for validation based on validation threshold and validationToRechargeRatio logic :: customerId: {}, totalValidationCount: {}, totalRechargeCount: {}",
						customerId, validationCount, rechargeCount);
				logBlockedRequestData(Long.valueOf(customerId),
						DateUtil.formatDate(LocalDate.now(), DateFormat.DATE_TIME_FORMAT_6),
						System.currentTimeMillis() / 1000, requestType.toString(), validationCount);

				List<String> tags = new ArrayList<>();
				tags.add("cust_id:" + customerId);

				metricsAgent.incrementEventCount("block_validate_req", tags);

				String localDate = DateUtil.formatDate(LocalDate.now(), DateFormat.DATE_TIME_FORMAT_6);
				String key = customerId + "_" + localDate;

				BlockRequestRespDto blockRequestData = BlockRequestRespDto.builder().blacklistStatus(1)
						.validationCount(currentBlockRequestData.getValidationCount() + 1)
						.rechargeCount(currentBlockRequestData.getRechargeCount()).build();

				addBlockedReqDataToCache(key, blockRequestData);
			}

			return true;
		}

		return false;
	}

	// log the blocked request data to csv through logger/log4j2
	private void logBlockedRequestData(Long customerId, String requestDate, Long requestTime, String requestType,
			Integer validationCount) {
		RequestBlockObj requestBlockObj = RequestBlockObj.builder().customerId(customerId)
				.requestBlockedDate(requestDate).requestBlockedTime(requestTime).requestCount(validationCount)
				.requestType(requestType).build();

		logger.info(requestBlockObj);
	}

	@Async(Constants.BLOCK_REQUEST_EXECUTOR)
	public void addRequestDataToCache(MultiValueMap<String, String> headers, RequestType requestType, Integer count) {
		if (Objects.nonNull(headers) && ObjectUtils.isNotEmpty(headers.get(Constants.CUSTOMER_ID))
				&& Objects.nonNull(headers.get(Constants.CUSTOMER_ID).get(0))) {

			// calculate current date
			String localDate = DateUtil.formatDate(LocalDate.now(), DateFormat.DATE_TIME_FORMAT_6);

			// Cache key
			String key = headers.get(Constants.CUSTOMER_ID).get(0) + "_" + localDate;

			// Data retrieved from aerospike
			BlockRequestRespDto blockRequestData = aerospikeGenericWrapper.getData(Constants.BFF_NAMESPACE,
					Constants.BLOCK_REQUEST_SET, key, serviceConfigCache.getBlockRequestConfig().getCacheTimeoutInMs(), BlockRequestRespDto.class);

			if (Objects.isNull(blockRequestData)) {
				blockRequestData = new BlockRequestRespDto();
			}

			if (RequestType.VALIDATE == requestType) {
				blockRequestData.setValidationCount(blockRequestData.getValidationCount() + count);
			} else if (RequestType.RECHARGE == requestType) {
				blockRequestData.setRechargeCount(blockRequestData.getRechargeCount() + count);

				// Check for excessive transactions
				checkExcessiveTransactions(headers.get(Constants.CUSTOMER_ID).get(0), blockRequestData.getRechargeCount());
			}

			if(Objects.equals(blockRequestData.getValidationCount(), 100)) {
				log.info(
						"Validation count is equal to 100 for customerId: {}, currentValidationCount: {}, currentRechargeCount: {}",
						headers.get(Constants.CUSTOMER_ID).get(0), blockRequestData.getValidationCount(),
						blockRequestData.getRechargeCount());
			}
			
			if (validateBlockRequestLogic(headers.get(Constants.CUSTOMER_ID).get(0), false, requestType,
					blockRequestData.getValidationCount(), blockRequestData.getRechargeCount(), null)) {
				blockRequestData.setBlacklistStatus(1);
			}

			addBlockedReqDataToCache(key, blockRequestData);
		}
	}

	private void addBlockedReqDataToCache(String key, BlockRequestRespDto blockRequestData) {
		try {
			blockRequestData.setKey(key);
			Integer cacheExpiryTime = (serviceConfigCache.getBlockRequestConfig().getBlockCacheTtl() + 1) * 24 * 60 * 60;
			// Cache the data
			aerospikeGenericWrapper.putData(Constants.BFF_NAMESPACE, Constants.BLOCK_REQUEST_SET, key,
					cacheExpiryTime, serviceConfigCache.getBlockRequestConfig().getCacheTimeoutInMs(),
					JsonUtil.convertObjectToMap(blockRequestData, new TypeReference<Map<String, Object>>() {
					}));
			log.info("######## 11 Constants.BFF_NAMESPACE : {}, Constants.BLOCK_REQUEST_SET : {}, key : {} , cacheExpiryTime : {}, serviceConfigCache.getBlockRequestConfig().getCacheTimeoutInMs() : {}, JsonUtil.convertObjectToMap(blockRequestData : {}, JsonUtil.convertObjectToMap(blockRequestData, new TypeReference<Map<String, Object>>()) : {}", Constants.BFF_NAMESPACE, Constants.BLOCK_REQUEST_SET, key, cacheExpiryTime, serviceConfigCache.getBlockRequestConfig().getCacheTimeoutInMs(), JsonUtil.convertObjectToMap(blockRequestData, new TypeReference<Map<String, Object>>() {}));
			log.info(Constants.BFF_NAMESPACE);
			log.info(Constants.BLOCK_REQUEST_SET);
			log.info(String.valueOf(cacheExpiryTime));
			log.info(String.valueOf(serviceConfigCache.getBlockRequestConfig().getCacheTimeoutInMs()));
			log.info(String.valueOf(JsonUtil.convertObjectToMap(blockRequestData, new TypeReference<Map<String, Object>>() {})));
		} catch (JsonProcessingException e) {
			log.error("addBlockedReqDataToCache :: Error while updating data in cache ", e);
		}
	}

	// Checks if checkout count exceeds thresholds and sends metrics to Prometheus
	public void checkExcessiveTransactions(String customerId, int checkoutCount) {

		List<Integer> checkoutAlertThresholds = serviceConfigCache.getBlockRequestConfig().getCheckoutAlertThresholds();

		// Check if the checkout count matches any threshold
		if (checkoutAlertThresholds!=null && checkoutAlertThresholds.contains(checkoutCount)) {

			log.info("Checkout threshold breached for customerId: {}", customerId);

			// Send metrics with customer_id
			List<String> tags = new ArrayList<>();
			tags.add(Constants.CUSTOMER_ID_METRIC_TAG + customerId);
			metricsAgent.incrementEventCount(Constants.CHECKOUT_THRESHOLD_METRIC, tags);
		}
	}

}
