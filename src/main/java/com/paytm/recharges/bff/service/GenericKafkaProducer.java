package com.paytm.recharges.bff.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

@Service
public class GenericKafkaProducer {

    private final KafkaTemplate<String, String> interstitialKafkaTemplate;

    @Autowired
    public GenericKafkaProducer(KafkaTemplate<String, String> interstitialKafkaTemplate) {
        this.interstitialKafkaTemplate = interstitialKafkaTemplate;
    }

    public void sendInterstitialMessage(String msgObj, String topicName) {
        interstitialKafkaTemplate.send(topicName, msgObj);
    }
}
