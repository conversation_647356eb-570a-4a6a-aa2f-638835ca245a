package com.paytm.recharges.bff.service.impl;

import com.paytm.recharges.bff.aop.annotations.MethodLatencyMetricsAction;
import com.paytm.recharges.bff.constants.Constants;
import com.paytm.recharges.bff.constants.InterstitialConstants;
import com.paytm.recharges.bff.datalayer.cache.CVRProductCache;
import com.paytm.recharges.bff.datalayer.dto.InterstitialDataRecord;
import com.paytm.recharges.bff.datalayer.dto.response.HomeReminderResponse;
import com.paytm.recharges.bff.datalayer.model.Product;
import com.paytm.recharges.bff.enums.EventState;
import com.paytm.recharges.bff.repository.InterstitialRepository;
import com.paytm.recharges.bff.service.RecentLocalisationManager;
import com.paytm.recharges.bff.utils.DateUtil;
import com.paytm.recharges.bff.datalayer.dto.response.SagaRecentResponse;
import com.paytm.recharges.bff.datalayer.cache.ServiceConfigCache;
import com.paytm.recharges.bff.utils.InterstitialUtils;
import com.paytm.recharges.bff.utils.JsonUtil;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;

import static com.paytm.recharges.bff.constants.RecentConstants.PayloadRelatedConstants.*;

@Service
public class InterstitialManagerImpl {
    private static final CustomLogger log = CustomLogManager.getLogger(InterstitialManagerImpl.class);

    @Autowired
    private InterstitialRepository interstitialRepository;

    @Autowired
    private RecentLocalisationManager localisationManager;

    /**
    * Retrieves existing interstitial impression counts for eligible saga responses.
    *
    * This method performs the following steps:
    * 1. Validates if interstitial display is enabled for any services
    * 2. Filters eligible saga responses based on service and business rules
    * 3. Generates unique bill keys and cooloff keys for each eligible response
    * 4. Fetches impression counts from Aerospike in a batch operation
    *
    * Eligibility Criteria:
    * - Service must be enabled for interstitial display
    * - For MOBILE PREPAID service: expiry date should be today or tomorrow
    * - Bill should be a full bill (not partial)
    * - Bill due date should be in future
    * - Should not be a manual SMS parsing case
    *
    * Error Handling:
    * - Returns empty map if no services are enabled
    * - Returns null if Aerospike operation fails
    * - Returns null if repository returns null response
    *
    * @param sagaRecentResponses List of saga responses to check for interstitial eligibility
    * @return Map<String, Integer> where:
    *         - Key: Unique bill key or cooloff key
    *         - Value: Number of impressions shown
    *         - null: In case of errors or invalid response from repository
    *         - Empty map: If no eligible responses or no enabled services
    *
    * @see InterstitialRepository#getInterstitialData
    * @see #isEligibleForInterstitial
    * @see #generateBillKey
    */
    @MethodLatencyMetricsAction(metricsName = "getEligibleInterstitalExistingImpression")
    public Map<String, InterstitialDataRecord> getEligibleInterstitalExistingImpression(ArrayList<SagaRecentResponse> sagaRecentResponses) {
        long interstitial_start_time = System.currentTimeMillis();
        Map<String, InterstitialDataRecord> existingInterstitialMap = new HashMap<>();
        List<String> keysToFetch = new ArrayList<>();

        // Get list of services enabled for interstitial display
        List<String> enabledServices = ServiceConfigCache.getInstance().getList(InterstitialConstants.INTERSTITIAL_DISPLAY_SERVICE);
        if (CollectionUtils.isEmpty(enabledServices)) {
            log.info("No services enabled for interstitial display");
            return existingInterstitialMap;
        }

        // Process each response and generate keys
        for (SagaRecentResponse sagaRecentResponse : sagaRecentResponses) {
            Product product = CVRProductCache.getInstance().getProductDetails(Long.parseLong(sagaRecentResponse.getPid()));
            if (!isEligibleForInterstitial(sagaRecentResponse, enabledServices ,product)) {
                continue;
            }

            String billKey = generateBillKey(sagaRecentResponse, product);
            String cooloffKey = billKey + InterstitialConstants.INTERSTITIAL_BILLKEY_COOL_OFF_SUFFIX;

            keysToFetch.add(billKey);
            existingInterstitialMap.put(billKey, new InterstitialDataRecord());

            keysToFetch.add(cooloffKey);
            existingInterstitialMap.put(cooloffKey, new InterstitialDataRecord());

            log.info("InterstitialManagerImpl.getEligibleInterstitalExistingImpression :: billKey: {}, cooloffKey: {}", billKey, cooloffKey);
        }

        // Fetch data from Aerospike
        if (!keysToFetch.isEmpty()) {
            try {
                Map<String, InterstitialDataRecord> interstitialData = interstitialRepository.getInterstitialData(keysToFetch);
                if (interstitialData != null) {
                    existingInterstitialMap.putAll(interstitialData);
                } else {
                    log.error("Null response received from interstitialRepository.getInterstitialData for keys: {}", keysToFetch);
                    existingInterstitialMap = null;
                }
            } catch (Exception e) {
                log.error("Error while fetching interstitial data for keys: {}, Error: {}", keysToFetch, e.getMessage());
                // Fallback: initialize with default values
                existingInterstitialMap = null;
            }
        } else
            return null;

        long interstitial_end_time = System.currentTimeMillis();
        double interstitial_time_difference = (interstitial_end_time - interstitial_start_time);
        log.info("getEligibleInterstitalExistingImpression fetch time taken ----->{}", interstitial_time_difference);
        log.info("InterstitialManagerImpl.getEligibleInterstitalExistingImpression :: existingInterstitialMap: {}", JsonUtil.toJson(existingInterstitialMap));
        return existingInterstitialMap;
    }

    public int checkAndAddInterstitialDetails(HashMap<String, Object> frequentOrder , HomeReminderResponse reminderResponse, Map<String, InterstitialDataRecord> impressionMap) {
        long interstitial_start_time = System.currentTimeMillis();
        Map<String, Object> customProperties = new HashMap<>();
        Product product =  CVRProductCache.getInstance().getProductDetails(Long.valueOf((String) frequentOrder.get(Constants.RECENT_PRODUCT_ID)));
        String billKey = generateBillKey(frequentOrder, product);
        try {
            boolean isEligible = checkBillLevelInterstitialEligibility(billKey, impressionMap, reminderResponse);
            if (isEligible) {
                customProperties.put(InterstitialConstants.SHOW_POPUP, true);
                customProperties.put(InterstitialConstants.DISPLAY_API, getImpressionAPIURL(reminderResponse, product, frequentOrder));
                customProperties.put(InterstitialConstants.BILL_AMOUNT, getHeader(reminderResponse));
                customProperties.put(InterstitialConstants.BILL_DUE, getDisplayMessage(reminderResponse, product, frequentOrder));
                reminderResponse.setCustomProperties(customProperties);
                log.info("InterstitialManagerImpl.checkAndAddInterstitialDetails :: reminderResponse: {}", JsonUtil.toJson(reminderResponse));
            }
            long interstitial_end_time = System.currentTimeMillis();
            double interstitial_time_difference = (interstitial_end_time - interstitial_start_time);
            log.info("checkAndAddInterstitialDetails fetch time taken ----->{}", interstitial_time_difference);
            return impressionMap.getOrDefault(billKey, new InterstitialDataRecord()).getSeenCount();
        } catch (Exception e) {
            log.error("Error in checking interstitial eligibility for customerId: {}", frequentOrder.get("customerId"), e);
            return 0;
        }
    }

    private boolean isEligibleForInterstitial(SagaRecentResponse sagaRecentResponse, List<String> enabledServices, Product product) {
        String service = product.getService();
        if (!enabledServices.contains(service.toLowerCase())) {
            return false;
        }

        LocalDate today = LocalDate.now();
        LocalDate expiryLocalDate = null;
        LocalDate tomorrow = today.plusDays(1);


        // Convert expiry date to LocalDate
        if (sagaRecentResponse.getBill() != null && sagaRecentResponse.getBill().getExpiryDate() != null) {
            Date expiryDate = DateUtil.stringToDate(
                    sagaRecentResponse.getBill().getExpiryDate(),
                    Constants.DATE_FORMAT_FOR_TIMESTAMP
            );
            if (expiryDate != null) {
                expiryLocalDate = expiryDate.toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDate();
            }
        }


        // If service is MOBILE and expiry is not today or tomorrow
        if (Constants.MOBILE.equalsIgnoreCase(service) && Constants.PREPAID.equalsIgnoreCase(product.getPayType()) && expiryLocalDate != null) {
            if (!expiryLocalDate.equals(today) && !expiryLocalDate.equals(tomorrow)) {
                return false;
            }
        }

        // If it is an Automatic card and it has not failed yet then interstitial will not be shown
        if(Objects.nonNull(sagaRecentResponse.getAutomaticState())
                && sagaRecentResponse.getAutomaticState() == 1
                && !EventState.RECHARGE_AUTOMATIC_FAILURE.name().equalsIgnoreCase(sagaRecentResponse.getEventState()))
            return false;

        // Final eligibility check
        return !Boolean.TRUE.equals(sagaRecentResponse.getIsDwhSmsParsingManual()) &&
                Boolean.TRUE.equals(sagaRecentResponse.getIsFullBill()) &&
                sagaRecentResponse.getBill() != null &&
                sagaRecentResponse.getBill().getBillDueDate() != null &&
                !today.isAfter(DateUtil.stringToDate(
                        sagaRecentResponse.getBill().getBillDueDate(),
                        Constants.DATE_FORMAT_FOR_TIMESTAMP
                ).toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
    }

    public String generateBillKey(Object input, Product product) {
        String customerId = "", billDueDate = null, planBucket = null, rechargeNumber = "";
        String operator = "", service = "";

        if (input instanceof SagaRecentResponse) {
            SagaRecentResponse saga = (SagaRecentResponse) input;
            customerId = String.valueOf(saga.getCustomerId());
            rechargeNumber = saga.getRechargeNumber1();
            operator = encode(Objects.toString(product.getOperator(), "").toLowerCase());
            service = Objects.toString(product.getService(), "").toLowerCase();

            if (saga.getBill() != null) {
                billDueDate = DateUtil.stringDateFormat(saga.getBill().getBillDueDate(), Constants.DATE_FORMAT_FOR_TIMESTAMP, Constants.DATE_FORMAT_1);
                planBucket = !StringUtils.isEmpty(saga.getBill().getPlan_bucket()) ? saga.getBill().getPlan_bucket().toLowerCase() : null;
            }

        } else if (input instanceof Map) {
            Map<String, Object> frequentOrder = (Map<String, Object>) input;
            customerId = Objects.toString(frequentOrder.get("customerId"), "");
            operator = encode(Objects.toString(product.getOperator(), "").toLowerCase());
            service = Objects.toString(product.getService(), "").toLowerCase();

            if(ObjectUtils.isNotEmpty(frequentOrder.get(Constants.CONFIGURATION_LOCAL.toLowerCase(Locale.ROOT)))) {
                rechargeNumber= (String)((HashMap<String, Object>) frequentOrder.get(Constants.CONFIGURATION_LOCAL.toLowerCase(Locale.ROOT))).get(Constants.RECHARGE_NUMBER);
            }

            if (frequentOrder.containsKey("bills") && Objects.nonNull(frequentOrder.get("bills"))) {
                try {
                    ArrayList<HashMap<String, Object>> billist = (ArrayList<HashMap<String, Object>>) frequentOrder.get("bills");
                    if (!CollectionUtils.isEmpty(billist)) {
                        HashMap<String, Object> bill = billist.get(0);
                        if (bill != null) {
                            Object dueDate = bill.get("due_date");
                            Object planBucketObj = bill.get("plan_bucket");

                            billDueDate = getFormattedDueDate(bill);
                            planBucket = planBucketObj != null ? planBucketObj.toString().toLowerCase() : null; //check for empty
                        }
                    }
                } catch (Exception e) {
                    log.error("Error processing bills data for customerId: {}, Error: {}", customerId, e.getMessage());
                }
            }

        } else {
            throw new IllegalArgumentException("Unsupported input type for generateBillKey");
        }

        return String.format("%s_%s_%s_%s_%s_%s",
                customerId,
                rechargeNumber,
                operator,
                service,
                billDueDate != null ? billDueDate : "null",
                planBucket != null ? planBucket : "null"
        );
    }

    private String getFormattedDueDate(HashMap<String, Object> bill) {
        if (Objects.nonNull(bill.get("due_date")))
            return DateUtil.stringDateFormat(bill.get("due_date").toString(), Constants.DATE_FORMAT_1, Constants.DATE_FORMAT_1);
        else if (Objects.nonNull(bill.get("expiry")))
            return DateUtil.stringDateFormat(bill.get("expiry").toString(), Constants.DATE_FORMAT_1, Constants.DATE_FORMAT_1);
        else
            return null;
    }

    private String encode(String value) {
        try {
            return URLEncoder.encode(value, StandardCharsets.UTF_8.toString());
        } catch (Exception e) {
            log.error("Failed to URL encode: {}", value, e);
            return value;
        }
    }

//    private String generateUniqueNumber(String service, Map<String, Object> jsonObject) {
//        String uniqueNumber = jsonObject.get("recharge_number").toString();
//        if (Constants.FINANCIAL_SERVICES.equalsIgnoreCase(service)) {
//            return jsonObject.get("recharge_number_3").toString();
//        }
//        return uniqueNumber;
//    }


    private String getImpressionAPIURL(HomeReminderResponse homeReminderResponse, Product product, Map<String, Object> frequentOrder) {
        String[] key = new String[]{
                Constants.LANGUAGE_ENGLISH,  // Language code
                Constants.RECO,             // Base key
                "deeplink_interstitial_impression"  // Message type
        };

        HashMap<String, String> payload = new HashMap<>();
        HashMap<String, Object> bill = new HashMap<>();

        if (frequentOrder.containsKey("bills") && Objects.nonNull(frequentOrder.get("bills"))) {
            ArrayList<HashMap<String, Object>> billist;
            billist = (ArrayList<HashMap<String, Object>>) frequentOrder.get("bills");
            bill = billist.get(0);
        }

        // Add all required parameters for the URL
        payload.put("rechargeNumber", homeReminderResponse.getRechargeNumber());
        payload.put("operator", encode(Objects.toString(product.getOperator(), "").toLowerCase()));
        payload.put("customerId", String.valueOf(Objects.toString(frequentOrder.get("customerId"), "").toLowerCase()));
        payload.put("payType", encode(Objects.toString(product.getPayType(), "").toLowerCase()));
        payload.put("service", encode(Objects.toString(product.getService(), "").toLowerCase()));
        payload.put("productId", String.valueOf(product.getProductId()));

        payload.put("planBucket",
                StringUtils.isNotBlank((String) bill.get("plan_bucket"))
                        ? encode(((String) bill.get("plan_bucket")).toLowerCase())
                        : "null");

        payload.put("amount",
                Objects.nonNull(homeReminderResponse.getAmount())
                        ? homeReminderResponse.getAmount().toString()
                        : "null");

        payload.put("circle",
                StringUtils.isNotBlank(product.getCircle())
                        ? encode(product.getCircle().toLowerCase())
                        : "null");

        payload.put("expiry",  StringUtils.isNotBlank((String)bill.get("expiry"))
                ? (String) bill.get("expiry")
                : "null");
        payload.put("dueDate", StringUtils.defaultString(DateUtil.stringDateFormat(homeReminderResponse.getDueDate(),Constants.DATE_FORMAT_FOR_TIMESTAMP, Constants.DATE_FORMAT_1)));

        return localisationManager.getMessage(key, true, payload);
    }

    /**
     * Calculates the remaining number of interstitial displays allowed for a customer based on their daily and monthly display counts.
     *
     * This method performs the following steps:
     * 1. Checks if interstitial display feature is enabled via INTERSTITIAL_DISPLAY_FLAG
     * 2. Validates the customer ID
     * 3. Retrieves display limits from service configuration
     * 4. Queries Aerospike for current display counts using batch operation
     * 5. Calculates remaining displays if within both daily and monthly limits
     *
     * Aerospike Query Details:
     * - Uses InterstitialRepository to perform batch get operation
     * - Queries two keys:
     *   a) Monthly key: custId_MM_YYYY (e.g., "123_03_2024")
     *   b) Daily key: custId_DD_MM_YYYY (e.g., "123_15_03_2024")
     *
     * Service Config Parameters:
     * - INTERSTITIAL_DISPLAY_FLAG: Feature toggle for interstitial display
     * - INTERSTITIAL_DISPLAY_PER_MONTH: Maximum allowed displays per month
     * - INTERSTITIAL_DISPLAY_PER_DAY: Maximum allowed displays per day
     *
     * @param customerId The customer ID to check eligibility for
     * @return int:
     *         - 0 if:
     *           - Feature is disabled
     *           - Customer ID is invalid
     *           - Display limits are not configured
     *           - Either daily or monthly limit is reached
     *         - (monthlyLimit - monthlyCount) if both daily and monthly limits are not reached
     */
    public int checkAndGetEligibleInterstitial(Long customerId) {
        // Input validation
        int eligibleInterstitial = 0;
        Map<String, InterstitialDataRecord> displayCounts;

        if (ServiceConfigCache.getInstance().getBoolean(InterstitialConstants.INTERSTITIAL_DISPLAY_FLAG)){
            if (Objects.isNull(customerId)) {
                return 0;
            }

            // Get display limits from service config
            Integer monthlyLimit = ServiceConfigCache.getInstance().getInteger(InterstitialConstants.INTERSTITIAL_DISPLAY_PER_MONTH);
            Integer dailyLimit = ServiceConfigCache.getInstance().getInteger(InterstitialConstants.INTERSTITIAL_DISPLAY_PER_DAY);

            // Validate configuration
            if (Objects.isNull(monthlyLimit) || Objects.isNull(dailyLimit) || monthlyLimit <1 || dailyLimit<1) {
                log.error("Display limits not configured. monthlyLimit: {}, dailyLimit: {}", monthlyLimit, dailyLimit);
                return 0;
            }

            // Create keys for aerospike lookup
            List<String> keys = new ArrayList<>();
            String monthKey = InterstitialUtils.generateMonthKey(customerId);
            String dayKey = InterstitialUtils.generateDayKey(customerId);
            keys.add(monthKey);
            keys.add(dayKey);

            // Get current display counts using batch operation
            try {
                displayCounts = interstitialRepository.getInterstitialData(keys);
                if (Objects.isNull(displayCounts)) {
                    log.error("Null response received from interstitialRepository for keys: {}", keys);
                    displayCounts = new HashMap<>(); // Initialize with empty map if null
                }
            } catch (Exception e) {
                log.error("Error in fetching interstitial data from Aerospike for keys: {}", keys, e);
                displayCounts = new HashMap<>(); // Initialize with empty map in case of exception
            }

            // Check against limits
            Integer monthlyCount = displayCounts.getOrDefault(monthKey, new InterstitialDataRecord()).getSeenCount();  // Default to 0 if no record exists
            Integer dailyCount = displayCounts.getOrDefault(dayKey, new InterstitialDataRecord()).getSeenCount();  // Default to 0 if no record exists

            // Return true only if both monthly and daily counts are within limits
            if(monthlyCount < monthlyLimit && dailyCount < dailyLimit)
                eligibleInterstitial = dailyLimit - dailyCount;
        }

        return eligibleInterstitial;
    }

    private String getDisplayMessage(HomeReminderResponse homeReminderResponse, Product product, HashMap<String, Object> frequentOrder) {
        if (homeReminderResponse.getDueDate() == null) {
            return "";
        }
//        log.info("homeReminderResponse : {}", homeReminderResponse);
        String billState = frequentOrder.containsKey("billState") ? frequentOrder.get("billState").toString() : Constants.EMPTY_STRING;
        String[] key = new String[]{Constants.LANGUAGE_ENGLISH, Constants.RECO, "interstitial_display_message", product.getServiceKey(), product.getPayTypeKey(), billState};
        HashMap<String, String> payload = new HashMap<>();
        payload.put(BILLDUEDATE, StringUtils.defaultString(DateUtil.stringDateFormat(homeReminderResponse.getDueDate(),Constants.DATE_FORMAT_FOR_TIMESTAMP, Constants.BILL_DATE_FORMAT_FOR_DUE_BILL)));
        return localisationManager.getMessage(key, true, payload);

    }

    private String getHeader(HomeReminderResponse homeReminderResponse) {
        if (homeReminderResponse.getAmount() == null) {
            return "";
        }
        String[] key = new String[]{Constants.LANGUAGE_ENGLISH, Constants.RECO, "interstitial_display_header"};
        HashMap<String, String> payload = new HashMap<>();
        payload.put(DUEAMOUNT, homeReminderResponse.getAmount().toString());

        return localisationManager.getMessage(key, true, payload);
    }

    private boolean checkBillLevelInterstitialEligibility(String billKey, Map<String, InterstitialDataRecord> impressionMap, HomeReminderResponse reminderResponse) {

        // Get Bill display limits from service config
        Integer interstitialBillLimit = ServiceConfigCache.getInstance().getInteger(InterstitialConstants.INTERSTITIAL_DISPLAY_PER_BILL);
        String cooloffKey = billKey + InterstitialConstants.INTERSTITIAL_BILLKEY_COOL_OFF_SUFFIX;
        if (Objects.isNull(interstitialBillLimit)) {
            log.error("Interstitial bill display limit not configured, defaulting to disabled. Bill limit: {}", interstitialBillLimit);
            return false;
        }

        if (impressionMap.containsKey(billKey) && impressionMap.containsKey(cooloffKey) && impressionMap.get(cooloffKey).getSeenCount() == 0) {
            int billSeen = impressionMap.get(billKey).getSeenCount();
            if (billSeen < interstitialBillLimit-1  || (interstitialBillLimit - billSeen == 1 && checkApproachingDueDate(reminderResponse))) {
                return true;
            }
        }

        return false;
    }

    private boolean checkApproachingDueDate(HomeReminderResponse reminderResponse) {
        if (reminderResponse == null || reminderResponse.getDueDate() == null) {
            return false;
        }

        Date dueDate = DateUtil.stringToDate(
                reminderResponse.getDueDate(),
                Constants.DATE_FORMAT_FOR_TIMESTAMP
        );

        if (dueDate == null) {
            return false;
        }

        LocalDate dueDateLocal = dueDate.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();

        LocalDate today = LocalDate.now();
        LocalDate tomorrow = today.plusDays(1);

        return dueDateLocal.equals(today) || dueDateLocal.equals(tomorrow);
    }


}