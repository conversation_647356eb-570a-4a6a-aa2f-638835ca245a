package com.paytm.recharges.bff.service.impl;

import com.aerospike.client.AerospikeClient;
import com.aerospike.client.Bin;
import com.aerospike.client.Key;
import com.aerospike.client.Record;
import com.aerospike.client.policy.WritePolicy;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.recharges.bff.aop.annotations.MethodLatencyMetricsAction;
import com.paytm.recharges.bff.client.AgentAeroSpikeClientWrapper;
import com.paytm.recharges.bff.client.GenericRestClient;
import com.paytm.recharges.bff.client.RuleServiceClient;
import com.paytm.recharges.bff.config.properties.ShortnerApiProperties;
import com.paytm.recharges.bff.constants.Constants.HomeReminderConstants;
import com.paytm.recharges.bff.constants.Constants.LatestTwoOrdersConstants;
import com.paytm.recharges.bff.constants.*;
import com.paytm.recharges.bff.constants.Constants;
import com.paytm.recharges.bff.constants.DateFormat;
import com.paytm.recharges.bff.constants.ErrorMessage;
import com.paytm.recharges.bff.constants.HomePageConstants;
import com.paytm.recharges.bff.constants.RecentConstants;
import com.paytm.recharges.bff.constants.ServiceConfigConstants;
import com.paytm.recharges.bff.constants.StringLiterals;
import com.paytm.recharges.bff.constants.*;
import com.paytm.recharges.bff.constants.Constants.*;
import com.paytm.recharges.bff.datalayer.cache.CVRProductCache;
import com.paytm.recharges.bff.datalayer.cache.FeatureConfigCache;
import com.paytm.recharges.bff.datalayer.cache.ServiceConfigCache;
import com.paytm.recharges.bff.datalayer.dto.*;
import com.paytm.recharges.bff.datalayer.dto.request.FrequentOrdersRequest;
import com.paytm.recharges.bff.datalayer.dto.request.PersonalisationRequest;
import com.paytm.recharges.bff.datalayer.dto.request.RURecoRequest;
import com.paytm.recharges.bff.datalayer.dto.request.RURecoViewRequest;
import com.paytm.recharges.bff.datalayer.dto.request.UpdateNicknameRequest;
import com.paytm.recharges.bff.datalayer.dto.response.HomeReminderResponse;
import com.paytm.recharges.bff.datalayer.dto.response.MyBillsClpLogObj;
import com.paytm.recharges.bff.datalayer.dto.response.PersonalisationResponse;
import com.paytm.recharges.bff.datalayer.dto.response.RuWidgetResponse;
import com.paytm.recharges.bff.datalayer.dto.response.SagaRecentResponse;
import com.paytm.recharges.bff.datalayer.dto.response.SagaSMSCardResponse;
import com.paytm.recharges.bff.datalayer.dto.response.SagaSavedCardResponse;
import com.paytm.recharges.bff.datalayer.dto.response.SagaFavWrapperResponse;
import com.paytm.recharges.bff.datalayer.dto.response.ShortnerApiResponse;
import com.paytm.recharges.bff.datalayer.dto.response.UpdateNicknameResponse;
import com.paytm.recharges.bff.datalayer.dto.response.personalisation.OverrideResponse;
import com.paytm.recharges.bff.datalayer.dto.response.personalisation.OverrideResponseObject;
import com.paytm.recharges.bff.datalayer.model.FeatureConfig;
import com.paytm.recharges.bff.datalayer.model.HeadingProperties;
import com.paytm.recharges.bff.datalayer.model.OverrideResponseParams;
import com.paytm.recharges.bff.datalayer.model.Product;
import com.paytm.recharges.bff.datalayer.model.RecentWrapper;
import com.paytm.recharges.bff.enums.BillState;
import com.paytm.recharges.bff.enums.EventState;
import com.paytm.recharges.bff.enums.FeatureConfigKey;
import com.paytm.recharges.bff.enums.FeatureConfigName;
import com.paytm.recharges.bff.exceptions.RestClientException;
import com.paytm.recharges.bff.monitor.MetricsAgent;
import com.paytm.recharges.bff.repository.InterstitialRepository;
import com.paytm.recharges.bff.responsehandler.HomeReminderResponseHandlerUtil;
import com.paytm.recharges.bff.rollout.PersonalisationRolloutClassFactory;
import com.paytm.recharges.bff.rollout.RolloutHandler;
import com.paytm.recharges.bff.service.FavouriteManager;
import com.paytm.recharges.bff.service.LocalisationManager;
import com.paytm.recharges.bff.service.RecentLocalisationManager;
import com.paytm.recharges.bff.service.SagaRecentResponseComparatorStrategy;
import com.paytm.recharges.bff.utils.AESUtil;
import com.paytm.recharges.bff.utils.AerospikeUtils;
import com.paytm.recharges.bff.utils.ClpSorterUtils;
import com.paytm.recharges.bff.utils.DateUtil;
import com.paytm.recharges.bff.utils.JsonUtil;
import com.paytm.recharges.bff.utils.RecentGenerator;
import com.paytm.recharges.bff.utils.RecentUtils;
import com.paytm.recharges.bff.utils.StreamUtils;
import com.paytm.recharges.bff.utils.Utils;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.recharges.favourite_orders_client.datalayer.dto.request.UpdateRecentRequest;
import com.paytm.recharges.favourite_orders_client.datalayer.dto.response.UpdateRecentResponse;
import com.paytm.recharges.favourite_orders_client.datalayer.model.FrequentOrder;
import com.paytm.recharges.favourite_orders_client.datalayer.model.ValueNickname;
import com.paytm.recharges.favourite_orders_client.service.FavouriteClient;
import com.paytm.recharges.saga_client.datalayer.dto.model.EventType;
import com.paytm.recharges.saga_client.datalayer.dto.response.SagaFavResponse;
import com.paytm.recharges.saga_client.datalayer.dto.response.SagaFavResponsePOJO;
import com.paytm.recharges.saga_client.datalayer.dto.response.SagaFavResponsePOJOWrapper;
import com.paytm.recharges.saga_client.datalayer.dto.response.SagaFavResponseWrapper;
import com.paytm.recharges.saga_client.datalayer.dto.response.SagaLastTwoRecentsResponse;
import com.paytm.recharges.saga_client.datalayer.dto.response.SagaLastTwoRecentsResponseWrapper;
import com.paytm.recharges.bff.utils.EncryptionUtil;
import com.paytm.recharges.bff.builder.RecoResponseManager;
import jakarta.annotation.PostConstruct;
import org.apache.commons.lang.mutable.MutableBoolean;
import org.apache.commons.lang3.ObjectUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.modelmapper.ModelMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

import static com.paytm.recharges.bff.constants.CTAConstants.ColorConstants.BUTTON_CTA_LABEL_COLOR_CONSTANT;
import static com.paytm.recharges.bff.constants.CTAConstants.ColorConstants.DEFAULT_CTA_LABEL_COLOR;
import static com.paytm.recharges.bff.constants.CTAConstants.LabelConstants.BUTTON_CTA_LABEL_CONSTANT;
import static com.paytm.recharges.bff.constants.Constants.*;
import static com.paytm.recharges.bff.constants.Constants.HomeReminderConstants.THIN_BANNER_DATEFORMAT;
import static com.paytm.recharges.bff.constants.DateFormat.DDMMM;
import static com.paytm.recharges.bff.constants.HomePageConstants.HOME_PAGE_VIEW_IDS;
import static com.paytm.recharges.bff.constants.HomePageConstants.NEW_BOTTOM_NAV_VIEW_IDS;
import static com.paytm.recharges.bff.constants.MetricsConstant.SAVING_DECRYPTED_HR;
import static com.paytm.recharges.bff.constants.MetricsConstant.SAVING_ENCRYPTED_HR;
import static com.paytm.recharges.bff.constants.RecentConstants.COLOR_CONSTANTS.DEFAULT_COLOR;
import static com.paytm.recharges.bff.constants.RecentConstants.COLOR_CONSTANTS.HEADING2_COLOR;
import static com.paytm.recharges.bff.constants.RecentConstants.HeadingLocalisationConstants.HEADING2;
import static com.paytm.recharges.bff.constants.RecentConstants.HeadingLocalisationConstants.RECON_ID;
import static com.paytm.recharges.bff.utils.RecentUtils.getLocalizationPayloadLite;
import static com.paytm.recharges.favourite_orders_client.constants.Constants.CONNECTION_TIMEOUT_MS;
import static com.paytm.recharges.favourite_orders_client.constants.Constants.MAX_CONNECTION;
import static com.paytm.recharges.favourite_orders_client.constants.Constants.READ_TIMEOUT_MS;
import static com.paytm.recharges.favourite_orders_client.constants.Constants.SMART_RECENT;

@Service
public class FavouriteManagerImpl implements FavouriteManager {

    private static final CustomLogger log = CustomLogManager.getLogger(FavouriteManagerImpl.class);

    @Autowired
    private CustomRecoLogger logger;
    private FavouriteClient favouriteClient;

    @Value("${urls.frequentOrders:}")
    private String frequentOrdersUrl;

    @Value("${urls.updateRecent:}")
    private String updateRecentUrl;


    @Value("${favourite.frequentOrder.config.connection_timeout}")
    private String frequentOrdersConnectionTimeout;

    @Value("${favourite.frequentOrder.config.read_timeout}")
    private String frequentOrdersReadTimeout;

    @Value("${favourite.frequentOrder.config.max_connections}")
    private String maxConnections;

    @Value("${favourite.frequentOrder.config.thread_timeout}")
    private Integer frequentOrdersThreadTimeout;
    @Value("${saga.config.http.thread_timeout}")
    private Integer sagaThreadTimeout;

    @Autowired
    private SagaManagerImpl sagaManager;
    @Autowired
    private SagaClientManagerImpl sagaClientManager;

    @Autowired
    private ExecutorService frequentOrderExecutorService;
    @Autowired
    private ExecutorService homepageExecutor;

    @Autowired
    private ExecutorService updateNickNameExecutor;

    @Autowired
    @Qualifier("aerospikeExecutor")
    private ExecutorService aeroSpikeExecutor;

    @Autowired
    private MetricsAgent metricsAgent;

    @Autowired
    private LocalisationManager localisationManager;
    @Autowired
    private RecentLocalisationManager recentLocalisationManager;
    @Autowired
    private HeadingManagerImpl headingManager;
    @Autowired
    protected RecentLocalisationKeyResolver recentLocalisationKeyResolver;
    @Autowired
    private InterstitialRepository interstitialRepository;
    @Autowired
    private InterstitialManagerImpl interstitialManager;
    @Autowired
    ModelMapper modelMapper;

    @Autowired
    AerospikeClient aerospikeClient;

    @Autowired
    RecentGenerator recentGenerator;

    @Autowired
    private RechargeHelperService rechargeHelperService;

    @Autowired
    private ShortnerApiProperties shortnerApiProperties;

    @Autowired
    private HomeReminderResponseHandlerUtil homeReminderResponseHandlerUtil;

    @Autowired
    private AgentAeroSpikeClientWrapper agentAeroSpikeClientWrapper;

    @Autowired
    private RecentWidgetServiceImpl favouriteService;


    @Autowired
    private ExecutorService sagaExecutor;

    @Autowired
    private ExecutorService ruleProcessorExecutor;

    @Autowired
    private ExecutorService sagaRecentExecutor;

    @Autowired
    private RedYellowLogger redYellowLogger;

    @Autowired
    private MyBillsClpLogger myBillsClpLogger;

    @Autowired
    private PersonalisationRolloutClassFactory personalisationRolloutClassFactory;



    @Value("#{'${favourite.frequentOrder.includeOperator_key_services}'.split(',')}")
    private List<String> includeOperatorInKeyServices;



    @Autowired
    @Qualifier("genericRestClient")
    private GenericRestClient genericRestClient;

    @Autowired
    @Qualifier("ShortnerApiTemplate")
    private RestTemplate restTemplate;

    @Value("${shortnerapi.dns}")
    private String dns;

    @Value("${shortnerapi.endpoint.shorten}")
    private String ShortnerEndPoint;

    @Autowired
    private RecoWidgetServiceImpl recoWidgetService;

    @Autowired
    private RuleServiceClient ruleServiceClient;

    @Autowired
    @Qualifier("homePageSagaRecentComparator")
    private SagaRecentResponseComparatorStrategy homePageSagaRecentComparator;

    @Autowired
    @Qualifier("defaultSagaRecentResponseComparator")
    private SagaRecentResponseComparatorStrategy defaultSagaRecentResponseComparator;

    @Autowired
    public StaticUserPlanOperatorUpdateServiceImpl staticUserPlanOperatorUpdateService;

    @Autowired
    private HomePagePersonalisationManager personalisationManager;

    @Autowired
    private AESUtil aesUtil;

    @Autowired
    private RecoResponseManager recoResponseManager;

    @PostConstruct
    public void init() {
        HashMap<String, String> requestOptions = new HashMap<>();
        requestOptions.put(CONNECTION_TIMEOUT_MS, frequentOrdersConnectionTimeout);
        requestOptions.put(READ_TIMEOUT_MS, frequentOrdersReadTimeout);
        requestOptions.put(MAX_CONNECTION, maxConnections);
        requestOptions.put(Constants.FREQUENT_ORDERS_URL, frequentOrdersUrl);
        requestOptions.put(Constants.UPDATE_RECENT_URL, updateRecentUrl);
        favouriteClient = new FavouriteClient(requestOptions);

    }

    private String createAuthorizationKey(String smartLinkUserName, String smartLinkPassword) {
        String auth = smartLinkUserName + ":" + smartLinkPassword;
        byte[] encodedAuth = Base64.getEncoder().encode(auth.getBytes(Charset.forName(US_ASCII)));
        String encodedAuthStr = new String(encodedAuth);
        String authHeader = new StringBuilder(BASIC).append(encodedAuthStr).toString();

        return authHeader;
    }

    @MethodLatencyMetricsAction(metricsName = "ShortnerApi")
    public ShortnerApiResponse shortnerApi(Map<String, Object> requestBody) {
        log.info("shortnerApi :: request Body {} ", requestBody);

        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        String smartLinkUserName = shortnerApiProperties.getUsername();
        String smartLinkPassword = shortnerApiProperties.getPassword();
        String authKey = createAuthorizationKey(smartLinkUserName, smartLinkPassword);
        headers.put(AUTHORISATION, Collections.singletonList(authKey));

        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put(ENCODED_LONG_URL, requestBody.get(DEEPLINK));
        queryParams.put(TTL, VALUE);
        ShortnerApiResponse shortnerApiResponse = new ShortnerApiResponse();
        try {
            shortnerApiResponse = genericRestClient.get(dns, ShortnerEndPoint, queryParams, headers, new ParameterizedTypeReference<ShortnerApiResponse>() {
            }, restTemplate);
            shortnerApiResponse.setCode(200);
            shortnerApiResponse.setStatus("SUCCESS");
            shortnerApiResponse.setMessage("Generated Successfully");
        } catch (RestClientException e) {
            log.error("[non_2xx_exception] method: GET, url: {}, response_status: {}, response_message: {}, exceptionMsg: {}", dns, "/", ShortnerEndPoint, e.getHttpStatus().value(), e.getMessage());
            shortnerApiResponse.setStatus(HttpStatus.valueOf(e.getHttpStatus().value()).getReasonPhrase());
            shortnerApiResponse.setCode(e.getHttpStatus().value());
            shortnerApiResponse.setMessage(e.getMessage());
        } catch (Exception e) {
            shortnerApiResponse.setCode(500);
            shortnerApiResponse.setStatus(String.valueOf(e.getCause()));
            shortnerApiResponse.setMessage(e.getMessage());
            log.error("[unknown_exception] method: GET, url: {}", dns, "/", ShortnerEndPoint, e);
            log.error("deeplink: {}", requestBody.get("deeplink"));
        }

        return shortnerApiResponse;
    }

    private ArrayList<FrequentOrder> combineRecents(Map<String, String> allRequestParams, Future<SagaFavResponsePOJOWrapper> sagaFavResponseWrapperCompletableFuture, Future<ArrayList<FrequentOrder>> frequentOrdersFuture) {
        ArrayList<FrequentOrder> favouriteFrequentOrders = null;
        List<SagaFavResponsePOJO> sagaFavResponses = null;

        try {
            if (Objects.nonNull(sagaFavResponseWrapperCompletableFuture)) {
                SagaFavResponsePOJOWrapper sagaFavResponsePOJOWrapper=sagaFavResponseWrapperCompletableFuture.get(sagaThreadTimeout, TimeUnit.MILLISECONDS);
                if(Objects.nonNull(sagaFavResponsePOJOWrapper)){
                    sagaFavResponses = sagaFavResponsePOJOWrapper.getData();
                }

            }
        } catch (InterruptedException e) {
            log.error("FavouriteManagerImpl.combineRecents  :: error while fetching sagaRecents ", e);
            //Thread.currentThread().interrupt();

        } catch (ExecutionException | RuntimeException | TimeoutException e) {
            log.error("FavouriteManagerImpl.combineRecents  :: error while fetching sagaRecents ", e);
        }
        if (Objects.nonNull(frequentOrdersFuture)) {
            try {
                favouriteFrequentOrders = frequentOrdersFuture.get(frequentOrdersThreadTimeout, TimeUnit.MILLISECONDS);

            } catch (InterruptedException e) {
                log.error("FavouriteManagerImpl.combineRecents  :: error while fetching favouriteFrequentOrders ", e);
                //Thread.currentThread().interrupt();

            } catch (ExecutionException | TimeoutException | RuntimeException e) {
                log.error("FavouriteManagerImpl.combineRecents  :: error while fetching favouriteFrequentOrders ", e);

            }
        }
        return getCombinedResponse(favouriteFrequentOrders, sagaFavResponses, allRequestParams);
    }

    private ArrayList<FrequentOrder> getCombinedResponse(ArrayList<FrequentOrder> favouriteFrequentOrders, List<SagaFavResponsePOJO> sagaFavResponses, Map<String, String> allRequestParams) {
        ArrayList<FrequentOrder> frequentOrderArrayList = new ArrayList<>();
        addAllIfNotNull(frequentOrderArrayList, favouriteFrequentOrders);
        if(!frequentOrderArrayList.isEmpty()){
            List<String> segMetrics = new ArrayList<>();
            segMetrics.add(METRIC_MARKET_FAV_RESPONSE_SIZE + frequentOrderArrayList.size());
            metricsAgent.incrementEventCount(MARKET_FAV_FREQUENT_ORDER_SIZE, segMetrics);
        }
        if (Objects.nonNull(sagaFavResponses) && !sagaFavResponses.isEmpty()) {
            try {
                ArrayList<FrequentOrder> frequentOrders = favouriteService.prepareFavResponse(sagaFavResponses, allRequestParams);
                addAllIfNotNull(frequentOrderArrayList, frequentOrders);
            } catch (JsonProcessingException e) {
                log.error("getCombinedResponse() :: Json Execption {}", e);
            }
        }
        ClpSorterUtils.sort(frequentOrderArrayList);
        for(FrequentOrder frequentOrder: frequentOrderArrayList){
            setProductIdNullIfAmbiguous(frequentOrder);
        }
        log.debug("FavouriteManagerImpl :: getCombinedResponse :: count of merged orders {}",frequentOrderArrayList.size());
        metricsAgent.recordHistogramValue("Count","Total Recents",Long.valueOf(frequentOrderArrayList.size()));
        return frequentOrderArrayList;
    }

    private void setProductIdNullIfAmbiguous(FrequentOrder frequentOrder){
        JSONObject orderData = frequentOrder.getOrderData();
        if(orderData != null) {
            Map<String, Object> recent = orderData.toMap();
            Map<String, Object> productAttributes = null;
            Map<String, Object> productDetails = null;
            if (recent.containsKey(PRODUCT)) {
                productDetails = (Map<String, Object>) recent.get(PRODUCT);
                if (productDetails.containsKey((RECENT_ATTRIBUTES))) {
                    productAttributes = (Map<String, Object>) productDetails.get(RECENT_ATTRIBUTES);

                    if (productAttributes.containsKey(IS_AMBIGUOUS_PID)) {
                        Object value = productAttributes.get(IS_AMBIGUOUS_PID);

                        if (value instanceof Boolean && (Boolean) value) {
                            recent.put(RECENT_PRODUCT_ID, null);
                        }
                    }
                }
            }

            orderData = new JSONObject(recent);
            frequentOrder.setOrderData(orderData);
        }
    }

    @Override
    public ArrayList<HashMap<String, Object>> getFrequentOrdersByCustomerId(Long customerId, HashMap<String, String> allRequestParams) {
        try {
            Future<SagaFavResponsePOJOWrapper> sagaFavResponseWrapperCompletableFuture = prepareCallForRecentsFromSaga(customerId, null,allRequestParams);
            Future<ArrayList<FrequentOrder>> frequentOrdersFuture = prepareDataForRecentGet(customerId, allRequestParams);
            return createResponse(filterParBasedCards(combineRecents(allRequestParams, sagaFavResponseWrapperCompletableFuture, frequentOrdersFuture)));
        } catch (RuntimeException | JsonProcessingException e) {
            log.error("FavouriteManagerImpl.getFrequentOrdersByCustomerId :: init :: error while creating response for customer_id :: getFrequentOrdersByCustomerId :: " + customerId, e);
            metricsAgent.incrementEventCount(FREQUENT_ORDER_ERROR);
        }
        return new ArrayList<>();
    }

    @Override
    public ArrayList<HashMap<String, Object>> getFrequentOrdersByCustomerIdV2(Long customerId, HashMap<String, String> allRequestParams) {
        CompletableFuture<SagaSMSCardResponse> smsCards = null;
        boolean isApiV5Version=Objects.nonNull(allRequestParams)&&allRequestParams.containsKey(API_VERSION_FOR_FREQUENT_ORDERS) && allRequestParams.get(API_VERSION_FOR_FREQUENT_ORDERS) !=null && allRequestParams.get(API_VERSION_FOR_FREQUENT_ORDERS).equals(VERSION_5_FOR_FREQUENT_ORDERS);
        Future<SagaFavResponsePOJOWrapper> sagaFavResponseWrapperCompletableFuture = prepareCallForRecentsFromSaga(customerId, null,allRequestParams);
        Future<ArrayList<FrequentOrder>> frequentOrdersFuture = prepareDataForRecentGet(customerId, allRequestParams);
        ArrayList<FrequentOrder> frequentOrderArrayList = new ArrayList<>();
        try{
             smsCards = callForSMSCards(customerId, allRequestParams, true,null);
            log.info("After callForSMSCards async{}", customerId);
            frequentOrderArrayList = combineRecents(allRequestParams, sagaFavResponseWrapperCompletableFuture, frequentOrdersFuture);

            //saved cards response in Get API
            CompletableFuture<SagaSavedCardResponse> savedCards = null;
            Map<String,Object> queryParams=new HashMap<>();
            boolean isCardSkinRequired=false;
            if(isApiV5Version){
                isCardSkinRequired= allRequestParams.get(IS_CARD_SKIN_REQUIRED) != null &&  allRequestParams.get(IS_CARD_SKIN_REQUIRED).equals("true");
                if(!isCardSkinRequired){
                    Boolean isCardSkinRequiredFromServiceConfig = ServiceConfigCache.getInstance().getBoolean(IS_CARD_SKIN_REQUIRED);
                    if(Boolean.TRUE.equals(isCardSkinRequiredFromServiceConfig)){
                        isCardSkinRequired=true;
                    }
                    
                }
            }

            try {
                if(rechargeHelperService.isCoftEnable()){
                    queryParams.put(Constants.IS_COFT,true);
                }
                if (allRequestParams.containsKey(BILL_TYPE)) {
                    queryParams.put(BILL_TYPE, allRequestParams.get(BILL_TYPE));
                }
                savedCards = sagaManager.getSavedCardsFromSagaV2(customerId,queryParams, isCardSkinRequired);
            } catch (RuntimeException e) {
                log.error("FavouriteManagerImpl.getFrequentOrdersByFilter :: for customer_id :: {} error while getting saved card  response  :: {}",customerId, e.getMessage());
                metricsAgent.incrementEventCount("saved_card_error_from_GET_API");
            }
            //merging saved cards to frequent orders
            if (savedCards != null) {
                try {
                    frequentOrderArrayList = recentGenerator.mergeSavedCardsWithCoft(frequentOrderArrayList,savedCards.get(frequentOrdersThreadTimeout, TimeUnit.MILLISECONDS),allRequestParams, rechargeHelperService.isCoftEnable(),isCardSkinRequired );
                } catch (InterruptedException | ExecutionException | TimeoutException |RuntimeException e) {
                    frequentOrderArrayList=recentGenerator.maskingRecents(frequentOrderArrayList);
                    log.error("FavouriteManagerImpl.getFrequentOrdersByFilter :: for customer_id :: {} error getting saved card  :: {}", customerId, e.getMessage());
                    metricsAgent.incrementEventCount("merge_error_with_saved_cards_GET_API");
                }
            }
            
            if (smsEnabledInRecent()) {
                frequentOrderArrayList = recentGenerator.dedupeSMSSavedCards(frequentOrderArrayList);
            }
            if (Objects.nonNull(smsCards) && !smsEnabledInRecent()) {
                try {
                    frequentOrderArrayList = recentGenerator.mergeSMSCards(frequentOrderArrayList, smsCards.get(frequentOrdersThreadTimeout, TimeUnit.MILLISECONDS), allRequestParams);
                } catch (InterruptedException ex) {
                    log.error("FavouriteManagerImpl.getFrequentOrdersByCustomerIdV2 :: interrupted error while merging sms card :: {}", ex.getMessage());
                    metricsAgent.incrementEventCount(SMS_CARD_MERGE_ERROR_EVENT);
                    //Thread.currentThread().interrupt();

                } catch (ExecutionException | TimeoutException |RuntimeException ex) {
                    log.error("FavouriteManagerImpl.getFrequentOrdersByCustomerIdV2 :: execution or timeout error while merging sms card :: {}", ex.getMessage());
                    metricsAgent.incrementEventCount(SMS_CARD_MERGE_ERROR_EVENT);
                }
            }
        }catch(Exception e){
            log.error("FavouriteManagerImpl.getFrequentOrdersByCustomerIdV2 :: error while getting in V3 :: ", e);
            metricsAgent.incrementEventCount(ERROR_IN_GET_V3);
        }
        try {
            return createResponse(frequentOrderArrayList,isApiV5Version);
        }catch (JsonProcessingException ex){
            log.error("FavouriteManagerImpl.getFrequentOrdersByCustomerIdV2 :: error while creating response :: {}",  ex.getMessage());
        }
        return new ArrayList<>();
    }


    private HashMap<String, Object> createQueryParamsForSagaRequest(boolean isCoft,Map<String, String> allRequestParams) {
        HashMap<String, Object> queryParams = new HashMap<>();
        queryParams.put(ONLY_REMINDER, true);
       /* String [] dropofflockey = new String[] {Constants.LANGUAGE_ENGLISH,HOME_REMINDER,EXCLUDE_DROPOFF,CONFIGURATION_LOCAL};
        HashMap<String,String> dropoffpayload=new HashMap<>();
       String dropoffValue=recentLocalisationManager.getMessage(dropofflockey, true, dropoffpayload);
        if(StringUtils.isEmpty(dropoffValue))
            dropoffValue = "false";

        */
        if(Objects.nonNull(allRequestParams)){
            queryParams.put(VERSION, allRequestParams.get(VERSION));
            queryParams.put(CLIENT, Constants.RECO_SAGA_CLIENT_NAME_MAP.get(allRequestParams.get(CLIENT)));
        }
        queryParams.put(EXCLUDE_DROPOFF, true);
        if (isCoft) {
            queryParams.put(Constants.IS_COFT, true);
        }
        return queryParams;
    }


    private List<String> getServicesFilterForSaga() {
        String serviceAvaiable = rechargeHelperService.servicesForSaga("reco_widget_services");
        return org.apache.commons.lang3.StringUtils.isEmpty(serviceAvaiable) ? new ArrayList<>() : Arrays.asList(serviceAvaiable.split(","));
    }

    public List<HashMap<String, Object>>   getFrequentOrdersForHomeReminder(Long customerId, Map<String, String> allRequestParams) throws IOException, ParseException {
        HashMap<String, String> filters = new HashMap<>();
        CompletableFuture<SagaSavedCardResponse> savedCards = null;
        //fetching data from saga for all services. We will filter data for allowed services in BFF
        filters.put(Constants.SERVICE, "");
        boolean maskingFlag = rechargeHelperService.featureForRollout(Constants.MASKED_CARD) || rechargeHelperService.isCoftEnable();
        HashMap<String, Object> queryParams = this.createQueryParamsForSagaRequest(maskingFlag,allRequestParams);


        ArrayList<SagaFavResponse> sagaFavResponses = new ArrayList<>();
        ArrayList<SagaRecentResponse> sagaRecentResponses = new ArrayList<>();

        ArrayList<FrequentOrder> frequentOrders = new ArrayList<>();
        boolean isCacheValue = false;
        Key key = new Key(SMARTREMINDER, null, HR_KEY.concat(String.valueOf(customerId)));
        if (Objects.isNull(filters.get(Constants.SERVICE))) {
            return new ArrayList<>();
        }
        try {
            Record record = aerospikeClient.get(null, key);
            if (Objects.nonNull(record)) {
                //log.info("Record already exisists record is -- "+record.bins);
                Map<String, Object> map = record.bins;
                if (Objects.nonNull(map)) {
                    if (AerospikeUtils.getDeserializedData(map.get("HR")).equals("[]")) return new ArrayList<>();
                    else sagaRecentResponses = (ArrayList<SagaRecentResponse>) AerospikeUtils.getDeserializedData(map.get("HR"));
                    isCacheValue = true;
                    metricsAgent.incrementEventCount("hr_found_in_cache");
                } else {
                    metricsAgent.incrementEventCount("hr_not_found_in_cache");
                }
            } else {
                metricsAgent.incrementEventCount("hr_not_found_in_cache");
            }
        } catch (Exception ex) {
            log.error("Aerospike is down/not responding while get {}", ex);
            metricsAgent.incrementEventCount("hr_error_in_get_cache");
        }

        boolean isCCTimedOut = false;
        if (!isCacheValue) {
            SagaFavResponseWrapper sagaFavResponseWrapper = sagaClientManager.getFrequentOrdersFromSaga(customerId, filters, queryParams);
            log.info("sagaFavResponseWrapper {}",sagaFavResponseWrapper);
            if(Objects.nonNull(sagaFavResponseWrapper)) {
                isCCTimedOut = sagaFavResponseWrapper.isCCTimedOut();
            }
            if (Objects.nonNull(sagaFavResponseWrapper)) {
                sagaFavResponses = sagaFavResponseWrapper.getData();
                if (sagaFavResponseWrapper.getAgent() == true) {
                    agentAeroSpikeClientWrapper.putAgentCustomerId(customerId);
                }
            }
            if (Objects.nonNull(sagaFavResponses) && sagaFavResponses.isEmpty()) {
                Bin bin = AerospikeUtils.getSerialiazedBin("HR","[]");
                try {
                    //log.info("Expiration time ---" + getExpirationTime());
                    WritePolicy policy = new WritePolicy(aerospikeClient.writePolicyDefault);
                    policy.expiration = getExpirationTime();
                    //log.info("Expiration time ---" + aerospikeClient.getWritePolicyDefault().expiration);
                    aerospikeClient.put(policy, key, bin);
                } catch (Exception ex) {
                    log.error("Aerospike is down/not responding while save {}", ex);
                    metricsAgent.incrementEventCount("hr_error_in_save_cache");
                }
                return new ArrayList<>();
            }
            if (Objects.isNull(sagaFavResponses)) return new ArrayList<>();

          //  log.info("saga rece respons full --" + sagaFavResponses);


            convertSagaResposneJsonToPojoAndSort(sagaFavResponses, sagaRecentResponses);

        }


        cacheRecentSagaResponse(sagaRecentResponses, customerId, isCacheValue, key, isCCTimedOut);

        List<Callable<FrequentOrder>> callableTasks = new ArrayList<>();
        List<String> serviceAvailable = getServicesFilterForSaga();
        List<SagaFavResponsePOJO> sagaRecentResponse = filterRecentAndSort(callableTasks, sagaRecentResponses, serviceAvailable, customerId, allRequestParams);
       // log.info("sagaRecentResponse is {}", JsonUtil.toJson(sagaRecentResponse));

        frequentOrders = recoWidgetService.prepareFavResponse(sagaRecentResponse, allRequestParams);

        try {
            return createResponse(frequentOrders);
        } catch (JsonProcessingException e) {
            log.error("FavouriteManagerImpl.getFrequentOrdersByFilter :: for customer_id :: error while creating response  :: " + customerId, e);
            metricsAgent.incrementEventCount("home_remainder_frequent_order_error");
        }
        return new ArrayList<>();
    }

    public List<HashMap<String, Object>>  getFrequentOrdersForHomeReminderV1(Long customerId, Map<String, String> allRequestParams, List<Future<Map<String,Integer>>> futureCatMaps) throws IOException, ParseException {
        HashMap<String, String> filters = new HashMap<>();
        CompletableFuture<SagaSavedCardResponse> savedCards = null;
        //fetching data from saga for all services. We will filter data for allowed services in BFF
        filters.put(Constants.SERVICE, "");
        boolean maskingFlag = rechargeHelperService.featureForRollout(Constants.MASKED_CARD) || rechargeHelperService.isCoftEnable();
        HashMap<String, Object> queryParams = this.createQueryParamsForSagaRequest(maskingFlag,allRequestParams);


        ArrayList<SagaFavResponse> sagaFavResponses = new ArrayList<>();
        ArrayList<SagaRecentResponse> sagaRecentResponses = new ArrayList<>();

        ArrayList<FrequentOrder> frequentOrders = new ArrayList<>();
        boolean isCacheValue = false;
        Key key = new Key(SMARTREMINDER, null, HR_KEY.concat(String.valueOf(customerId)));
        if (Objects.isNull(filters.get(Constants.SERVICE))) {
            return new ArrayList<>();
        }

         try { 	 
             Record record = aerospikeClient.get(null, key);
             if (Objects.nonNull(record)) {
 //                log.info("Record already exisists record is -- "+record.bins);
                 Map<String, Object> map = record.bins;
                 if (Objects.nonNull(map) && Objects.nonNull(map.get("HR"))) {
                     if (AerospikeUtils.getDeserializedData(map.get("HR")).equals("[]")) return new ArrayList<>();
                     else sagaRecentResponses = (ArrayList<SagaRecentResponse>) AerospikeUtils.getDeserializedData(map.get("HR"));
                     isCacheValue = true;
                     metricsAgent.incrementEventCount("hr_found_in_cache");
                 } else {
                     metricsAgent.incrementEventCount("hr_not_found_in_cache");
                 }
             } else {
                 metricsAgent.incrementEventCount("hr_not_found_in_cache");
             }
         } catch (Exception ex) {
             log.error("Aerospike is down/not responding while get for cust id: {} exception : {}",customerId, ex);
             metricsAgent.incrementEventCount("hr_error_in_get_cache");
         }

         boolean isCCTimedOut = false;
        if (!isCacheValue) {
            SagaFavResponseWrapper sagaFavResponseWrapper = sagaClientManager.getFrequentOrdersFromSaga(customerId, filters, queryParams);
            if(Objects.nonNull(sagaFavResponseWrapper)) {
                isCCTimedOut = sagaFavResponseWrapper.isCCTimedOut();
            }
            if (Objects.nonNull(sagaFavResponseWrapper)) {
                sagaFavResponses = sagaFavResponseWrapper.getData();
                if (sagaFavResponseWrapper.getAgent() == true) {
                    agentAeroSpikeClientWrapper.putAgentCustomerId(customerId);
                }
            }
            if (Objects.nonNull(sagaFavResponses) && sagaFavResponses.isEmpty()) {
                Bin bin = AerospikeUtils.getSerialiazedBin("HR","[]");
                try {
                    //log.info("Expiration time ---" + getExpirationTime());
                    WritePolicy policy = new WritePolicy(aerospikeClient.writePolicyDefault);
                    policy.expiration = getExpirationTime();
                    //log.info("Expiration time ---" + aerospikeClient.getWritePolicyDefault().expiration);
                    aerospikeClient.put(policy, key, bin);
                } catch (Exception ex) {
                    log.error("Aerospike is down/not responding while saving for cust id: {} exception : {}",customerId, ex);
                    metricsAgent.incrementEventCount("hr_error_in_save_cache");
                }
                return new ArrayList<>();
            }
            if (Objects.isNull(sagaFavResponses)) return new ArrayList<>();

            //  log.info("saga rece respons full --" + sagaFavResponses);


            convertSagaResposneJsonToPojoAndSort(sagaFavResponses, sagaRecentResponses);

        }


        cacheRecentSagaResponse(sagaRecentResponses, customerId, isCacheValue, key, isCCTimedOut);

        List<Callable<FrequentOrder>> callableTasks = new ArrayList<>();
        List<String> serviceAvailable = getServicesFilterForSaga();
        List<SagaFavResponsePOJO> sagaRecentResponse = filterRecentAndSort(callableTasks, sagaRecentResponses, serviceAvailable, customerId, allRequestParams);


       // log.info("sagaRecentResponse is {}", JsonUtil.toJson(sagaRecentResponse));

        frequentOrders = recoWidgetService.prepareFavResponse(sagaRecentResponse, allRequestParams);

        try {
            return createResponse(frequentOrders);
        } catch (JsonProcessingException e) {
            log.error("FavouriteManagerImpl.getFrequentOrdersByFilter :: for customer_id :: error while creating response  :: " + customerId, e);
            metricsAgent.incrementEventCount("home_remainder_frequent_order_error");
        }
        return new ArrayList<>();
    }

    public SagaFavWrapperResponse getFrequentOrdersForHomeReminderV2(Long customerId, Map<String, String> allRequestParams, Map<Long, String> overrideViewIdToWidgetTypeMap, List<Future<OverrideResponse>> futureServicesList, List<Future<Map<String,Integer>>> futureCatMaps) throws Exception {
        log.info("[FavouiteManagerImpl.getFrequentOrdersForHomeReminderV2] :: function starting");
        HashMap<String, String> filters = new HashMap<>();
        CompletableFuture<SagaSavedCardResponse> savedCards = null;
        //fetching data from saga for all services. We will filter data for allowed services in BFF
        filters.put(Constants.SERVICE, "");
        boolean maskingFlag = rechargeHelperService.featureForRollout(Constants.MASKED_CARD) || rechargeHelperService.isCoftEnable();
        HashMap<String, Object> queryParams = this.createQueryParamsForSagaRequest(maskingFlag,allRequestParams);


        ArrayList<SagaFavResponse> sagaFavResponses = new ArrayList<>();
        ArrayList<SagaRecentResponse> sagaRecentResponses = new ArrayList<>();
        Map<String, InterstitialDataRecord> interstitialData = new HashMap<>();

        ArrayList<FrequentOrder> frequentOrders = new ArrayList<>();
        boolean isCacheValue = false;
        Key key = new Key(SMARTREMINDER, null, HR_KEY.concat(String.valueOf(customerId)));
        if (Objects.isNull(filters.get(Constants.SERVICE))) {
            return new SagaFavWrapperResponse(new ArrayList<>(),new HashMap<>(), 0);
        }

        boolean isCCTimedOut = false;
        int eligibleInterstitialCount = 0;
        long interstitial_start_time = System.currentTimeMillis();
        CompletableFuture<Integer> interstitialFuture = CompletableFuture.supplyAsync(() -> interstitialManager.checkAndGetEligibleInterstitial(customerId), aeroSpikeExecutor);

        if(!agentAeroSpikeClientWrapper.isAgent(customerId)) {
            try {
                long start_time = System.currentTimeMillis();
                Record record = aerospikeGet(key, customerId);
                long end_time = System.currentTimeMillis();
                double difference = (end_time - start_time);
                log.info("Aerospike fetch time taken ----->{}", difference);
                if (Objects.nonNull(record)) {
                    Map<String, Object> map = record.bins;
                    if (Objects.nonNull(map) && Objects.nonNull(map.get(Constants.HR_BIN))) {
                        Object deSerializedData = AerospikeUtils.getDeserializedData(map.get(Constants.HR_BIN));
                        if (deSerializedData.equals("[]")) {
                            sagaRecentResponses = new ArrayList<>();
                        } else {
                            // If encrypted, decrypt the data after deserializing
                            if (Objects.nonNull(map.get(Constants.HR_ENCRYPTED_BIN)) && Boolean.TRUE.equals((Boolean) map.get(Constants.HR_ENCRYPTED_BIN))) {

                                sagaRecentResponses = JsonUtil.fromJson(aesUtil.decrypt(deSerializedData.toString()), new TypeReference<>() {
                                });
                                log.info("Encrypted data found cust_id ={},  HR_ENCRYPTED_BIN = {}, data = {}",customerId,map.get(HR_ENCRYPTED_BIN),sagaRecentResponses);
                                log.info("##### 1 testing: aerospike: map : {}", map);
                                metricsAgent.incrementEventCount(MetricsConstant.ENCRYPTED_HR_FOUND);
                            } else {
                                log.info("Decrypted data found for cust_id = {}, data={} ",customerId,deSerializedData);
                                sagaRecentResponses = (ArrayList<SagaRecentResponse>) deSerializedData;
                                metricsAgent.incrementEventCount(MetricsConstant.DECRYPTED_HR_FOUND);
                            }
                        }
                        isCacheValue = true;
                        metricsAgent.incrementEventCount("hr_found_in_cache");

                    } else {
                        metricsAgent.incrementEventCount("hr_not_found_in_cache");
                    }
                } else {
                    metricsAgent.incrementEventCount("hr_not_found_in_cache");
                }
            } catch (Exception ex) {
                log.error("Aerospike is down/not responding while get for cust id: {} exception : {}", customerId, ex);
                metricsAgent.incrementEventCount("hr_error_in_get_cache");
            }


            try {
                eligibleInterstitialCount = interstitialFuture.get(ServiceConfigCache.getInstance().getInteger(InterstitialConstants.INTERSTITIAL_THREAD_TIMEOUT), TimeUnit.MILLISECONDS); // in milliseconds timeout
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("Thread interrupted while waiting for interstitial count: {}", e.getMessage());
                eligibleInterstitialCount = 0; // default fallback value
            } catch (ExecutionException e) {
                log.error("Error executing interstitial count future: {}", e.getMessage());
                metricsAgent.incrementEventCount("customer_intertstitial_error");
                eligibleInterstitialCount = 0; // default fallback value
            } catch (TimeoutException e) {
                log.error("Timeout while getting interstitial count after set milliseconds and error: {}", e);
                interstitialFuture.cancel(true); // Cancel the future if it times out
                metricsAgent.incrementEventCount("customer_intertstitial_timed_out");
                eligibleInterstitialCount = 0; // default fallback value
            }

            long interstitial_end_time = System.currentTimeMillis();
            double interstitial_time_difference = (interstitial_end_time - interstitial_start_time);
            log.info("Interstitial Aerospike fetch time taken ----->{}", interstitial_time_difference);

            if (!isCacheValue) {
                SagaFavResponseWrapper sagaFavResponseWrapper = sagaClientManager.getFrequentOrdersFromSaga(customerId, filters, queryParams);
                if(Objects.nonNull(sagaFavResponseWrapper)) {
                    isCCTimedOut = sagaFavResponseWrapper.isCCTimedOut();
                }
                if (Objects.nonNull(sagaFavResponseWrapper)) {
                    sagaFavResponses = sagaFavResponseWrapper.getData();
                    log.info("sagaFavResponses :: {}", sagaFavResponses);
                    if (sagaFavResponseWrapper.getAgent() == true) {
                        agentAeroSpikeClientWrapper.putAgentCustomerId(customerId);
                    }
                }
                if (Objects.nonNull(sagaFavResponses) && sagaFavResponses.isEmpty()) {
                    Bin bin = AerospikeUtils.getSerialiazedBin(Constants.HR_BIN,"[]");
                    try {
                        //log.info("Expiration time ---" + getExpirationTime());
                        log.info("##### 2: nothing from saga : {}", sagaFavResponses);
                        WritePolicy policy = new WritePolicy(aerospikeClient.writePolicyDefault);
                        policy.expiration = getExpirationTime();
                        //log.info("Expiration time ---" + aerospikeClient.getWritePolicyDefault().expiration);
                        aerospikeClient.put(policy, key, bin);
                    } catch (Exception ex) {
                        log.error("Error while putting into aerospike ", ex);
                        metricsAgent.incrementEventCount("hr_error_in_save_cache");
                    }
                }
//            if (Objects.isNull(sagaFavResponses)) return new ArrayList<>();

                //  log.info("saga rece respons full --" + sagaFavResponses);

                if (!CollectionUtils.isEmpty(sagaFavResponses))
                    convertSagaResposneJsonToPojoAndSort(sagaFavResponses, sagaRecentResponses);

            }
        }
        allRequestParams.put(RECO_REQUEST_IDENTIFIER,TRUE);
            List<SagaFavResponsePOJO> sagaRecentResponse = new ArrayList<>();
            if (!CollectionUtils.isEmpty(sagaRecentResponses)) {
                cacheRecentSagaResponse(sagaRecentResponses, customerId, isCacheValue, key, isCCTimedOut);
                List<Callable<FrequentOrder>> callableTasks = new ArrayList<>();
                List<String> serviceAvailable = getServicesFilterForSaga();
                sagaRecentResponse = filterRecentAndSort(callableTasks, sagaRecentResponses, serviceAvailable, customerId, allRequestParams);
            }
        List<SagaFavResponsePOJO> finalSagaRecentResponses = sagaRecentResponse;
        List<SagaRecentResponse> sagaRecentResponsesForPersonalisation = new ArrayList<>(sagaRecentResponses);

        if (overrideViewIdToWidgetTypeMap != null || !CollectionUtils.isEmpty(overrideViewIdToWidgetTypeMap)) {

            Future<OverrideResponse> futureServices = ruleProcessorExecutor.submit(EncryptionUtil.wrapCallableWithLTC(
                    () -> getPersonalisedIconForHomeReminder(customerId, overrideViewIdToWidgetTypeMap, finalSagaRecentResponses, sagaRecentResponsesForPersonalisation, agentAeroSpikeClientWrapper.isAgent(customerId), allRequestParams)));
            futureServicesList.add(futureServices);
        }
        if(CollectionUtils.isEmpty(sagaRecentResponse))
            return new SagaFavWrapperResponse(new ArrayList<>(),new HashMap<>(), 0);
        Future<Map<String,Integer>> futureService = homepageExecutor.submit(EncryptionUtil.wrapCallableWithLTC(
                () -> createCatIdCountMap(finalSagaRecentResponses)));

        futureCatMaps.add(futureService);
       // log.info("sagaRecentResponse is {}", JsonUtil.toJson(sagaRecentResponse));
        frequentOrders = recoWidgetService.prepareFavResponse(sagaRecentResponse, allRequestParams);

        if(eligibleInterstitialCount > 0) {
            interstitialData = interstitialManager.getEligibleInterstitalExistingImpression(sagaRecentResponses);
            if(interstitialData == null) { // Case where we are not able to fetch impression data from Aerospike or there is no interstitial eligible
                log.error("FavouriteManagerImpl.getFrequentOrdersForHomeReminderV2 :: interstitialData is null for cust id: {}", customerId);
                eligibleInterstitialCount = 0;
            }
        }

        log.info("FavouriteManagerImpl.getFrequentOrdersForHomeReminderV2 :: eligibleInterstitialCount :: {} for cust id: {}", eligibleInterstitialCount, customerId);
        try {
            List<HashMap<String, Object>> frequentOrderResponse = createResponse(frequentOrders);
            return new SagaFavWrapperResponse(frequentOrderResponse, interstitialData, eligibleInterstitialCount);
        } catch (JsonProcessingException e) {
            log.error("FavouriteManagerImpl.getFrequentOrdersByFilter :: for customer_id :: error while creating response  :: " + customerId, e);
            metricsAgent.incrementEventCount("home_remainder_frequent_order_error");
        }
        return new SagaFavWrapperResponse(new ArrayList<>(),new HashMap<>(), 0);

    }

    @MethodLatencyMetricsAction(metricsName = "bff_aerospike_get_latency")
    private Record aerospikeGet(Key key, Long customerId){
    	Record record = null;
        try {
            record = aerospikeClient.get(null, key);
        } catch (Exception ex) {
            log.error("Aerospike is down/not responding while get for cust id: {} exception : {}",customerId, ex);
            metricsAgent.incrementEventCount("hr_error_in_get_cache");
        }
        return record;

    }

    private void convertSagaResposneJsonToPojoAndSort(ArrayList<SagaFavResponse> sagaFavResponses, ArrayList<SagaRecentResponse> sagaRecentResponses) {
        log.info("[FavouiteManagerImpl.convertSagaResposneJsonToPojoAndSort] :: function starting");
        for (SagaFavResponse sagaFavResponse : sagaFavResponses) {
            JSONObject sagaObj = sagaFavResponse.getFavOrdersData();
            //log.info("saga rece respons --" + sagaObj.toString());
            SagaRecentResponse sagaRecentResponse = JsonUtil.fromJson(sagaObj.toString(), SagaRecentResponse.class);
            if ((Objects.nonNull(sagaRecentResponse.getAutomaticState()) && sagaRecentResponse.getAutomaticState() ==5) || (Objects.nonNull(sagaRecentResponse.getEventState())
                    && sagaRecentResponse.getEventState().toString().equalsIgnoreCase(RENEW_AUTOMATIC.toString())) || (Objects.nonNull(sagaRecentResponse.getEventType())
                    && sagaRecentResponse.getEventType().toString().equalsIgnoreCase(SMART_RECENT.toString())) ||
                    (Objects.nonNull(sagaRecentResponse)
                            && Objects.nonNull(sagaRecentResponse.getBill())))
                sagaRecentResponses.add(sagaRecentResponse);
        }

        /*
        // Sort in Descending order

        Collections.sort(sagaRecentResponses, new Comparator<SagaRecentResponse>() {
            @Override
            public int compare(SagaRecentResponse o1, SagaRecentResponse o2) {
                Date dueDate1 = DateUtil.stringToDate(o1.getBill().getBillDueDate(), DateFormat.DATE_TIME_FORMAT_2);
                Date dueDate2 = DateUtil.stringToDate(o2.getBill().getBillDueDate(), DateFormat.DATE_TIME_FORMAT_2);

                return dueDate2.compareTo(dueDate1);
            }
        });

         */
    }
    private void cacheRecentSagaResponse(ArrayList<SagaRecentResponse> sagaRecentResponses, Long customerId, boolean isCacheValue, Key key, boolean isCCTimedOut) {
        if(isCCTimedOut) {
            log.info("CC timed out for Customer ID: {} ; not caching in Aerospike.", customerId);
            metricsAgent.incrementEventCount(HR_NOT_SAVED_IN_CACHE_CC_TIMEOUT);
            return;
        }
        if (sagaRecentResponses.size() > 0 && !isCacheValue) {
            try {
                WritePolicy policy = new WritePolicy(aerospikeClient.writePolicyDefault);
                policy.expiration = getExpirationTime(RecentUtils.getSmartReminderTTL());

                if (Boolean.TRUE.equals(ServiceConfigCache.getInstance().getBoolean(ServiceConfigConstants.ENABLE_HR_ENCRYPTION_KEY)) && isCustomerIdRolloutPercentage(customerId)) {
                    // Convert sagaRecentResponses to JSON string before encrypting
                    String jsonData = JsonUtil.toJson(sagaRecentResponses);
                    // Encrypt the JSON string
                    String encryptedData = aesUtil.encrypt(jsonData);
                    log.info("Encrypted data size = {}",encryptedData.getBytes(StandardCharsets.UTF_8).length);
                    // Create bins for encrypted data and encryption flag
                    Bin dataBin = AerospikeUtils.getSerialiazedBin(Constants.HR_BIN, encryptedData);
                    Bin encryptionFlagBin = new Bin(HR_ENCRYPTED_BIN, true);
                    log.info("Saving the encrypted data to aerospike for cust_id = {}",customerId);
                    // Save both bins
                    aerospikeClient.put(policy, key, dataBin, encryptionFlagBin);
                    metricsAgent.incrementEventCount(SAVING_ENCRYPTED_HR);
                } else {
                    String unencryptedJsonData = JsonUtil.toJson(sagaRecentResponses);
                    // Old flow - store unencrypted data
                    log.info("Unencrypted  data size = {}",unencryptedJsonData.getBytes(StandardCharsets.UTF_8).length);
                    Bin bin = AerospikeUtils.getSerialiazedBin(Constants.HR_BIN, sagaRecentResponses);
                    log.info("Saving the decrypted data to aerospike for cust_id = {}",customerId);
                    aerospikeClient.put(policy, key, bin);
                    metricsAgent.incrementEventCount(SAVING_DECRYPTED_HR);
                }

                metricsAgent.incrementEventCount(HR_SAVED_IN_CACHE_SUCCESS);
            } catch (Exception ex) {
                log.error("Aerospike is down/not responding {} {} {}", customerId, ex, getExpirationTimeBasedOnBillDate(sagaRecentResponses.get(0)));
                log.error("response is {} ", sagaRecentResponses);
                metricsAgent.incrementEventCount("hr_error_in_save_cache");
            }
        }
    }

    private List<SagaFavResponsePOJO> filterRecentAndSort(List<Callable<FrequentOrder>> callableTasks, ArrayList<SagaRecentResponse> sagaRecentResponses, List<String> serviceAvailable, Long customerId, Map<String, String> allRequestParams) {
        log.info("[FavouiteManagerImpl.filterRecentAndSort] ::  function starting, sagaRecentResponses size - {}", sagaRecentResponses.size());

        Map<String, SagaRecentResponse> uniqueMap = new HashMap<>();

        for (SagaRecentResponse sagaRecentResponse : sagaRecentResponses) {
            sagaRecentResponse.setCustomerId(customerId);
            sagaRecentResponse.setAllowedServices(serviceAvailable);
            sagaRecentResponse.setPid(ActiveInactivePidMapCache.getInstance().getActivePid(sagaRecentResponse.getPid()));

            if (RecentUtils.isRecentSkippable(sagaRecentResponse, serviceAvailable)) {
                log.info("FavouriteManagerImpl.filterRecentAndSort :: skipping recent for customer_id - {}, recharge number  {}, and pid - {}", customerId, sagaRecentResponse.getRechargeNumber1(), sagaRecentResponse.getPid());
                continue;
            }
            String uniqueRecentKey = RecentUtils.getUniqueKey(sagaRecentResponse, includeOperatorInKeyServices);


            if (sagaRecentResponse.getBill()!=null || (Objects.nonNull(sagaRecentResponse.getAutomaticState()) && sagaRecentResponse.getAutomaticState() ==5) || sagaRecentResponse.getEventState().equalsIgnoreCase(RENEW_AUTOMATIC)){
                if(!uniqueMap.containsKey(uniqueRecentKey)){
                    uniqueMap.put(uniqueRecentKey, sagaRecentResponse);
                } else{
                    if((Objects.nonNull(sagaRecentResponse.getAutomaticState()) && sagaRecentResponse.getAutomaticState() ==5) || sagaRecentResponse.getEventState().equalsIgnoreCase(RENEW_AUTOMATIC)){
                        uniqueMap.put(uniqueRecentKey, sagaRecentResponse);
                    }
                    else if(Objects.nonNull(sagaRecentResponse.getBill())){
                        if (RecentUtils.getPartialBillEnabledCategories().contains(CVRProductCache.getInstance().getProductDetails(Long.valueOf(sagaRecentResponse.getPid())).getCategoryId().toString()) && sagaRecentResponse.getBill().getBillDueDate() == null) {
                            uniqueMap.put(uniqueRecentKey, sagaRecentResponse);
                        } else if(DateUtil.stringToDate(uniqueMap.get(uniqueRecentKey).getBill().getBillDueDate(), DateFormat.DATE_TIME_FORMAT_2).compareTo(DateUtil.stringToDate(sagaRecentResponse.getBill().getBillDueDate(), DateFormat.DATE_TIME_FORMAT_2)) > 0) {
                            uniqueMap.put(uniqueRecentKey, sagaRecentResponse);
                        }
                    }
                }
            }
        }


        List<SagaRecentResponse> sagaRecentResponseList = new ArrayList<>(uniqueMap.values());
        List<SagaFavResponsePOJO> sagaFavResponsePOJOList = new ArrayList<>();
        if(Utils.recoPriorityEnabled(customerId) && Objects.nonNull(allRequestParams.get(RECO_REQUEST_IDENTIFIER)) && TRUE.equalsIgnoreCase(allRequestParams.get(RECO_REQUEST_IDENTIFIER))){
            log.info("Comparing saga recent response for Reco");
            Collections.sort(sagaRecentResponseList, new SagaRecentResponseComparator(homePageSagaRecentComparator));
        } else {
            log.info(" Inside DefaultSagaRecentResponseComparator.compare");
            Collections.sort(sagaRecentResponseList, new SagaRecentResponseComparator(defaultSagaRecentResponseComparator));
        }
        sagaRecentResponseList = sagaRecentResponseList.subList(0, Math.min(RecentUtils.getSmartReminderCustomerBillLimit(), sagaRecentResponseList.size()));
        for (SagaRecentResponse sagaRecentResponse : sagaRecentResponseList) {
            SagaFavResponsePOJO sagaFavResponsePOJO  = new SagaFavResponsePOJO();
            BeanUtils.copyProperties(sagaRecentResponse, sagaFavResponsePOJO);
            sagaFavResponsePOJO.setEventType(EventType.valueOf(sagaRecentResponse.getEventType().toString()));
            sagaFavResponsePOJO.setBillState(RecentUtils.getState(sagaRecentResponse,sagaFavResponsePOJO).toString());
            sagaFavResponsePOJOList.add(sagaFavResponsePOJO);
        }

        return sagaFavResponsePOJOList;

    }

    @Override
    public ArrayList<HashMap<String, Object>> getRecentOrders(Long customerId, FrequentOrdersRequest frequentOrdersRequest, HashMap<String,String> allRequestParams) {

        Future<SagaLastTwoRecentsResponseWrapper> SagaRecentResponseWrapperCompletableFuture = fetchLatestRecentsFromSaga(customerId, frequentOrdersRequest,allRequestParams);

        List<SagaLastTwoRecentsResponse> sagaLastTwoRecentResponses = null;

        try {
            if (Objects.nonNull(SagaRecentResponseWrapperCompletableFuture)) {
                SagaLastTwoRecentsResponseWrapper sagaRecentResponseWrapper= SagaRecentResponseWrapperCompletableFuture.get(sagaThreadTimeout, TimeUnit.MILLISECONDS);
                if(Objects.nonNull(sagaRecentResponseWrapper)){
                    sagaLastTwoRecentResponses = sagaRecentResponseWrapper.getData();
                }
            }
        } catch (InterruptedException e) {
            log.error("FavouriteManagerImpl.getRecentOrders  :: error while fetching sagaRecents ", e);
            //Thread.currentThread().interrupt();

        } catch (ExecutionException | RuntimeException | TimeoutException e) {
            log.error("FavouriteManagerImpl.getRecentOrders  :: error while fetching sagaRecents ", e);
        }
        if(allRequestParams!=null)
            allRequestParams.put("API_VERSION","v1");
        return createCards(LatestTwoOrdersConstants.RECENT,sagaLastTwoRecentResponses, frequentOrdersRequest,allRequestParams);
    }

    private SagaLastTwoRecentsResponseWrapper fetchSagaLastTwoRecentsResponseWrapper(Long customerId, FrequentOrdersRequest frequentOrdersRequest, Map<String,String> allRequestParams){
        Future<SagaLastTwoRecentsResponseWrapper> sagaRecentResponseWrapperCompletableFuture = fetchLatestRecentsFromSaga(customerId, frequentOrdersRequest,allRequestParams);
        try {
            if (Objects.nonNull(sagaRecentResponseWrapperCompletableFuture)) {
                SagaLastTwoRecentsResponseWrapper sagaRecentResponseWrapper= sagaRecentResponseWrapperCompletableFuture.get(sagaThreadTimeout, TimeUnit.MILLISECONDS);
                return sagaRecentResponseWrapper;
            }
        } catch (InterruptedException e) {
            log.error("FavouriteManagerImpl.getRecentOrdersV2  :: error while fetching sagaRecents ", e);

        } catch (ExecutionException | RuntimeException | TimeoutException e) {
            log.error("FavouriteManagerImpl.getRecentOrdersV2  :: error while fetching sagaRecents ", e);
        }
        return null;
    }

    public Map<String,ArrayList<HashMap<String, Object>>> getRecentOrdersV2(Long customerId, FrequentOrdersRequest frequentOrdersRequest, HashMap<String,String> allRequestParams) {
        Map<String,ArrayList<HashMap<String, Object>>> res=new HashMap<>();
        List<SagaLastTwoRecentsResponse> sagaLastTwoRecentResponses = null;
        List<SagaLastTwoRecentsResponse> sagaSuggestedResponses = null;
        SagaLastTwoRecentsResponseWrapper sagaRecentResponseWrapper= fetchSagaLastTwoRecentsResponseWrapper(customerId, frequentOrdersRequest,allRequestParams);
        if(Objects.nonNull(sagaRecentResponseWrapper)){
            sagaLastTwoRecentResponses = sagaRecentResponseWrapper.getData();
            sagaSuggestedResponses = sagaRecentResponseWrapper.getSuggestedCards();
        }
        res.put("recent",createCards(LatestTwoOrdersConstants.RECENT,sagaLastTwoRecentResponses, frequentOrdersRequest,allRequestParams));
        res.put("suggestedCard",createCards(LatestTwoOrdersConstants.SUGGESTED_CARD,sagaSuggestedResponses, frequentOrdersRequest,allRequestParams));
        log.info("FavouriteManagerImpl.getRecentOrdersV2:: {}",res);
        return res;

    }

    private ArrayList<HashMap<String, Object>> createCards(String cardType,List<SagaLastTwoRecentsResponse> sagaLastTwoRecentResponses, FrequentOrdersRequest frequentOrdersRequest, HashMap<String,String> allRequestParams){
        ArrayList<HashMap<String, Object>> recentsMap = new ArrayList<>();
        HashMap<String, Object> recent;
        ObjectMapper objectMapper = new ObjectMapper();
        if(Objects.nonNull(sagaLastTwoRecentResponses)) {
            for (SagaLastTwoRecentsResponse sagaRecentResponse : sagaLastTwoRecentResponses) {
                try {
                    String sagaRecentResponseString = objectMapper.writeValueAsString(sagaRecentResponse);
                    recent = objectMapper.readValue(sagaRecentResponseString, HashMap.class);
                    HeadingProperties heading = getRecentsOrderHeading2(cardType,sagaRecentResponse, frequentOrdersRequest, allRequestParams);
                    recent.put("label", heading.getLabel());
                    recent.put("color", heading.getColor());
                    if(LatestTwoOrdersConstants.RECENT.equals(cardType)){
                        heading = getRecentsOrderHeading2(LatestTwoOrdersConstants.INVALID_PLAN_CARD,sagaRecentResponse, frequentOrdersRequest, allRequestParams);
                        Map<String,Object> discontinuedLabel=new HashMap<>();
                        discontinuedLabel.put("label", heading.getLabel());
                        discontinuedLabel.put("color", heading.getColor());
                        discontinuedLabel.put("cta",getRecentsOrderButtonCTA(LatestTwoOrdersConstants.INVALID_PLAN_CARD,sagaRecentResponse, frequentOrdersRequest, allRequestParams));
                        if(!(allRequestParams !=null
                                && allRequestParams.get("API_VERSION") !=null
                                && "v1".equals(allRequestParams.get("API_VERSION"))))
                            recent.put("discontinuedLabel",discontinuedLabel);
                    }
                    recent.put("cta",getRecentsOrderButtonCTA(cardType,sagaRecentResponse, frequentOrdersRequest, allRequestParams));

                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
                recentsMap.add(recent);
            }
        }
        return recentsMap;
    }

    private HeadingProperties getRecentsOrderHeading2(String cardType,SagaLastTwoRecentsResponse sagaRecentResponse, FrequentOrdersRequest frequentOrdersRequest, HashMap<String,String> allRequestParams){
        String eventState = sagaRecentResponse.getEventState();
        String billState = sagaRecentResponse.getBillState();
        if(LatestTwoOrdersConstants.INVALID_PLAN_CARD.equals(cardType)){
            eventState=LatestTwoOrdersConstants.INVALID_PLAN_CARD;
        }else if(LatestTwoOrdersConstants.SUGGESTED_CARD.equals(cardType)){
            eventState=LatestTwoOrdersConstants.SUGGESTED_CARD;
            billState = ""+Math.round(sagaRecentResponse.getAmount());
        }
        RecentWrapper recentWrapper = new RecentWrapper();
        recentWrapper.setClient(allRequestParams.get(CLIENT));
        recentWrapper.setVersion(allRequestParams.get(VERSION));
        recentWrapper.setLocale(LANGUAGE_ENGLISH);
        recentWrapper.setPayload(getLocalizationPayloadLite(recentWrapper, sagaRecentResponse));
        String[] heading2LabelKeys = new String[]{LANGUAGE_ENGLISH, RECENTS_V2, HEADING2, Utils.toUpperCase(eventState), Utils.toLowerCase(billState), PREPAID, frequentOrdersRequest.getService().toLowerCase()};
        String[] heading2ColorKeys = Arrays.stream(new String[]{LANGUAGE_ENGLISH, RECENTS_V2, HEADING2_COLOR, Utils.toUpperCase(eventState), Utils.toLowerCase(billState), PREPAID, frequentOrdersRequest.getService().toLowerCase()}).filter(org.apache.commons.lang3.StringUtils::isNotEmpty).toArray(String[]::new);
        String heading2Label = recentLocalisationKeyResolver.getMessage(heading2LabelKeys, true, recentWrapper.getPayload());
        String heading2Color = recentLocalisationKeyResolver.getMessage(heading2ColorKeys, true, recentWrapper.getPayload());
        HeadingProperties headingProperties = new HeadingProperties();
        headingProperties.setLabel(heading2Label);
        headingProperties.setColor(org.apache.commons.lang3.StringUtils.isNotBlank(heading2Color) ? heading2Color : DEFAULT_COLOR);
        return headingProperties;
    }

    private List<Map<String,String>> getRecentsOrderButtonCTA(String cardType,SagaLastTwoRecentsResponse sagaRecentResponse, FrequentOrdersRequest frequentOrdersRequest, HashMap<String,String> allRequestParams){
        List<Map<String,String>> buttons=new ArrayList<>();
        String eventState = sagaRecentResponse.getEventState();
        String billState = sagaRecentResponse.getBillState();
        if(LatestTwoOrdersConstants.INVALID_PLAN_CARD.equals(cardType)){
            eventState=LatestTwoOrdersConstants.INVALID_PLAN_CARD;
        }else if(LatestTwoOrdersConstants.SUGGESTED_CARD.equals(cardType)){
            eventState=LatestTwoOrdersConstants.SUGGESTED_CARD;
            billState = BillState.NO_DUE.name();
        }else{
            eventState=LatestTwoOrdersConstants.SUCCESS_CARD;
        }
        RecentWrapper recentWrapper = new RecentWrapper();
        recentWrapper.setClient(allRequestParams.get(CLIENT));
        recentWrapper.setVersion(allRequestParams.get(VERSION));
        recentWrapper.setLocale(LANGUAGE_ENGLISH);
        String[] heading2LabelKeys = new String[]{LANGUAGE_ENGLISH, RECENTS_V2, BUTTON_CTA_LABEL_CONSTANT, Utils.toUpperCase(eventState), Utils.toLowerCase(billState), PREPAID, frequentOrdersRequest.getService().toLowerCase()};
        String[] heading2ColorKeys = Arrays.stream(new String[]{LANGUAGE_ENGLISH, RECENTS_V2, BUTTON_CTA_LABEL_COLOR_CONSTANT, Utils.toUpperCase(eventState), Utils.toLowerCase(billState), PREPAID, frequentOrdersRequest.getService().toLowerCase()}).filter(org.apache.commons.lang3.StringUtils::isNotEmpty).toArray(String[]::new);
        String heading2Label = recentLocalisationKeyResolver.getMessage(heading2LabelKeys, true, recentWrapper.getPayload());
        String heading2Color = recentLocalisationKeyResolver.getMessage(heading2ColorKeys, true, recentWrapper.getPayload());
        Map<String,String> obj= new HashMap<>();
        obj.put("type", "button");
        obj.put("label", heading2Label);
        obj.put("color", Objects.nonNull(heading2Color) ? heading2Color : DEFAULT_CTA_LABEL_COLOR);
        buttons.add(obj);
        return buttons;
    }

    @Override
    public ArrayList<HashMap<String, Object>> getFrequentOrdersByFilter(Long customerId, FrequentOrdersRequest frequentOrdersRequest, HashMap<String,String> allRequestParams,boolean isCoftVersion) {

        CompletableFuture<SagaSavedCardResponse> savedCards = callForSavedCards(customerId, frequentOrdersRequest, allRequestParams, isCoftVersion);

        Future<ArrayList<FrequentOrder>> frequentOrdersFuture = submitTaskToRecentPostAPI(customerId, frequentOrdersRequest, allRequestParams);

        CompletableFuture<SagaSMSCardResponse> smsCards = callForSMSCards(customerId, allRequestParams, isCoftVersion,frequentOrdersRequest);

        Future<SagaFavResponsePOJOWrapper> sagaFavResponseWrapperCompletableFuture = prepareCallForRecentsFromSaga(customerId, frequentOrdersRequest,allRequestParams);

        ArrayList<FrequentOrder> frequentOrderArrayList = combineRecents(allRequestParams, sagaFavResponseWrapperCompletableFuture, frequentOrdersFuture);

        boolean isCardSkinRequired = frequentOrdersRequest.getIsCardSkinRequired();

        if (savedCards != null) {
            try {
                if (!isCoftVersion) {
                    frequentOrderArrayList = recentGenerator.mergeSavedCards(frequentOrderArrayList, savedCards.get(frequentOrdersThreadTimeout, TimeUnit.MILLISECONDS), allRequestParams);
                } else {
                    frequentOrderArrayList = recentGenerator.mergeSavedCardsWithCoft(frequentOrderArrayList, savedCards.get(frequentOrdersThreadTimeout, TimeUnit.MILLISECONDS), allRequestParams, rechargeHelperService.isCoftEnable(), isCardSkinRequired);
                }

            } catch (InterruptedException e) {
                metricsAgent.incrementEventCount(SAVED_CARD_MERGE_EVENT);
                log.error("FavouriteManagerImpl.getFrequentOrdersByFilter :: for customer_id :: error getting saved card  :: ", customerId, e);
                //Thread.currentThread().interrupt();

            } catch (ExecutionException | TimeoutException | RuntimeException e) {
                frequentOrderArrayList = recentGenerator.maskingRecents(frequentOrderArrayList);
                metricsAgent.incrementEventCount(SAVED_CARD_MERGE_EVENT);
                log.error("FavouriteManagerImpl.getFrequentOrdersByFilter :: for customer_id :: error getting saved card  :: ", customerId, e);
            }
        } else {
            log.info("callForSavedCards else");
            if (Constants.FINANCIAL_SERVICES.equalsIgnoreCase(frequentOrdersRequest.getService())) {

                frequentOrderArrayList = recentGenerator.maskingRecents(frequentOrderArrayList);
            }
        }

        if (smsEnabledInRecent()) {
            frequentOrderArrayList = recentGenerator.dedupeSMSSavedCards(frequentOrderArrayList);
        }

        if (isCoftVersion && Objects.nonNull(smsCards) && !smsEnabledInRecent()) {
            try {
                frequentOrderArrayList = recentGenerator.mergeSMSCards(frequentOrderArrayList, smsCards.get(frequentOrdersThreadTimeout, TimeUnit.MILLISECONDS), allRequestParams);
            } catch (InterruptedException ex) {
                log.error("FavouriteManagerImpl.getFrequentOrdersByFilter :: interrupt error while merging sms card :: {}", ex.getMessage());
                metricsAgent.incrementEventCount(SMS_CARD_MERGE_ERROR_EVENT);
                //Thread.currentThread().interrupt();

            } catch (ExecutionException | TimeoutException | RuntimeException ex) {
                log.error("FavouriteManagerImpl.getFrequentOrdersByFilter :: execution or timeout  error while merging sms card :: {}", ex.getMessage());
                metricsAgent.incrementEventCount(SMS_CARD_MERGE_ERROR_EVENT);
            }
        }
        return prepareResponse(frequentOrderArrayList, isCoftVersion, customerId);

    }

    @Override
    public UpdateNicknameResponse updateNickname(UpdateNicknameRequest updateNicknameRequest, HashMap<String, String> allRequestParams) {
        UpdateRecentResponse updateRecentResponse = new UpdateRecentResponse();
        UpdateNicknameResponse updateNicknameResponse = new UpdateNicknameResponse();

        try {

            UpdateRecentRequest updateRecentRequest = modelMapper.map(updateNicknameRequest, UpdateRecentRequest.class);
            ValueNickname valueNickname = new ValueNickname();
            valueNickname.setFieldName("nickname");
            valueNickname.setNickname(updateNicknameRequest.getNickname());
            updateRecentRequest.setFields(new ArrayList<>(Arrays.asList(valueNickname)));
            Future<UpdateRecentResponse> updateNicknameFuture = updateNickNameExecutor.submit(EncryptionUtil.wrapCallableWithLTC(
                    () -> favouriteClient.updateNickname(updateRecentRequest, allRequestParams)));

            updateRecentResponse = updateNicknameFuture.get(frequentOrdersThreadTimeout, TimeUnit.MILLISECONDS);
            updateNicknameResponse.setStatusCode(updateRecentResponse.getStatusCode());
            switch (updateRecentResponse.getStatusCode()) {
                case "00":
                    updateNicknameResponse.setMessage(ErrorMessage.SUCCESS_MESSAGE);
                    break;
                case "ERR01":
                    updateNicknameResponse.setMessage(ErrorMessage.MANDATORY_PARAMS_MISSING_MESSAGE);
                    break;
                case "ERR03":
                    updateNicknameResponse.setMessage(ErrorMessage.INVALID_NICKNAME);
                    break;
                case "ERR07":
                    updateNicknameResponse.setMessage(ErrorMessage.NO_RECORD_FOUND);
                    break;
                default:
                    updateNicknameResponse.setMessage(ErrorMessage.ERROR_MESSAGE);

            }
        } catch (Exception e) {
            log.error("updateNickname :: error updating nickname  :: ", e);
            metricsAgent.incrementEventCount("update_nickname_error");
            updateNicknameResponse.setStatusCode("ERR04");
            updateNicknameResponse.setMessage(ErrorMessage.ERROR_MESSAGE);
        }
        return updateNicknameResponse;
    }

    public ArrayList<HashMap<String, Object>> createResponse(ArrayList<FrequentOrder> frequentOrders) throws JsonProcessingException {
        return createResponse(frequentOrders,false);
    }

    public ArrayList<HashMap<String, Object>> createResponse(ArrayList<FrequentOrder> frequentOrders,boolean isApiV5version) throws JsonProcessingException {
        log.info("[FavouiteManagerImpl.createResponse] :: function starting");
        ArrayList<HashMap<String, Object>> recentsMap = new ArrayList<>();
        MutableBoolean isUserPlanCheckedForMobile =new MutableBoolean(false);

        for (FrequentOrder frequentOrder : frequentOrders) {
            redirectToPlansCheck(frequentOrder,recentsMap,isApiV5version,isUserPlanCheckedForMobile);
        }
        return recentsMap;
    }


    private Map<String,Object> checkForCategoryForRedirectToPlans(HashMap<String, Object> recent,Map<String, Object> productAttributes ,Map<String, Object> productDetails,Map<Long, String> categoryMap){

        HashMap<String,Object> mapForRedirectPlans=new HashMap<>();
        if (recent.containsKey(PRODUCT)) {
            productDetails = (Map<String, Object>) recent.get(PRODUCT);
            if (productDetails.containsKey((RECENT_ATTRIBUTES))) {
                productAttributes = (Map<String, Object>) productDetails.get(RECENT_ATTRIBUTES);

            }

        }

        mapForRedirectPlans.put(SERVICE, (productAttributes != null && productAttributes.containsKey(SERVICE)) ? productAttributes.get(SERVICE).toString() : (productDetails != null && productDetails.containsKey(SERVICE) ? productDetails.get(SERVICE).toString() : ""));
        mapForRedirectPlans.put(IS_CATEGORY_FOR_REDIRECT_PLANS,false);
        if (productAttributes != null && Objects.nonNull(productDetails) &&
                (Objects.nonNull(productDetails.get(CATEGORY_ID)) || Objects.nonNull(productDetails.get("category_id")))){
            String categoryId=Objects.nonNull(productDetails.get(CATEGORY_ID))?productDetails.get(CATEGORY_ID).toString():productDetails.get("category_id").toString();
            if(Objects.nonNull(categoryId) && Objects.nonNull(categoryMap) && Objects.nonNull(categoryMap.get(Long.parseLong(categoryId)))&&categoryMap.get(Long.parseLong(categoryId)).equalsIgnoreCase(MOBILE) && Objects.nonNull(productAttributes.get(Constants.CARD_PAYTYPE)) && productAttributes.get(Constants.CARD_PAYTYPE).toString().equalsIgnoreCase(PREPAID)){
                mapForRedirectPlans.put(IS_CATEGORY_FOR_REDIRECT_PLANS,true);
            }

        }

        return mapForRedirectPlans;

    }



    private void redirectToPlansCheck(FrequentOrder frequentOrder,ArrayList<HashMap<String, Object>> recentsMap,boolean isV5ApiVersion,MutableBoolean isUserPlanCheckedForMobile) throws JsonProcessingException {
        HashMap<String, Object> recent;
        Map<String, Object> productAttributes = null;
        Map<String, Object> productDetails = null;
        ObjectMapper objectMapper = new ObjectMapper();
        Map<Long, String> categoryMap = rechargeHelperService.getCategoryMap();
        int minusDays = 0;
        int plusDays = 0;
        recent = objectMapper.readValue(frequentOrder.getOrderData().toString(), HashMap.class);
        log.info("FavouriteManageImpl::redirectToPlansCheck :isV5ApiVersion {} and  isUserPlanCheckedForMobile  {}", isV5ApiVersion,isUserPlanCheckedForMobile);
        Map<String,Object> categoryForRedirectionPlans= checkForCategoryForRedirectToPlans(recent, productAttributes, productDetails, categoryMap);
        try {
            if ((boolean) categoryForRedirectionPlans.get(IS_CATEGORY_FOR_REDIRECT_PLANS)) {
                if (recent.containsKey("bills")) {
                    List<Map<String, Object>> billList = (List<Map<String, Object>>) recent.get("bills");
                    for (Map<String, Object> bill : billList) {
                        if (bill.containsKey("mark_as_paid_time")) {
                            continue;
                        }
                        if (bill.containsKey("expiry")) {//expiry_date
                            try {
                                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                                LocalDate date = null;
                                try {
                                    date = LocalDate.parse(bill.get("expiry").toString(), formatter);

                                } catch (Exception ex) {
                                    date = LocalDate.parse(bill.get("expiry").toString());
                                }

                                String[] keys = new String[]{Constants.LANGUAGE_ENGLISH, "BILL_DUE", "CONFIGURTION"};
                                Map<String, String> payload = new HashMap<>();
                                String locMessage = localisationManager.getMessage(keys, true, payload);

                                if (org.apache.commons.lang3.StringUtils.isEmpty(locMessage)) {
                                    minusDays = 3;
                                    plusDays = 3;
                                } else {
                                    try {
                                        JSONObject daysConfig = new JSONObject(locMessage);
                                        minusDays = daysConfig.getInt("minusDays");
                                        plusDays = daysConfig.getInt("plusDays");
                                    } catch (Exception ex) {
                                        minusDays = 3;
                                        plusDays = 3;
                                        log.error("Parsing exception occurred while fetch days configuration", ex);
                                    }
                                }
                                if (!LocalDate.now().isAfter(date.plusDays(plusDays)) && !LocalDate.now().isBefore(date.minusDays(minusDays))) {//make configurable //recent has +3 and -2
                                    recent.put(REDIRECT_TO_PLANS, false);
                                }


                            } catch (Exception ex) {
                                log.error("error occurred while checking for redirection key", ex);
                            }

                        }
                    }
                }
                if (!recent.containsKey(REDIRECT_TO_PLANS) && recent.containsKey("type") && (recent.get("type").toString().equals("drop_off") || recent.get("type").toString().equals("recharge_pending") || recent.get("type").toString().equals("recharge_failure") || recent.get("type").toString().equals("recharge_cancel"))) {
                    recent.put(REDIRECT_TO_PLANS, false);
                }
                if (!recent.containsKey(REDIRECT_TO_PLANS)) {
                    recent.put(REDIRECT_TO_PLANS, true);
                }
                if (recent.containsKey("isPartialBill") && recent.get("isPartialBill") != null && recent.get("isPartialBill").toString().equalsIgnoreCase("true")) {
                    recent.put(REDIRECT_TO_PLANS, true);
                }
                List<String> redirectToPlanOperatorList = ServiceConfigCache.getInstance().getList("redirectToPlans");
                Map<String, Object> product = (Map<String, Object>) recent.get("product");
                Map<String, Object> productAttr = (Map<String, Object>) product.get("attributes");
                if (Objects.nonNull(redirectToPlanOperatorList) && redirectToPlanOperatorList.contains(productAttr.get("operator").toString().toLowerCase())) {
                    recent.put(REDIRECT_TO_PLANS, true);
                }
            }
            recentsMap.add(recent);
            if (categoryForRedirectionPlans.get(SERVICE).toString().equalsIgnoreCase(MOBILE) && isV5ApiVersion) {
                if(Objects.nonNull(isUserPlanCheckedForMobile)&&! isUserPlanCheckedForMobile.booleanValue()){
                    populateChangeUserPlansOperator(recentsMap, recent, isUserPlanCheckedForMobile);
                }

            }
        } catch (Exception ex) {
            log.error("error occurred while checking for redirection key", ex);
        }
    }


    private ArrayList<FrequentOrder> filterParBasedCards(ArrayList<FrequentOrder> frequentOrders) {
        ArrayList<FrequentOrder> cinBasedFrequentOrders = new ArrayList<>();
        if (Objects.nonNull(frequentOrders)) {

            for (FrequentOrder recentCard : frequentOrders) {
                if (Objects.isNull(recentCard) || Objects.isNull(recentCard.getOrderData())) continue;
                JSONObject frequentOrderData = recentCard.getOrderData();


                if (isCinCard(frequentOrderData)) {
                    cinBasedFrequentOrders.add(recentCard);
                }

            }
        }
        return cinBasedFrequentOrders;
    }

    private boolean isCinCard(JSONObject frequentOrderData) {

        if (Objects.isNull(frequentOrderData.getJSONObject(Constants.OPERATOR_DATA)) && Objects.isNull(frequentOrderData.getJSONObject(Constants.OPERATOR_RECENT_DATA))) {
            return false;
        } else if (Objects.isNull(frequentOrderData.getJSONObject(Constants.OPERATOR_DATA)) && Objects.nonNull(frequentOrderData.getJSONObject(Constants.OPERATOR_RECENT_DATA))) {

            if (((!frequentOrderData.getJSONObject(Constants.OPERATOR_RECENT_DATA).has(Constants.PAR)) || frequentOrderData.getJSONObject(Constants.OPERATOR_RECENT_DATA).isNull(Constants.PAR) || frequentOrderData.getJSONObject(Constants.OPERATOR_RECENT_DATA).get(Constants.PAR).toString().isEmpty())) {
                return true;
            }
            return false;
        } else if (Objects.isNull(frequentOrderData.getJSONObject(Constants.OPERATOR_RECENT_DATA)) && Objects.nonNull(frequentOrderData.getJSONObject(Constants.OPERATOR_DATA))) {
            if (((!frequentOrderData.getJSONObject(Constants.OPERATOR_DATA).has(Constants.PAR)) || frequentOrderData.getJSONObject(Constants.OPERATOR_DATA).isNull(Constants.PAR) || frequentOrderData.getJSONObject(Constants.OPERATOR_DATA).get(Constants.PAR).toString().isEmpty())) {
                return true;
            }
            return false;
        } else if ((Objects.nonNull(frequentOrderData.getJSONObject(Constants.OPERATOR_DATA)) && ((!frequentOrderData.getJSONObject(Constants.OPERATOR_DATA).has(Constants.PAR)) || (frequentOrderData.getJSONObject(Constants.OPERATOR_DATA).isNull(Constants.PAR)) || (frequentOrderData.getJSONObject(Constants.OPERATOR_DATA).get(Constants.PAR).toString().isEmpty()))) && (Objects.nonNull(frequentOrderData.getJSONObject(Constants.OPERATOR_RECENT_DATA)) && ((!frequentOrderData.getJSONObject(Constants.OPERATOR_RECENT_DATA).has(Constants.PAR)) || frequentOrderData.getJSONObject(Constants.OPERATOR_RECENT_DATA).isNull(Constants.PAR) || (frequentOrderData.getJSONObject(Constants.OPERATOR_RECENT_DATA).get(Constants.PAR).toString().isEmpty())))) {
            return true;

        }
        return false;

    }

    public ArrayList<HomeReminderResponse> getHomeReminderResponse(Long customerId, HashMap<String, String> allRequestParams) throws IOException, ParseException {

        if (Objects.isNull(customerId) || agentAeroSpikeClientWrapper.isAgent(customerId)) return new ArrayList<>();

        List<HashMap<String, Object>> frequentOrders = getFrequentOrdersForHomeReminder(customerId, allRequestParams);
        if (frequentOrders.isEmpty()) return new ArrayList<>();
        ArrayList<HomeReminderResponse> homeRemainderResponse1 = convertToHRResponse((ArrayList<HashMap<String, Object>>) frequentOrders,null,customerId);
        log.debug("returning response customerId {} size {}", customerId, homeRemainderResponse1.size());
        if (homeRemainderResponse1.isEmpty()) {
            return new ArrayList<>();
        }
        return homeRemainderResponse1;
    }

    public RuWidgetResponse getHomeReminderResponseV2(Long customerId, HashMap<String, String> allRequestParams, RURecoRequest ruRecoRequest) throws Exception {

        if (Objects.isNull(customerId) || agentAeroSpikeClientWrapper.isAgent(customerId)) return new RuWidgetResponse();

        ruRecoRequest.setView(formatViewIds(ruRecoRequest));

        Long recoViewId = null;
        Long bannerViewId = null;
        for(RURecoViewRequest ruRecoViewRequest : ruRecoRequest.getView()){
            if(ruRecoViewRequest.getWidgetType().equalsIgnoreCase(RU_RECO_WIDGET))
                recoViewId = ruRecoViewRequest.getViewId();
            if(ruRecoViewRequest.getWidgetType().equalsIgnoreCase(RU_BANNER_WIDGET))
                bannerViewId = ruRecoViewRequest.getViewId();
        }

        if(Boolean.FALSE.equals(isValidForRuReco(recoViewId,bannerViewId,null))){
            return new RuWidgetResponse();
        }
        List<Future<Map<String,Integer>>> futureCatIdMap = new ArrayList<>();
        List<HashMap<String, Object>> frequentOrders = getFrequentOrdersForHomeReminderV1(customerId, allRequestParams, futureCatIdMap);
        if (frequentOrders.isEmpty()) return new RuWidgetResponse();
        RuWidgetResponse ruWidgetResponse = new RuWidgetResponse();

        ArrayList<HomeReminderResponse> homeRemainderResponse1 = convertToHRResponse((ArrayList<HashMap<String, Object>>) frequentOrders, recoViewId,customerId);

        if(homeRemainderResponse1.size()>1 && bannerViewId != null) {
            HomeReminderResponse thinBannerResponse = convertToHRResponseV2(homeRemainderResponse1, ruRecoRequest, frequentOrders, customerId, bannerViewId);
            if(recoViewId == null)
                homeRemainderResponse1.clear();
            homeRemainderResponse1.add(thinBannerResponse);
        }
        ruWidgetResponse.setRuReminderResponseList(homeRemainderResponse1);
        return ruWidgetResponse;
    }
    public RuWidgetResponse getHomeReminderResponseV3(Long customerId, HashMap<String, String> allRequestParams, RURecoRequest ruRecoRequest) throws Exception {
        long start_time = System.currentTimeMillis();
        if (Objects.isNull(customerId)) return new RuWidgetResponse();

        ruRecoRequest.setView(formatViewIds(ruRecoRequest));

        Long bannerViewId = null;
        Long bottomNavViewId = null;

        Map<Long, String> overrideViewIdToWidgetTypeMap = new HashMap<>();
        List<String> ruOverrideWidgetTypes = ServiceConfigCache.getInstance().getRuOverrideWidgetTypes();

        List<String> ruRemindersWidgetTypes = ServiceConfigCache.getInstance().getRuRemindersWidgetTypes();
        Set<Long> recoViewIds = new HashSet<>();

        for(RURecoViewRequest ruRecoViewRequest : ruRecoRequest.getView()) {
            RolloutHandler rolloutHandler = personalisationRolloutClassFactory.getRolloutClass(ruRecoViewRequest.getWidgetType());
            // If widget type is present on rollout config and if customer id allowed, go forward if not allowed then skip that widget
            if(Objects.nonNull(rolloutHandler) && !rolloutHandler.isAllowedForCustomerId(customerId)) {
                continue;
            }
            if(ruRemindersWidgetTypes != null && !ruRemindersWidgetTypes.isEmpty()) {
                if(ruRemindersWidgetTypes.contains(ruRecoViewRequest.getWidgetType()) &&
                        ruRecoViewRequest.getViewId() != null) {
                    recoViewIds.add(ruRecoViewRequest.getViewId());
                }
            } else {
                if (RU_RECO_WIDGET.equalsIgnoreCase(ruRecoViewRequest.getWidgetType()) &&
                        ruRecoViewRequest.getViewId() != null) {
                    recoViewIds.add(ruRecoViewRequest.getViewId());
                }
            }

            if (RU_BANNER_WIDGET.equalsIgnoreCase(ruRecoViewRequest.getWidgetType()))
                bannerViewId = ruRecoViewRequest.getViewId();

            if(ruOverrideWidgetTypes == null) {
                if (RU_OVERIDE_WIDGET.equalsIgnoreCase(ruRecoViewRequest.getWidgetType())
                        && ruRecoViewRequest.getViewId() != null) {
                    overrideViewIdToWidgetTypeMap.put(ruRecoViewRequest.getViewId(), ruRecoViewRequest.getWidgetType());
                }
            } else {
                if(ruOverrideWidgetTypes.contains(ruRecoViewRequest.getWidgetType())
                    && ruRecoViewRequest.getViewId() != null) {
                    overrideViewIdToWidgetTypeMap.put(ruRecoViewRequest.getViewId(), ruRecoViewRequest.getWidgetType());
                }
            }
            if ("bottom-nav".equalsIgnoreCase(ruRecoViewRequest.getWidgetType()))
                bottomNavViewId = ruRecoViewRequest.getViewId();
        }

        if(Boolean.FALSE.equals(isValidForRuRecoV2(recoViewIds,bannerViewId,overrideViewIdToWidgetTypeMap)) && bottomNavViewId==null){
            log.error("[FavouriteManagerImpl.getHomeReminderResponseV3] :: Invalid request for customer Id {}",customerId);
            metricsAgent.incrementEventCount(HOMEPAGE_INVALID_CALL);
            return new RuWidgetResponse();
        }
        List<Future<OverrideResponse>> futureServicesList = new ArrayList<>();
        List<Future<Map<String,Integer>>> futureCatIdMap = new ArrayList<>();
        SagaFavWrapperResponse sagaFavWrapperResponse = getFrequentOrdersForHomeReminderV2(customerId, allRequestParams, overrideViewIdToWidgetTypeMap, futureServicesList, futureCatIdMap);
        List<HashMap<String, Object>> frequentOrders = sagaFavWrapperResponse.getFrequentOrderResponse();

        RuWidgetResponse ruWidgetResponse = new RuWidgetResponse();
        OverrideResponse overrideResponse = new OverrideResponse();
        if(!CollectionUtils.isEmpty(overrideViewIdToWidgetTypeMap) && !CollectionUtils.isEmpty(futureServicesList)) {
            Integer personalisationThreadTimeout;
            try {
                personalisationThreadTimeout = ServiceConfigCache.getInstance().getInteger(PERSONALISATION_THREAD_TIMEOUT);
                if(personalisationThreadTimeout == null)
                    personalisationThreadTimeout = 15;
                Future<OverrideResponse> futureServices = futureServicesList.get(0);
                overrideResponse = futureServices.get(personalisationThreadTimeout, TimeUnit.MILLISECONDS);
            } catch (Exception ex) {
                log.error("ruleProcessor exception occured here", ex);
            }
        }
        if(CollectionUtils.isEmpty(overrideViewIdToWidgetTypeMap)) {
            overrideResponse.setStatus(OVERRIDE_RESPONSE_FAILURE);
            overrideResponse.setError(OVERRIDE_CONFIG_ERROR_MSG);

            log.error("[FavouriteManagerImpl.getHomeReminderResponseV3] :: Override config error for customer Id {} {}",customerId, ruRecoRequest);
        }

        if(overrideResponse!=null && !CollectionUtils.isEmpty(overrideResponse.getAdUnitNotifs()) && RequestContextHolder.getRequestAttributes() != null)
            RequestContextHolder.getRequestAttributes().setAttribute(RecentConstants.PERSONALISATION,Boolean.TRUE, RequestAttributes.SCOPE_REQUEST);


        if (frequentOrders.isEmpty()) {
            ruWidgetResponse.setRuOverrides(overrideResponse);
            return ruWidgetResponse;
        }

        ArrayList<HomeReminderResponse> homeRemainderResponse1 = convertToHRResponseForMultipleViewIds((ArrayList<HashMap<String, Object>>) frequentOrders,recoViewIds,customerId, sagaFavWrapperResponse.getImpressionMap(), sagaFavWrapperResponse.getEligibleInterstitial());

        Map<String, Integer> catIdCountMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(frequentOrders)) {
            Integer homePageThreadTimeout = 20;
            try {
                homePageThreadTimeout = ServiceConfigCache.getInstance().getInteger(HOMEPAGE_THREAD_TIMEOUT);
                if(homePageThreadTimeout == null)
                    homePageThreadTimeout =20;
                Future<Map<String,Integer>> futureService = futureCatIdMap.get(0);
                catIdCountMap = futureService.get(homePageThreadTimeout, TimeUnit.MILLISECONDS);
            }
            catch(Exception ex){
                    metricsAgent.recordExecutionTimeOfEvent(HOMEPAGE_THREAD_TIMEOUT_EVENT,homePageThreadTimeout);
                log.error("CatIdToCountMap exception occured here",ex);
            }
        }

        if(frequentOrders.size()>1 && bannerViewId != null) {
            HomeReminderResponse thinBannerResponse = convertToHRResponseV2(homeRemainderResponse1, ruRecoRequest, frequentOrders, customerId, bannerViewId);
            if(recoViewIds.isEmpty())
                homeRemainderResponse1.clear();
            homeRemainderResponse1.add(thinBannerResponse);
        }
        pushRedYellowLog(overrideResponse, customerId,catIdCountMap);
        ruWidgetResponse.setRuOverrides(overrideResponse);

        ruWidgetResponse.setRuReminderResponseList(homeRemainderResponse1);
        ruWidgetResponse.setCategoryIdToCountMap(catIdCountMap);

        log.info("FavouriteManagerImpl.getHomeReminderResponseV3:: ruWidgetResponse: {}", JsonUtil.toJson(ruWidgetResponse));
        long end_time = System.currentTimeMillis();
        double time_difference = (end_time - start_time);
        log.info("Time taken in total by the API ----->{}", time_difference);
        return ruWidgetResponse;
    }
    private void pushRedYellowLog(OverrideResponse overrideResponse, Long customerId, Map<String, Integer> catIdCountMap) {
        Map<String, String> serviceToCatIdMap = JsonUtil.convertObjectToMap(ServiceConfigCache.getInstance().get(SERVICE_CAT_ID_MAP));
        Set<String> serviceSet = new HashSet<>();
        LocalDate currentDate = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String formattedDate = currentDate.format(formatter);
        try {
            if (Objects.nonNull(overrideResponse) && Objects.nonNull(overrideResponse.getAdUnitNotifs())) {
                overrideResponse.getAdUnitNotifs().values().stream()
                        .flatMap(Collection::stream)
                        .filter(overrideResponseObject -> Objects.nonNull(overrideResponseObject.getWzrkInfo()) && Objects.nonNull(overrideResponseObject.getCombineKeys()))
                        .forEach(overrideResponseObject -> {
                            String service = getKeyByValue(serviceToCatIdMap, overrideResponseObject.getCombineKeys().getRuCategoryId());
                            if (Objects.isNull(overrideResponseObject.getWzrkInfo().getWzrkPivot()) || Objects.isNull(service) || !serviceSet.add(service)) {
                                return;
                            }

                            int wzrkPivot = Integer.parseInt(overrideResponseObject.getWzrkInfo().getWzrkPivot());
                            boolean yellowDotService = wzrkPivot == -1;
                            int redNumberCount = yellowDotService ? 0 : wzrkPivot;
                            String bannerId = "";
                            if (Objects.nonNull(overrideResponseObject.getCustomKV()) && Objects.nonNull(overrideResponseObject.getCustomKV().getBannerId())) {
                                bannerId = overrideResponseObject.getCustomKV().getBannerId();
                            }
                            RedYellowLogObj redYellowLogObj = new RedYellowLogObj(customerId, service, redNumberCount, yellowDotService, overrideResponseObject.getWzrkInfo().getWzrkId(), new Date().getTime(), formattedDate,bannerId);
                            redYellowLogger.info(redYellowLogObj);
                        });
            } else {
                catIdCountMap.forEach((catId, count) -> {
                    String service = getKeyByValue(serviceToCatIdMap, catId);
                    if (Objects.isNull(service) || !serviceSet.add(service)) {
                        return;
                    }

                    boolean yellowDotService = count == -1;
                    int redNumberCount = yellowDotService ? 0 : count;
                    RedYellowLogObj redYellowLogObj = new RedYellowLogObj(customerId, service, redNumberCount, yellowDotService, null, new Date().getTime(), formattedDate,null);
                    redYellowLogger.info(redYellowLogObj);
                });
            }
        } catch (Exception ex) {
            log.error("Error while pushing red yellow log", ex);
            metricsAgent.incrementEventCount(RED_YELLOW_LOG_PUSH_ERROR);
        }
    }
    public String getKeyByValue(Map<String, String> map, String value) {
        if(Objects.isNull(map) || Objects.isNull(value) || map.isEmpty()) {
            return null;
        }
        for (Map.Entry<String, String> entry : map.entrySet()) {
            if (value.equals(entry.getValue())) {
                return entry.getKey();
            }
        }
        return null; // Value not found
    }

    private int getExpirationTime() {
        LocalDateTime date = LocalDateTime.now();
        int seconds = date.toLocalTime().toSecondOfDay();
        long fixSec = 86400;
        return Integer.parseInt((fixSec - seconds) + "");
    }

    private int getExpirationTime(int days) {
        long fixSec = 86400;
        return Integer.parseInt((fixSec * days) + "");
    }

    private int getThinBannerExpirationTime() {
        Date date = new Date();
        Date endOfTheDate = DateUtil.getEndOfDayDate(date);
        long secDiff = (endOfTheDate.getTime() - date.getTime())/1000;
        return Integer.parseInt((secDiff) + "");
    }

    private int getExpirationTimeBasedOnBillDate(SagaRecentResponse sagaRecentResponse) {
        LocalDateTime dueDate = null;
        if (Objects.isNull(sagaRecentResponse.getBill()) || Objects.isNull(sagaRecentResponse.getBill().getBillDueDate())) return 0;

        try {
            dueDate = LocalDateTime.parse(sagaRecentResponse.getBill().getBillDueDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            LocalDateTime endOfDate = LocalTime.MAX.atDate(dueDate.toLocalDate());
            long dueDateToDayDiff = endOfDate.toEpochSecond(ZoneId.systemDefault().getRules().getOffset(Instant.now())) - (System.currentTimeMillis() / 1000);
            long fixSec = 86400 * (sagaRecentResponse.getBillVisibilityDays()) + dueDateToDayDiff;
            return Integer.parseInt((fixSec) + "");

        } catch (Exception e) {
            log.error("FrequentResponseHandler :: decideExpiryDetails unable to parse due date  :: {} ", sagaRecentResponse.getBill().getBillDueDate());
            return getExpirationTime();
        }

    }

    public ArrayList<HomeReminderResponse> convertToHRResponse(ArrayList<HashMap<String, Object>> frequentOrders, Long recoViewId, Long customerId) {
        Set<Long> recoViewIds = new HashSet<>();
        recoViewIds.add(recoViewId);
        return convertToHRResponseForMultipleViewIds(frequentOrders, recoViewIds,customerId,  new HashMap<String, InterstitialDataRecord>(), 0);
    }

    public ArrayList<HomeReminderResponse> convertToHRResponseForMultipleViewIds(ArrayList<HashMap<String, Object>> frequentOrders, Set<Long> recoViewIds, Long customerId, Map<String, InterstitialDataRecord> impressionMap, Integer eligibleInterstitials) {
        log.info("[FavouiteManagerImpl.convertToHRResponseForMultipleViewIds] :: function starting");
        ArrayList<HomeReminderResponse> homeRemainderResponses = new ArrayList<>();
        HomeReminderResponse remindersFolderViewObject = ServiceConfigCache.getInstance().getRemindersFolderViewObject();

        if(recoViewIds==null || CollectionUtils.isEmpty(recoViewIds)){
            log.info("[FavouriteManagerImpl.convertToHRResponseForMultipleViewIds] :: reco view id null or empty");
            return new ArrayList<>();
        }

        for(Long recoViewId : recoViewIds) {
            int count = 0;
            int interstitialAdded = 0;
            for (HashMap<String, Object> frequentOrder : frequentOrders) {
                HomeReminderResponse remainderResponse = null;
                int seenCount = 0;
                remainderResponse = convertToHRDTO(frequentOrder);

                if(interstitialAdded < eligibleInterstitials){
                    seenCount = interstitialManager.checkAndAddInterstitialDetails(frequentOrder, remainderResponse, impressionMap);
                    if (Objects.nonNull(remainderResponse.getCustomProperties())) {
                        interstitialAdded++;
                    }
                }


                if(recoViewId!=null) {
                    remainderResponse.setViewId(recoViewId);
                }
                if(Objects.nonNull(remainderResponse) && Boolean.FALSE.equals(excludeServicesFromComboReminder(recoViewId, String.valueOf(frequentOrder.get(RECENT_PRODUCT_ID))))) {
                    homeRemainderResponses.add(remainderResponse);
                    count++;
                }

                RecoLogObj recoLogObj = getRecoLogObject(customerId, remainderResponse, seenCount);
                try {
                    if (MOBILE.equalsIgnoreCase(remainderResponse.getService()) && RecentUtils.isWhitelistedCustId(customerId)) {
                        Long productId = Long.parseLong(String.valueOf(frequentOrder.get(RECENT_PRODUCT_ID)));
                        Product product = CVRProductCache.getInstance().getProductDetails(productId);
                        if (Objects.nonNull(product)&&Constants.PREPAID.equalsIgnoreCase(product.getPayType())) {
                            metricsAgent.incrementEventCount(MOBILE_PREPAID_RECO);

                        } else if (Objects.nonNull(product)&&POSTPAID.equalsIgnoreCase(product.getPayType())) {
                            metricsAgent.incrementEventCount(MOBILE_POSTPAID_RECO);

                        }
                        log.info("convertToHRResponseForMultipleViewIds:: serving reco for mobile {}",customerId);

                    }

                } catch (Exception ex) {
                    log.error("convertToHRResponseForMultipleViewIds:: Error while publishing mobile reco metrics");
                }

                if(Boolean.TRUE.equals(remainderResponse.getIsBillDue())){
                    logger.info(recoLogObj);
                }
            }

            if(!CollectionUtils.isEmpty(frequentOrders) && remindersFolderViewObject != null && recoViewId != null && remindersFolderViewObject.getViewId() == recoViewId) {
                    //add that dummy folder-view All Utility Bills object in the reminders
                    homeRemainderResponses.add(remindersFolderViewObject);
            }
            if(recoViewId != null && count>0)
                metricsAgent.increaseBatchCount(count, Arrays.asList(VIEW_ID + recoViewId,  Constants.EVENT_STATS_COUNT + RECO_VIEW_ID_COUNT));
        }
        return  homeRemainderResponses;
    }

    private RecoLogObj getRecoLogObject(Long customerId, HomeReminderResponse remainderResponse, Integer seenCount) {
        LocalDate currentDate = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String formattedDate = currentDate.format(formatter);
        String recoLogRN = remainderResponse.getRechargeNumber();
        if(FINANCIAL_SERVICES.equalsIgnoreCase(remainderResponse.getService()) && recoLogRN != null){
            recoLogRN = aesUtil.encrypt(recoLogRN);
        }
        List<String> tagsListForRecoEventsByService = new ArrayList<>();
        if (Objects.nonNull(remainderResponse.getService())) {
            tagsListForRecoEventsByService.add("category:" + remainderResponse.getService());

        } else {
            tagsListForRecoEventsByService.add("category:NA");
        }

        metricsAgent.incrementEventCount("RECO_LOG_EVENT", tagsListForRecoEventsByService);
        return new RecoLogObj(customerId, remainderResponse.getReconId(), new Date().getTime(),formattedDate,
                remainderResponse.getDueDate(), remainderResponse.getBillDate(), remainderResponse.getAmount(), recoLogRN,
                remainderResponse.getService(), remainderResponse.getPaytype(), remainderResponse.getSortableDueDate(),
                seenCount, remainderResponse.getCustomProperties() != null);

    }
    private HomeReminderResponse convertToHRResponseV2(ArrayList<HomeReminderResponse> homeReminderResponses, RURecoRequest ruRecoRequest, List<HashMap<String, Object>> frequentOrders, Long customerId, Long bannerViewId) throws UnsupportedEncodingException {
        HomeReminderResponse remainderResponse;

        Key key = new Key(SMARTREMINDER, null, TB_KEY.concat(String.valueOf(customerId)));

        remainderResponse = checkIfResponseExistsInCache(key);
        if (remainderResponse == null) {
            remainderResponse = convertToRuReco(homeReminderResponses, bannerViewId, frequentOrders);
            Bin bin = AerospikeUtils.getSerialiazedBin(RU_RECO, remainderResponse);
            saveThinBannerResponseInCache(key,bin);
        }
        remainderResponse.setViewId(bannerViewId);
        return remainderResponse;
    }

    private String getGaCategory(Product product, HashMap<String, Object> config, String reconId, HomeReminderResponse homeReminderResponse){

        StringBuilder gaCategory=  new StringBuilder()
                .append(product.getServiceKey())
                .append("_")
                .append(product.getPayTypeKey())
                .append("_")
                .append(product.getRecoOperatorName())
                .append("_")
                .append(getRechargeNumber(config, product))
                .append("_");

        if(Objects.nonNull(homeReminderResponse) && Objects.nonNull(homeReminderResponse.getItemLayout())){
            gaCategory.append("NewBill");
        } else {
            gaCategory.append(getEventType(config));
        }

        if (reconId != null)
            gaCategory.append("_")
                    .append(reconId);

        return gaCategory.toString();

    }

    private boolean isCCProduct(Product product) {
        return product != null && FINANCIAL_SERVICES.equalsIgnoreCase(product.getService());
    }

    private String getRechargeNumber(HashMap<String, Object> config, Product product) {
        String rechargeNumber = String.valueOf(config.get("recharge_number"));
        if(isCCProduct(product) && rechargeNumber != null) {
            return aesUtil.encrypt(rechargeNumber); //encrypting the recharge number for financial services
        }
    	return rechargeNumber;
    }

    private String getEventType(HashMap<String, Object> config) {
        return Objects.nonNull(config.get("allEventState")) && config.get("allEventState").toString().startsWith("DATA_EXHAUST")
                ? config.get("allEventState").toString().replace("_","") :String.valueOf(config.get(EVENT_TYPE));
    }


    public HomeReminderResponse convertToHRDTO(HashMap<String, Object> frequentOrder) {
        HomeReminderResponse homeRemainderResponse = new HomeReminderResponse();
        String[] bannerIdKey = new String[]{Constants.LANGUAGE_ENGLISH, BANNER_KEY, CONFIGURATION_LOCAL};
        List<String> keyComponents = new ArrayList<>();

        Map<String, String> payload = new HashMap<>();
        String bannerId = recentLocalisationManager.getMessage(bannerIdKey, true, payload);

        if (frequentOrder.get("uniqueId") != null) {
            String uniqueId = (String) frequentOrder.get("uniqueId");
            homeRemainderResponse.setUniqueId(uniqueId);
            homeRemainderResponse.setItemKey(uniqueId);
        }
        if (frequentOrder.get("type") != SMART_RECENT) {
            homeRemainderResponse.setIsBillDue(true);
        }
        if(frequentOrder.get("subscriberDetails") != null){
            homeRemainderResponse.setSubscriberDetails(frequentOrder.get("subscriberDetails"));
        }

        Map<String, Object> additionalInfoMap = (Map<String, Object>) frequentOrder.get("additionalInfo");

        if(additionalInfoMap != null && ObjectUtils.isNotEmpty(additionalInfoMap.get(RECON_ID))){
            String reconId = (String) additionalInfoMap.get(RECON_ID);
            if(reconId != null) {
                homeRemainderResponse.setReconId(reconId);
            }
        }

        if (!org.apache.commons.lang3.StringUtils.isEmpty(bannerId)) {
            homeRemainderResponse.setBannerId(bannerId);
            try {
                homeRemainderResponse.setId(Long.parseLong(homeRemainderResponse.getBannerId()));
            } catch (Exception e) {
                log.error("error while creating home reminder response id ", e);
            }

        }
        homeRemainderResponse.setItemType(RECCO);
        homeRemainderResponse.setFilter(RU_UPPERCASE);
        String operatorLogoUrlFromLocalisation = getOperatorLogoUrlFromLocalisation(frequentOrder);
        boolean isOperatorLogoUrlPresentInLocalisation = Objects.nonNull(operatorLogoUrlFromLocalisation) && !operatorLogoUrlFromLocalisation.isEmpty();
        if (isOperatorLogoUrlPresentInLocalisation) {
            homeRemainderResponse.setOperatorLogo(operatorLogoUrlFromLocalisation);
        } else if (frequentOrder.containsKey("ope_logo_url")) {
            homeRemainderResponse.setOperatorLogo((String) frequentOrder.get("ope_logo_url"));
        }

        Map<String, Object> ctaObj;
        Product product = new Product();
        if (frequentOrder.containsKey(RECENT_PRODUCT_ID)) {

            product = homeReminderResponseHandlerUtil.fetchProductDetails(String.valueOf(frequentOrder.get(RECENT_PRODUCT_ID)));
            keyComponents.add(product.getVerticalId().toString());
            keyComponents.add(product.getMerchantId().toString());
            homeRemainderResponse.setName(product.getService());
            Map attrMap =new ObjectMapper().convertValue(product.getAttributes(), Map.class);
            String name1=" ";String name2="Bill";
            String[] titleKey;
            payload.put(Constants.SERVICE, product.getService());
            payload.put(Constants.CARD_PAYTYPE, product.getPayType());
            titleKey = recoResponseManager.getTitleKey(product, payload);

            name1 = recentLocalisationManager.getMessage(titleKey, true, payload);
            if (Objects.nonNull(name1)) {
                homeRemainderResponse.setName(name1);
            }
            if (org.apache.commons.lang3.StringUtils.isEmpty(name1)) {
                if (product.getPayType().equalsIgnoreCase("credit card")) {
                    name1 = product.getPayType();
                    homeRemainderResponse.setName(name1);
                } else {
                    name1 = product.getService();
                    name1 = Objects.nonNull(name1) ? name1 : "";
                    if (attrMap.containsKey("bill_text") && !attrMap.get("bill_text").toString().isEmpty())
                        name2 = attrMap.get("bill_text").toString();
                    homeRemainderResponse.setName(name1.concat(" ").concat(name2));
                }
            }

            //populate product.getService() in homeRemainderResponse
            homeRemainderResponse.setService(product.getService());
            homeRemainderResponse.setPaytype(product.getPayType());

        }

        HashMap<String, Object> config = getConfigFromFrequentOrder(frequentOrder);

        if(Objects.nonNull(frequentOrder.get("customer_name"))){
            homeRemainderResponse.setTitle((String) frequentOrder.get("customer_name"));
        }else if(PAYTM_POSTPAID_SERVICE.equalsIgnoreCase(product.getService())){
            homeRemainderResponse.setTitle("");
        }else {
            if(frequentOrder.containsKey(CONFIGURATION_LOCAL.toLowerCase(Locale.ROOT)) && Objects.nonNull(config)){
                product = CVRProductCache.getInstance().getProductDetails(Long.parseLong(frequentOrder.get(RECENT_PRODUCT_ID).toString()));
                homeRemainderResponse.setTitle(recoResponseManager.getFormattedTitle(product, config, payload));
                log.info("FavouriteManagerImpl.convertToHRDTO :: homeRemainderResponse.setTitle :: {}", homeRemainderResponse.getTitle());
            }
        }
        if(FASTAG_SERVICE.equalsIgnoreCase(product.getService())){
            String[] titleKey = new String[]{LANGUAGE_ENGLISH, RECO, RECO_FASTAG_TITLE};
            payload.put(RECHARGE_NUMBER, (String) config.get(RECHARGE_NUMBER));
            String title = recentLocalisationManager.getMessage(titleKey, true, payload);
            homeRemainderResponse.setTitle(title);
        }

        if(product.getService() != null && ELECTRICITY.equalsIgnoreCase(product.getService().toLowerCase()) && frequentOrder.get("isPrepaid") != null && (Boolean) frequentOrder.get("isPrepaid")){
            homeRemainderResponse.setIsPrepaid(true);
        }

        if(frequentOrder.containsKey(CONFIGURATION_LOCAL.toLowerCase(Locale.ROOT)) && Objects.nonNull(config)){
            //populate recharge number in home reminder response
            homeRemainderResponse.setRechargeNumber((String) config.get(RECHARGE_NUMBER));
        }

        if(frequentOrder.get("isRenewSubscription") != null && Boolean.parseBoolean(frequentOrder.get("isRenewSubscription").toString())){
            homeRemainderResponse.setPriority("1");
        }


        if(PAYTM_POSTPAID_SERVICE.equalsIgnoreCase(product.getService())){
            homeRemainderResponse.setPriority("0");
        }



        if (frequentOrder.containsKey("bills") && Objects.nonNull(frequentOrder.get("bills"))) {
            ArrayList<HashMap<String, Object>> billist;
            billist = (ArrayList<HashMap<String, Object>>) frequentOrder.get("bills");
            HashMap<String, Object> bill = billist.get(0);

            if (Objects.nonNull(bill) && (Objects.nonNull(bill.get("due_date")) || Objects.nonNull(bill.get("expiry")))) {
                String dueDateString = getFormattedDueDate(bill);

                homeRemainderResponse.setDueDate(dueDateString);

                LocalDateTime dueDate = null;
                try {
                    dueDate = LocalDateTime.parse(dueDateString, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                } catch (Exception e) {
                    log.error("FrequentResponseHandler :: decideExpiryDetails unable to parse due date  :: {} ", dueDateString);
                }

                long dueDateToDayDiff = Utils.getDayDiffBetweenDates(LocalDateTime.now().with(LocalTime.MIDNIGHT), dueDate.with(LocalTime.MIDNIGHT));
                if (Objects.nonNull(dueDateToDayDiff))
                    homeRemainderResponse.setCtVariantId(String.valueOf(dueDateToDayDiff));
                homeRemainderResponse.setItemLayout(setItemLayout(product, frequentOrder, dueDate));


                //populate bill_date, amount in homeReminderResponse
                homeRemainderResponse.setBillDate(getFormattedBillDate(bill));
                homeRemainderResponse.setAmount(getFormattedAmountFromBill(bill));
            }

        }
        homeRemainderResponse.setGaCategory(getGaCategory(product, config, getReconId(frequentOrder), homeRemainderResponse));

        if(frequentOrder.containsKey("cta_obj")&&Objects.nonNull(frequentOrder.get("cta_obj"))){
            ctaObj = (Map<String, Object>) frequentOrder.get("cta_obj");
            Map<String, Object> heading3 = null;
            Map<String, Object> heading2 = null;
            String longSubtitle = null;
            if (ctaObj.containsKey(HEADING2) && Objects.nonNull(ctaObj.get(HEADING2))) {
                heading2 = (Map<String, Object>) ctaObj.get(HEADING2);
                if (heading2.containsKey("label") && Objects.nonNull(heading2.get("label")))
                    longSubtitle = (String) heading2.get("label");
                if (heading2.containsKey("color") && Objects.nonNull(heading2.get("color")))
                    homeRemainderResponse.setSubTitleColor((String) heading2.get("color"));
            }
            if(ctaObj.containsKey(HEADING3)&&Objects.nonNull(ctaObj.get(HEADING3))){
                heading3 = (Map<String, Object>) ctaObj.get(HEADING3);
                if(heading3.containsKey("label")&&Objects.nonNull(heading3.get("label"))&&ObjectUtils.isEmpty(heading3.get("label")))
                    longSubtitle.concat(", ").concat( heading3.get("label").toString());
                if(Objects.nonNull(homeRemainderResponse.getSubTitleColor())){
                    if(heading3.containsKey("color")&&Objects.nonNull(heading3.get("color")))
                        longSubtitle.concat(", ").concat((String) heading3.get("color"));
                }
            }
//             if(ctaObj.containsKey("cta")&&Objects.nonNull(ctaObj.get("cta")))
//                 homeRemainderResponse.setCtaObjects( ctaObj.get("cta"));
            if(ctaObj.containsKey("cta")&&Objects.nonNull(ctaObj.get("cta"))){
                try{
                    ArrayList<HashMap<String,Object>> allowedCTAsArray = (ArrayList<HashMap<String,Object>>) ctaObj.get("cta");

                    if(Objects.nonNull(allowedCTAsArray)&&!allowedCTAsArray.isEmpty()){
                        for (Map<String, Object> allowedCTA : allowedCTAsArray) {
                            JSONObject ctaInfo =  new JSONObject(allowedCTA);
                            if (ctaInfo.has("childCta")) {
                                addDismissActions(homeRemainderResponse, ctaInfo);

                            }
                            else {
                                addCTAObj(homeRemainderResponse, ctaInfo);
                            }
                        }
                    }

                }catch (Exception ex){
                    log.error("Error while parsing child cta ", ex);
                }

            }

            homeRemainderResponse.setLongSubtitle(longSubtitle);
            homeRemainderResponse.setSubtitle(longSubtitle);
            if (isOperatorLogoUrlPresentInLocalisation) {
                homeRemainderResponse.setOperatorLogo(operatorLogoUrlFromLocalisation);
            }
            else if (frequentOrder.containsKey("operatorLogoUrl") && Objects.nonNull(frequentOrder.get("operatorLogoUrl"))) {
                homeRemainderResponse.setOperatorLogo((String) frequentOrder.get("operatorLogoUrl"));
            }

        }
        if (frequentOrder.containsKey("sortableDueDate") && Objects.nonNull(frequentOrder.get("sortableDueDate"))) {
            homeRemainderResponse.setSortableDueDate((String) frequentOrder.get("sortableDueDate"));
        }

        //homeRemainderResponse.setReason(HomeReminderConstants.RESPONSE_REASON);
        homeRemainderResponse.setCtCampaignId(String.format(HomeReminderConstants.RESPONSE_CT_CAMPAIGNID, DateUtil.formatDate(new Date(), DateFormat.YYYYMMDD)));
        return homeRemainderResponse;
    }
    private Object setItemLayout(Product product, HashMap<String, Object> frequentOrder, LocalDateTime dueDate) {
        JSONObject homeReminderItemLayout=new JSONObject();
        /*Map<String, Object> additionalInfoMap = (Map<String, Object>) frequentOrder.get("additionalInfo");
        boolean isNewBillIdentified =false;
        if (Objects.nonNull(additionalInfoMap) && Objects.nonNull(additionalInfoMap.get(IS_NEW_BILL_IDENTIFIED))) {
            isNewBillIdentified = Boolean.TRUE.toString().equalsIgnoreCase(additionalInfoMap.get(IS_NEW_BILL_IDENTIFIED).toString());
        }*/
        Boolean isNewBillTagForAll = ServiceConfigCache.getInstance().getBoolean(ServiceConfigConstants.NEW_BILL_TAG_FOR_ALL);
        if(Boolean.TRUE.equals(isNewBillTagForAll) || isNonTransactingUser(frequentOrder)) {
            String eventState = frequentOrder.containsKey("allEventState") ? frequentOrder.get("allEventState").toString() : EMPTY_STRING;

            Long orderID= frequentOrder.containsKey("orderId") ? Long.parseLong(frequentOrder.get("orderId").toString()) : null;
            if(EventState.RECHARGE_SUCCESS.name().equalsIgnoreCase(eventState) && Objects.isNull(orderID)) {
                eventState = EventState.NEW_ACCOUNT.name();
            }
            String billState = frequentOrder.containsKey("billState") ? frequentOrder.get("billState").toString() : EMPTY_STRING;
            String serviceKey = Objects.nonNull(product) ? product.getServiceKey() : EMPTY_STRING;
            String payTypeKey = Objects.nonNull(product) ? product.getPayTypeKey() : EMPTY_STRING;
            String operator = product.getRecoOperatorName();
            String[] bgKeys = new String[]{LANGUAGE_ENGLISH, NEW_BILL_TAG_BG_COLOUR, eventState, billState, serviceKey, operator, payTypeKey};
            String[] textKeys = new String[]{LANGUAGE_ENGLISH, NEW_BILL_TAG_TEXT_COLOUR, eventState,billState, serviceKey, operator, payTypeKey};
            String[] newBillTagLabelKey = new String[]{LANGUAGE_ENGLISH, NEW_BILL_TAG_LABEL_KEY, eventState, billState, serviceKey, operator, payTypeKey};
            String bgMessage = recentLocalisationManager.getMessage(bgKeys, true, new HashMap<>());
            String textColourMessage = recentLocalisationManager.getMessage(textKeys, true, new HashMap<>());
            String newBillTagLabelMessage = recentLocalisationManager.getMessage(newBillTagLabelKey, true, new HashMap<>());
            if (org.apache.commons.lang3.StringUtils.isEmpty(bgMessage) || org.apache.commons.lang3.StringUtils.isEmpty(textColourMessage) || org.apache.commons.lang3.StringUtils.isEmpty(newBillTagLabelMessage)) return null;

            homeReminderItemLayout.put("label_bgcolor", bgMessage);
            homeReminderItemLayout.put("label_text_color", textColourMessage);
            homeReminderItemLayout.put("label", newBillTagLabelMessage);
            LocalDate expirationDate = dueDate.toLocalDate().plusDays(3);
            Date date = Date.from(expirationDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
            homeReminderItemLayout.put("expiry_time", DateUtil.formatDateTimeInGMT(date));
            log.info("Item lay out response {}" , homeReminderItemLayout);
            return homeReminderItemLayout.toMap();
        }
        return null;

    }

    private Double getFormattedAmountFromBill(HashMap<String, Object> bill) {
        Double billAmount = null;
        try {
            if (Objects.nonNull(bill.get(AMOUNT))) {
                Object amountObject = bill.get(AMOUNT);
                if (amountObject instanceof Number) {
                    billAmount = ((Number) amountObject).doubleValue();
                } else {
                    billAmount = Double.parseDouble(amountObject.toString());
                }
            }
        } catch(Exception e) {
            log.error("Error while parsing amount from bill", e);
        }

        return billAmount;
    }

	private HashMap<String, Object> getConfigFromFrequentOrder(HashMap<String, Object> frequentOrder) {
		HashMap<String, Object> config = new HashMap<>();
        
       if(ObjectUtils.isNotEmpty(frequentOrder.get(CONFIGURATION_LOCAL.toLowerCase(Locale.ROOT)))) {
    	   config = (HashMap<String, Object>) frequentOrder.get(CONFIGURATION_LOCAL.toLowerCase(Locale.ROOT));
           config.put("allEventState",frequentOrder.get("allEventState"));
       }
       
		return config;
	}

    private String getReconId(HashMap<String, Object> frequentOrder) {
        HashMap<String, Object> config = new HashMap<>();

        if(ObjectUtils.isNotEmpty(frequentOrder.get(TITLE_ADDITIONAL_INFO))) {
            config = (HashMap<String, Object>) frequentOrder.get(TITLE_ADDITIONAL_INFO);
        }

        if (!CollectionUtils.isEmpty(config) && config.get(RECON_ID) != null) {
            return config.get(RECON_ID).toString();
        }

        return StringLiterals.NA;
    }

    private String getFormattedDueDate(HashMap<String, Object> bill) {
        if (Objects.nonNull(bill.get("due_date")))
            return DateUtil.stringDateFormat(bill.get("due_date").toString(), DATE_FORMAT_1, DATE_FORMAT_FOR_TIMESTAMP);
        else
            return DateUtil.stringDateFormat(bill.get("expiry").toString(), DATE_FORMAT_1, DATE_FORMAT_FOR_TIMESTAMP);
    }

    private String getFormattedBillDate(HashMap<String, Object> bill) {
        if (Objects.nonNull(bill.get("bill_date")))
            return DateUtil.stringDateFormat(bill.get("bill_date").toString(), DATE_FORMAT_1, DATE_FORMAT_FOR_TIMESTAMP);
        else
            return null;
    }

    private String getFormattedDueDateForThinBanner(HashMap<String, Object> bill) {
        if (Objects.nonNull(bill.get("due_date")))
            return DateUtil.stringDateFormat(bill.get("due_date").toString(), DATE_FORMAT_1, DDMMM);
        else
            return DateUtil.stringDateFormat(bill.get("expiry").toString(), DATE_FORMAT_1, DDMMM);
    }

    private void addCTAObj(HomeReminderResponse homeRemainderResponse, JSONObject ctaInfo) {

            JSONObject homeReminderCTA=new JSONObject();
            homeReminderCTA.put("url",ctaInfo.get("deeplink"));
            JSONObject addInfoCta = ctaInfo.getJSONObject("additionalInfo");
            homeReminderCTA.put("url_type",addInfoCta.get("url_type"));
            homeReminderCTA.put("label",ctaInfo.get("label"));
            homeRemainderResponse.setCtaObjects(homeReminderCTA.toMap());
            homeRemainderResponse.setUrl(String.valueOf(ctaInfo.get("deeplink")));
            homeRemainderResponse.setUrlType(String.valueOf(addInfoCta.get("url_type")));

    }

    private void addDismissActions(HomeReminderResponse homeRemainderResponse, JSONObject obj) {
            JSONArray jsonArray = obj.getJSONArray("childCta");
            JSONArray finalChildCTA=new JSONArray();
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject childCtaObject=new JSONObject();
                JSONObject childCtaInfo = jsonArray.getJSONObject(i);
                childCtaObject.put("label",childCtaInfo.get("label"));
                childCtaObject.put("api",childCtaInfo.get("deeplink"));
                JSONObject addInfoChild = childCtaInfo.getJSONObject("additionalInfo");
                childCtaObject.put("icon_url",addInfoChild.get("icon_url"));
                finalChildCTA.put(childCtaObject.toMap());
            }
            homeRemainderResponse.setDisMissActions(finalChildCTA.toList());

    }

    private List<String> getSmSEnabledServices(Map<String, String> allRequestParams) {
        Map<String, Set<Long>> smsCategoryMap = JsonUtil.convertObjectToMap(ServiceConfigCache.getInstance().get(Constants.CATEGORY_MAPPING));
        List<String> smsEnabledServiceList = JsonUtil.convertObjectToList(ServiceConfigCache.getInstance().get(Constants.SMS_ENABLED_SERVICES));
        if(Objects.nonNull(smsEnabledServiceList)){
            return smsEnabledServiceList.stream().filter(service -> Objects.nonNull(smsCategoryMap.get(service)) && rechargeHelperService.isFeatureLiveForCategory(new HashSet<Long>(smsCategoryMap.get(service)), FeatureConfigName.isSmsPrepaid.name().toLowerCase(Locale.ROOT), allRequestParams.get(Constants.CLIENT), allRequestParams.get(Constants.VERSION))).collect(Collectors.toList());
        }
        return null;

    }

    private boolean smsEnabledInRecent() {

        Boolean enableSMSCardInRecent = ServiceConfigCache.getInstance().getBoolean(ServiceConfigConstants.ENABLE_SMS_CARD_IN_RECENT);
        return  enableSMSCardInRecent;
    }

    private HashMap<String, Object> prepareFrequentRequest(FrequentOrdersRequest frequentOrdersRequest) {
        HashMap<String, Object> filters = frequentOrdersRequest.getFilters();
        filters.put(Constants.SERVICE, frequentOrdersRequest.getService());
        if (frequentOrdersRequest.getOperator() != null) {
            filters.put(Constants.OPERATOR, frequentOrdersRequest.getOperator());
        }

        if (frequentOrdersRequest.getRecharge_number() != null) {
            filters.put(Constants.RECHARGE_NUMBER, frequentOrdersRequest.getRecharge_number());
        }
        return filters;
    }

    private Map<String, Object> settingQueryParams(HashMap<String, String> allRequestParams, boolean isCoftVersion) {
        Map<String, Object> queryParams = new HashMap<>();
        if (allRequestParams == null) {
            allRequestParams = new HashMap<>();
        }
        boolean locMessage = rechargeHelperService.isCoftEnable();
        if (Objects.nonNull(locMessage)) {
            if (!isCoftVersion) {
                allRequestParams.put(Constants.IS_COFT, String.valueOf(locMessage));
                queryParams.put(Constants.IS_COFT, locMessage);
            }
        } else {
            if (!isCoftVersion) {
                allRequestParams.put(Constants.IS_COFT, "false");
                queryParams.put(Constants.IS_COFT, "false");

            } else {
                if (rechargeHelperService.isCoftEnable()) {
                    queryParams.put(Constants.IS_COFT, true);
                }

            }
            if (isCoftVersion && rechargeHelperService.isCoftEnable()) {
                allRequestParams.put(Constants.IS_COFT, "true");
            }
        }

        if (allRequestParams.containsKey(BILL_TYPE)) {
            queryParams.put(BILL_TYPE, allRequestParams.get(BILL_TYPE));
        }

        return queryParams;
    }


    private CompletableFuture<SagaSMSCardResponse> callForSMSCards(Long customerId, Map<String, String> allRequestParams, boolean isCoftVersion ,FrequentOrdersRequest frequentOrdersRequest) {
        CompletableFuture<SagaSMSCardResponse> smsCards = null;
        if (!smsEnabledInRecent() && isCoftVersion) {
            try {
                List<String> servicesList = getSmSEnabledServices(allRequestParams);
                if(Objects.nonNull(frequentOrdersRequest) && Objects.nonNull(frequentOrdersRequest.getService())){
                    servicesList=servicesList.contains(frequentOrdersRequest.getService().toLowerCase())?new ArrayList<>(Arrays.asList(frequentOrdersRequest.getService().toLowerCase())):new ArrayList<>();
                }
                log.info("services list  for sms {}",servicesList);
                if(Objects.nonNull(servicesList) && ! servicesList.isEmpty()){
                    smsCards = sagaManager.getSMSCardsFromSaga(customerId, allRequestParams, servicesList);
                }
               // smsCards = sagaManager.getSMSCardsFromSaga(customerId, allRequestParams, servicesList);

            } catch (Exception e) {
                log.error("FavouriteManagerImpl.getFrequentOrdersByFilter :: for customer_id :: error while getting sms card  response  :: " + customerId, e);
                metricsAgent.incrementEventCount(SMS_CARD_ERROR_EVENT);
            }
        }
        return smsCards;
    }

    private CompletableFuture<SagaSavedCardResponse> callForSavedCards(Long customerId, FrequentOrdersRequest frequentOrdersRequest, Map<String, String> allRequestParams, boolean isCoftVersion) {
        CompletableFuture<SagaSavedCardResponse> savedCards = null;
        if (Constants.FINANCIAL_SERVICES.equalsIgnoreCase(frequentOrdersRequest.getService())) {
            Map<String, Object> queryParams = settingQueryParams((HashMap<String, String>) allRequestParams, isCoftVersion);
            boolean isCardSkinRequired = frequentOrdersRequest.getIsCardSkinRequired();
            try {
                if (!isCoftVersion) {
                    savedCards = sagaManager.getSavedCardsFromSaga(customerId, queryParams);
                } else {
                    savedCards = sagaManager.getSavedCardsFromSagaV2(customerId, queryParams, isCardSkinRequired);
                }

            } catch (RuntimeException e) {
                log.error("FavouriteManagerImpl.getFrequentOrdersByFilter :: for customer_id :: error while getting saved card  response  :: " + customerId, e);
                metricsAgent.incrementEventCount(SAVED_CARD_EVENT);
            }
        }
        log.info("savedCards {}",savedCards);
        return savedCards;
    }

    private ArrayList<HashMap<String, Object>> prepareResponse(ArrayList<FrequentOrder> frequentOrders, boolean isCoftVersion, Long customerId) {
        if (Objects.nonNull(frequentOrders)) {
            //log.info("FavouriteManagerImpl.getFrequentOrdersByFilter :: recent response for customerId :: {} , response :: {}", customerId, frequentOrders.toString());
            try {
                if (!isCoftVersion) {
                    return createResponse(filterParBasedCards(frequentOrders));
                }
                return createResponse(frequentOrders);
            } catch (JsonProcessingException e) {
                log.error("FavouriteManagerImpl.getFrequentOrdersByFilter :: for customer_id :: error while creating response  :: " + customerId, e);
                metricsAgent.incrementEventCount(FREQUENT_ORDER_ERROR);
            }
        }
        return new ArrayList<>();
    }


    private Future<ArrayList<FrequentOrder>> prepareGetCallForRecents(Long customerId, Map<String, String> allRequestParams) {

        Future<ArrayList<FrequentOrder>> frequentOrdersFuture = null;
        try {
            return frequentOrderExecutorService.submit(EncryptionUtil.wrapCallableWithLTC(() -> favouriteClient.getFrequentOrdersByCustomerId(customerId, (HashMap<String, String>) allRequestParams)));
        } catch (RuntimeException e) {
            log.error("FavouriteManagerImpl.getFrequentOrdersByCustomerId :: init :: error while creating response for customer_id :: getFrequentOrdersByCustomerId :: " + customerId, e);
            metricsAgent.incrementEventCount(FREQUENT_ORDER_ERROR);
        }
        return frequentOrdersFuture;
    }

    private Future<ArrayList<FrequentOrder>> prepareDataForRecentGet(Long customerId, Map<String, String> allRequestParams) {
        Future<ArrayList<FrequentOrder>> frequentOrdersFuture = null;
        if (isValidForFavouriteRecents(allRequestParams, null)) {
            frequentOrdersFuture = prepareGetCallForRecents(customerId, allRequestParams);
        }

        return frequentOrdersFuture;
    }

    private Future<ArrayList<FrequentOrder>> submitTaskToRecentPostAPI(Long customerId, FrequentOrdersRequest frequentOrdersRequest, Map<String, String> allRequestParams) {
        HashMap<String, Object> filters = prepareFrequentRequest(frequentOrdersRequest);
        if (isValidForFavouriteRecents(allRequestParams, filters)) {
            try {
                List<String> segMetrics = new ArrayList<>();
                segMetrics.add(METRIC_SERVICE + frequentOrdersRequest.getService());
                metricsAgent.incrementEventCount(MARKET_FAV_FREQUENT_ORDER_API, segMetrics);

                return frequentOrderExecutorService.submit(EncryptionUtil.wrapCallableWithLTC(() -> favouriteClient.getFrequentOrdersByFilter(customerId, filters, (HashMap<String, String>) allRequestParams)));

            } catch (RuntimeException e) {
                log.error("FavouriteManagerImpl.getFrequentOrdersByFilter :: for customer_id :: error getting frequent orders  :: " + customerId, e);
                metricsAgent.incrementEventCount("frequent_order_executor_error");
            }
        }

        return null;
    }

    private boolean isValidForFavouriteRecents(Map<String, String> allRequestParams, Map<String, Object> filters) {
        List<String> excludedServices = getSagaEnabledServices();
        if (Objects.nonNull(excludedServices) && excludedServices.isEmpty()) {
            return false;
        }
        if (Objects.nonNull(filters) && Objects.nonNull(filters.get(SERVICE)) && Objects.nonNull(excludedServices) && excludedServices.contains(filters.get(SERVICE).toString().toLowerCase())) {
            return false;
        }
        if ((Objects.isNull(filters) || Objects.isNull(filters.get(SERVICE)))&&(Objects.nonNull(excludedServices))) {
            allRequestParams.put(EXCLUDED_SERVICES, StreamUtils.convertListToString(excludedServices));
        }
        return true;
    }


    public List<HashMap<String, Object>> getV1FrequentOrders(Long customerId, Map<String, String> allRequestParams) {
        Future<ArrayList<FrequentOrder>> frequentOrdersFuture = prepareDataForRecentGet(customerId, allRequestParams);
        return fetchSagaRecentAndMerge(frequentOrdersFuture, customerId, null, allRequestParams);

    }

    public List<HashMap<String, Object>> fetchSagaRecentAndMerge(Future<ArrayList<FrequentOrder>> frequentOrdersFuture, Long customerId, FrequentOrdersRequest frequentOrdersRequest, Map<String, String> allRequestParams) {
        Future<SagaFavResponsePOJOWrapper> sagaFavResponseWrapperCompletableFuture = null;
        sagaFavResponseWrapperCompletableFuture = prepareCallForRecentsFromSaga(customerId, frequentOrdersRequest,allRequestParams);
        ArrayList<FrequentOrder> frequentOrderArrayList = combineRecents(allRequestParams, sagaFavResponseWrapperCompletableFuture, frequentOrdersFuture);
        try {
            return createResponseV1(frequentOrderArrayList);
        } catch (JsonProcessingException ex) {
            log.error("Error in parsing ", ex);
        }
        return null;
    }

    public List<HashMap<String, Object>> postV1FrequentOrders(Long customerId, FrequentOrdersRequest frequentOrdersRequest, Map<String, String> allRequestParams) {
        Future<ArrayList<FrequentOrder>> frequentOrdersFuture = submitTaskToRecentPostAPI(customerId, frequentOrdersRequest, allRequestParams);
        return fetchSagaRecentAndMerge(frequentOrdersFuture, customerId, frequentOrdersRequest, allRequestParams);

    }


    private Future<SagaFavResponsePOJOWrapper> prepareCallForRecentsFromSaga(Long customerId, FrequentOrdersRequest frequentOrdersRequest,Map<String,String> requestQueryParams) {


        List<String> sagaEnabledServices = getSagaEnabledServices();
        if (Objects.isNull(sagaEnabledServices) || (Objects.nonNull(frequentOrdersRequest) && Objects.nonNull(frequentOrdersRequest.getService()) && !sagaEnabledServices.isEmpty() && !sagaEnabledServices.contains(frequentOrdersRequest.getService().toLowerCase()))) {
            return null;
        }

        boolean maskingFlag = rechargeHelperService.featureForRollout(Constants.MASKED_CARD) || rechargeHelperService.isCoftEnable();
        HashMap<String, Object> queryParams = this.getQueryParamsForSagaRequest(maskingFlag,requestQueryParams);

        return sagaRecentExecutor.submit(EncryptionUtil.wrapCallableWithLTC(() -> sagaManager.getRecentsFromSaga(customerId, frequentOrdersRequest, sagaEnabledServices, queryParams)));


    }

    private Future<SagaLastTwoRecentsResponseWrapper> fetchLatestRecentsFromSaga(Long customerId, FrequentOrdersRequest frequentOrdersRequest, Map<String,String> requestQueryParams) {


        List<String> sagaEnabledServices = getSagaEnabledServices();
        if (Objects.isNull(sagaEnabledServices) || (Objects.nonNull(frequentOrdersRequest) && Objects.nonNull(frequentOrdersRequest.getService()) && !sagaEnabledServices.isEmpty() && !sagaEnabledServices.contains(frequentOrdersRequest.getService().toLowerCase()))) {
            return null;
        }

        HashMap<String, Object> queryParams = this.getQueryParamsForSagaRequest(false,requestQueryParams);

        return sagaRecentExecutor.submit(EncryptionUtil.wrapCallableWithLTC(() -> sagaManager.getLastTwoRecentsFromSaga(customerId, frequentOrdersRequest, sagaEnabledServices, queryParams)));


    }

    private List<String> getSagaEnabledServices() {
        List<String> sagaEnabledServiceList = null;
        try {
            sagaEnabledServiceList = JsonUtil.convertObjectToList(ServiceConfigCache.getInstance().get(Constants.SAGA_ENABLED_SERVICES));
        } catch (Exception e) {
            log.error("Exception in getting smsEnabledServiceList", e);
        }
        return sagaEnabledServiceList;

    }


    private HashMap<String, Object> getQueryParamsForSagaRequest(boolean isCoft,Map<String,String> requestQueryParams) {
        HashMap<String, Object> queryParams = new HashMap<>();
        queryParams.putAll(requestQueryParams);
        if (isCoft) {
            queryParams.put(Constants.IS_COFT, true);
        }
        return queryParams;
    }

    private static <E> void addAllIfNotNull(List<E> list, Collection<? extends E> c) {
        if (c != null) {
            list.addAll(c);
        }
    }

    private ArrayList<HashMap<String, Object>> createResponseV1(ArrayList<FrequentOrder> frequentOrders) throws JsonProcessingException {

        ArrayList<HashMap<String, Object>> recentsMap = new ArrayList<>();
        HashMap<String, Object> recent;
        ObjectMapper objectMapper = new ObjectMapper();
        for (FrequentOrder frequentOrder : frequentOrders) {
            recent = objectMapper.readValue(frequentOrder.getOrderData().toString(), HashMap.class);
            recentsMap.add(recent);
        }
        return recentsMap;
    }
    private List<RURecoViewRequest> formatViewIds(RURecoRequest ruRecoRequest){
        Map<String, List<Long>> viewIdWidgetMap = JsonUtil.convertObjectToMap(ServiceConfigCache.getInstance().get(VIEW_ID_WIDGET_MAPPING));
        List<Integer> bottomNavViewIds = JsonUtil.convertObjectToList(ServiceConfigCache.getInstance().get("bottomNavViewIds"));
        List<RURecoViewRequest> finalView = new ArrayList<>();
        if (viewIdWidgetMap != null) {
            for (RURecoViewRequest ruRecoViewRequest : ruRecoRequest.getView()) {
                if(bottomNavViewIds!=null && bottomNavViewIds.contains(ruRecoViewRequest.getViewId().intValue())){
                    ruRecoViewRequest.setWidgetType("bottom-nav");
                    finalView.add(ruRecoViewRequest);
                    log.info("[FavouriteManagerImpl.formatViewIds] :: valid call for bottom nav with view id {}",ruRecoViewRequest);
                }
                else if (Objects.nonNull(viewIdWidgetMap.get(ruRecoViewRequest.getWidgetType()))) {
                    if (viewIdWidgetMap.get(ruRecoViewRequest.getWidgetType()).contains(ruRecoViewRequest.getViewId())) {
                        finalView.add(ruRecoViewRequest);
                    }
                } else {
                    ruRecoViewRequest.setViewId(null);
                    finalView.add(ruRecoViewRequest);
                }
            }
            return finalView;
        }
        return new ArrayList<>();
    }
    private HomeReminderResponse convertToRuReco(ArrayList<HomeReminderResponse> homeReminderResponses, Long viewId, List<HashMap<String, Object>> frequentOrder) throws UnsupportedEncodingException {
        HomeReminderResponse thinBannerResponse = new HomeReminderResponse();
        thinBannerResponse.setViewId(viewId);
        thinBannerResponse.setCtCampaignId(String.format(HomeReminderConstants.RESPONSE_CT_CAMPAIGNID, DateUtil.formatDate(new Date(), DateFormat.YYYYMMDD)));
        setValuesForRuReco(frequentOrder,homeReminderResponses, thinBannerResponse);
        thinBannerResponse.setSeourl(thinBannerResponse.getUrl());
        thinBannerResponse.setMetadataSourceId("1");
        thinBannerResponse.setSlotId("1");
        return thinBannerResponse;
    }
    private void setValuesForRuReco(List<HashMap<String, Object>> frequentOrders, List<HomeReminderResponse> homeReminderResponses, HomeReminderResponse thinBannerResponse) throws UnsupportedEncodingException {
        Product product = homeReminderResponseHandlerUtil.fetchProductDetails(String.valueOf(frequentOrders.get(0).get(RECENT_PRODUCT_ID)));
        HashMap<String, Object> config = getConfigFromFrequentOrder(frequentOrders.get(0));
        
        String ctVariantId = getGaCategory(product, config, null, null);
        thinBannerResponse.setCtVariantId(ctVariantId);
        if(frequentOrders.size()==2){
            getThinBannerV1(frequentOrders,homeReminderResponses,thinBannerResponse);
        }else if(frequentOrders.size()==3){
            getThinBannerV2(frequentOrders,homeReminderResponses,thinBannerResponse);
        }
        else {
            getThinBannerV3(frequentOrders,homeReminderResponses,thinBannerResponse);
        }
    }
    private void getThinBannerV1(List<HashMap<String, Object>> frequentOrders, List<HomeReminderResponse> homeReminderResponses, HomeReminderResponse thinBannerResponse) throws UnsupportedEncodingException {
        Product product1 = homeReminderResponseHandlerUtil.fetchProductDetails(String.valueOf(frequentOrders.get(1).get(RECENT_PRODUCT_ID)));
        HashMap<String, Object> config1 = getConfigFromFrequentOrder(frequentOrders.get(1));

        String[] keys = new String[]{LANGUAGE_ENGLISH, RECO,RU,THIN_BANNER_IMAGE_URL_1};
        Map<String, String> payload = new HashMap<>();

        String title;
        if(PAYTM_POSTPAID_SERVICE_KEY.equalsIgnoreCase(product1.getServiceKey()))
            title = homeReminderResponses.get(1).getName();
        else title = homeReminderResponses.get(1).getName()+" "+"-"+" "+homeReminderResponses.get(1).getTitle();

        if(title.length()>RU_THIN_BANNER_1_LIMIT)
            title = title.substring(0,RU_THIN_BANNER_1_LIMIT-3)+"...";

        Map<String, Object> homeReminderCTA;

        homeReminderCTA = JsonUtil.fromObject(homeReminderResponses.get(1).getCtaObjects(),Map.class);

        payload.put(RU_RECO_TITLE,encodedPayload(title));
        payload.put(HEADING,encodedPayload(homeReminderResponses.get(1).getLongSubtitle()));
        if(Objects.nonNull(product1.getProductAttributes().getDynamicBannerLogoId()))
            payload.put(RU_BRAND_LOGO,product1.getProductAttributes().getDynamicBannerLogoId());
        else payload.put(RU_BRAND_LOGO,"");
        payload.put(RU_RECO_CTA,homeReminderCTA.get("label").toString());
        String imageUrl = recentLocalisationManager.getMessage(keys, true, payload);

        String[] bannerIdKey = new String[]{Constants.LANGUAGE_ENGLISH, RECO,RU, RU_BANNER_KEY_1};
        setThinBannerId(bannerIdKey,thinBannerResponse);

        thinBannerResponse.setOperatorLogo(imageUrl);
        thinBannerResponse.setAltImageUrl(thinBannerResponse.getOperatorLogo());
        thinBannerResponse.setGaCategory(getGaCategory(product1, config1, null,null));
        thinBannerResponse.setUrl(homeReminderResponses.get(1).getUrl());
    }
    private void getThinBannerV2(List<HashMap<String, Object>> frequentOrders, List<HomeReminderResponse> homeReminderResponses, HomeReminderResponse thinBannerResponse) throws UnsupportedEncodingException, UnsupportedEncodingException {
        Product product1 = homeReminderResponseHandlerUtil.fetchProductDetails(String.valueOf(frequentOrders.get(1).get(RECENT_PRODUCT_ID)));
        Product product2 = homeReminderResponseHandlerUtil.fetchProductDetails(String.valueOf(frequentOrders.get(2).get(RECENT_PRODUCT_ID)));
        
        HashMap<String, Object> config1 = getConfigFromFrequentOrder(frequentOrders.get(1));
        HashMap<String, Object> config2 = getConfigFromFrequentOrder(frequentOrders.get(2));

        String[] keys = new String[]{LANGUAGE_ENGLISH,RECO,"ru","image_url_2"};

        String title1 = product1.getProductAttributes().getMyBillsHeading();
        if(Objects.nonNull(title1) && title1.length()>RU_THIN_BANNER_2_LIMIT)
            title1 = title1.substring(0,RU_THIN_BANNER_2_LIMIT-3)+"...";

        String title2 = product2.getProductAttributes().getMyBillsHeading();
        if(Objects.nonNull(title2) && title2.length()>RU_THIN_BANNER_2_LIMIT)
            title2 = title2.substring(0,RU_THIN_BANNER_2_LIMIT-3)+"...";

        Map<String, String> payload = new HashMap<>();

        payload.put(RU_RECO_TITLE_1, encodedPayload(title1));
        payload.put(HEADING1,encodedPayload(getLabel(frequentOrders.get(1))));
        if(Objects.nonNull(product1.getProductAttributes().getDynamicBannerLogoId()))
            payload.put(RU_BRAND_LOGO_1,product1.getProductAttributes().getDynamicBannerLogoId());
        else payload.put(RU_BRAND_LOGO_1,"");
        payload.put(RU_RECO_TITLE_2,encodedPayload(title2));
        payload.put(HEADING2,encodedPayload(getLabel(frequentOrders.get(2))));
        if(Objects.nonNull(product2.getProductAttributes().getDynamicBannerLogoId()))
            payload.put(RU_BRAND_LOGO_2,product2.getProductAttributes().getDynamicBannerLogoId());
        else payload.put(RU_BRAND_LOGO_2,"");

        String imageUrl = recentLocalisationManager.getMessage(keys, true, payload);

        String[] bannerIdKey = new String[]{Constants.LANGUAGE_ENGLISH, RECO,RU, RU_BANNER_KEY_2};
        setThinBannerId(bannerIdKey,thinBannerResponse);

        String gaCategory = getGaCategory(product1, config1, null,null) + " " + "&" + " " + getGaCategory(product2, config2, null,null);

        thinBannerResponse.setGaCategory(gaCategory);
        thinBannerResponse.setUrl(getMyBillsUrl());
        thinBannerResponse.setOperatorLogo(imageUrl);
        thinBannerResponse.setAltImageUrl(thinBannerResponse.getOperatorLogo());
    }

    private String getLabel(HashMap<String, Object> map) {

        Product product1 = homeReminderResponseHandlerUtil.fetchProductDetails(String.valueOf(map.get(RECENT_PRODUCT_ID)));

        String[] heading1Key = new String[]{LANGUAGE_ENGLISH, RECO,"ru","heading",product1.getServiceKey(),product1.getPayTypeKey()};

        DecimalFormat format = new DecimalFormat("#,##,###.##");
        ArrayList<HashMap<String, Object>> billist = (ArrayList<HashMap<String, Object>>) map.get("bills");
        HashMap<String, Object> bill = billist.get(0);
        String dueDateString = null;
        if (Objects.nonNull(bill) && (Objects.nonNull(bill.get("due_date")) || Objects.nonNull(bill.get("expiry")))) {
            dueDateString = getFormattedDueDateForThinBanner(bill);
        }

        Map<String, String> heading1Payload = new HashMap<>();

        if (map.get("amount") != null) {
            heading1Payload.put("dueAmount", format.format(map.get("amount")));
            return recentLocalisationManager.getMessage(heading1Key, true, heading1Payload);
        }
        else
            return String.format(THIN_BANNER_DATEFORMAT, dueDateString);

    }

    private void getThinBannerV3(List<HashMap<String, Object>> frequentOrders, List<HomeReminderResponse> homeReminderResponses, HomeReminderResponse thinBannerResponse) throws UnsupportedEncodingException {
        Product product1 = homeReminderResponseHandlerUtil.fetchProductDetails(String.valueOf(frequentOrders.get(1).get(RECENT_PRODUCT_ID)));
        Product product2 = homeReminderResponseHandlerUtil.fetchProductDetails(String.valueOf(frequentOrders.get(2).get(RECENT_PRODUCT_ID)));
        
        HashMap<String, Object> config1 = getConfigFromFrequentOrder(frequentOrders.get(1));
        HashMap<String, Object> config2 = getConfigFromFrequentOrder(frequentOrders.get(2));

        String[] keys = new String[]{LANGUAGE_ENGLISH, RECO,"ru","image_url_3"};

        String title1 = product1.getProductAttributes().getMyBillsHeading();
        if(Objects.nonNull(title1) && title1.length()>RU_THIN_BANNER_2_LIMIT)
            title1 = title1.substring(0,RU_THIN_BANNER_2_LIMIT-3)+"...";

        String title2 = product2.getProductAttributes().getMyBillsHeading();
        if(Objects.nonNull(title2) && title2.length()>RU_THIN_BANNER_2_LIMIT)
            title2 = title2.substring(0,RU_THIN_BANNER_2_LIMIT-3)+"...";

        Map<String, String> payload = new HashMap<>();
        payload.put(RU_RECO_TITLE_1,encodedPayload(title1));
        payload.put(HEADING1, encodedPayload(getLabel(frequentOrders.get(1))));
        if(Objects.nonNull(product1.getProductAttributes().getDynamicBannerLogoId()))
            payload.put(RU_BRAND_LOGO_1,product1.getProductAttributes().getDynamicBannerLogoId());
        else payload.put(RU_BRAND_LOGO_1,"");
        payload.put(RU_RECO_TITLE_2,encodedPayload(title2));
        payload.put(HEADING2, encodedPayload(getLabel(frequentOrders.get(2))));
        if(Objects.nonNull(product2.getProductAttributes().getDynamicBannerLogoId()))
            payload.put(RU_BRAND_LOGO_2,product2.getProductAttributes().getDynamicBannerLogoId());
        else payload.put(RU_BRAND_LOGO_2,"");
        payload.put(RU_RECO_CTA, encodedPayload("Bills Due"));

        Integer increase = homeReminderResponses.size()-2;
        payload.put(INCREASE,increase.toString());

        String imageUrl = recentLocalisationManager.getMessage(keys, true, payload);

        String[] bannerIdKey = new String[]{Constants.LANGUAGE_ENGLISH, RECO,RU, RU_BANNER_KEY_3};
        setThinBannerId(bannerIdKey,thinBannerResponse);

        String gaCategory = getGaCategory(product1, config1, null,null) + " " + "&" + " " + getGaCategory(product2, config2, null,null);
        thinBannerResponse.setGaCategory(gaCategory);
        thinBannerResponse.setUrl(getMyBillsUrl());
        thinBannerResponse.setOperatorLogo(imageUrl);
        thinBannerResponse.setAltImageUrl(thinBannerResponse.getOperatorLogo());
    }
    private void setThinBannerId(String[] bannerIdKey, HomeReminderResponse thinBannerResponse){
        Map<String, String> payload1 = new HashMap<>();
        String bannerId = recentLocalisationManager.getMessage(bannerIdKey, true, payload1);

        if (!org.apache.commons.lang3.StringUtils.isEmpty(bannerId)) {
            thinBannerResponse.setBannerId(bannerId);
            try {
                thinBannerResponse.setId(Long.parseLong(thinBannerResponse.getBannerId()));
            } catch (Exception e) {
                log.error("error while creating home reminder response id ", e);
            }
        }
    }
    private String getMyBillsUrl(){
        String[] billsKeys = new String[]{Constants.LANGUAGE_ENGLISH, RECO, RU, MY_BILLS_URL};
        String[] billsPayload = new String[]{Constants.LANGUAGE_ENGLISH, RECO, RU, MY_BILLS_PAYLOAD};

        Map<String, String> payload = new HashMap<>();

        String deeplinkPayload = recentLocalisationManager.getMessage(billsPayload, true, payload);
        String encodedBytes = Base64.getEncoder().encodeToString(deeplinkPayload.getBytes());

        Map<String, String> encodedPayload = new HashMap<>();
        encodedPayload.put("base64data", encodedBytes);
        String locMessage = recentLocalisationManager.getMessage(billsKeys, true, encodedPayload);
        String url = "";
        if (locMessage != null)
            url = locMessage;
        return url;
    }

    private void saveThinBannerResponseInCache(Key key, Bin bin) {
        try {
            WritePolicy policy = new WritePolicy(aerospikeClient.writePolicyDefault);
            policy.expiration = getThinBannerExpirationTime();
            aerospikeClient.put(policy, key, bin);
        } catch (Exception ex) {
            log.error("Aerospike is down/not responding while saving for key {} exception {}",key, ex);
            metricsAgent.incrementEventCount("hr_error_in_save_cache");
        }
    }
    private HomeReminderResponse checkIfResponseExistsInCache(Key key){
        HomeReminderResponse reminderResponse = null;
        try {
            Record record = aerospikeClient.get(null, key);
            if (Objects.nonNull(record)) {
                Map<String, Object> map = record.bins;
                if (Objects.nonNull(map)) {
                    if (Objects.isNull(map.get(RU_RECO)) || AerospikeUtils.getDeserializedData(map.get(RU_RECO)).equals("")){
                        return reminderResponse;
                    }
                    else{
                        reminderResponse = (HomeReminderResponse) AerospikeUtils.getDeserializedData(map.get(RU_RECO));
                        log.info("RU thin banner found in cache");
                    }
                    metricsAgent.incrementEventCount("ru_reco_found_in_cache");
                } else {
                    log.info("ru thin banner not found in cache");
                    metricsAgent.incrementEventCount("ru_reco_not_found_in_cache");
                }
            } else {
                metricsAgent.incrementEventCount("ru_reco_not_found_in_cache");
            }
        } catch (Exception ex) {
            log.error("Aerospike is down/not responding while get for key {} and exception {}",key,ex);
            metricsAgent.incrementEventCount("ru_reco_error_in_get_cache");
        }
        return reminderResponse;
    }
    private Boolean isValidForRuReco(Long recoViewId, Long bannerViewId, Map<Long, String> overrideViewIdToWidgetTypeMap){
        if (recoViewId == null && bannerViewId == null){
            metricsAgent.incrementEventCount(RU_RECO_INVALID_CRED);
            return false;
        }else if(recoViewId == null || bannerViewId == null || overrideViewIdToWidgetTypeMap == null || overrideViewIdToWidgetTypeMap.isEmpty()){
            metricsAgent.incrementEventCount(RU_RECO_INVALID_CRED);
            return true;
        }
        return true;
    }

    private Boolean isValidForRuReco(Set<Long> recoViewIds, Long bannerViewId, Map<Long, String> overrideViewIdToWidgetTypeMap){
        if ((recoViewIds == null || recoViewIds.isEmpty()) && bannerViewId == null){
            metricsAgent.incrementEventCount(RU_RECO_INVALID_CRED);
            return false;
        }else if(recoViewIds == null || recoViewIds.isEmpty() || bannerViewId == null || overrideViewIdToWidgetTypeMap == null || overrideViewIdToWidgetTypeMap.isEmpty()){
            metricsAgent.incrementEventCount(RU_RECO_INVALID_CRED);
            return true;
        }
        return true;
    }

    private Boolean isValidForRuRecoV2(Set<Long> recoViewIds, Long bannerViewId, Map<Long, String> overrideViewIdToWidgetTypeMap){
        return (recoViewIds != null && !CollectionUtils.isEmpty(recoViewIds)) || (overrideViewIdToWidgetTypeMap != null && !CollectionUtils.isEmpty(overrideViewIdToWidgetTypeMap));
    }

    private String encodedPayload(String payload){
        if(payload == null)
            return "";
        try{
            payload = URLEncoder.encode(payload, StandardCharsets.UTF_8.toString());
        }catch (Exception e){
            log.error("Error while encoding");
        }
        return payload;
    }
    private Map<String, Integer> createCatIdCountMap(List<SagaFavResponsePOJO> sagaFavResponsePOJOList){
        log.info("[FavouiteManagerImpl.createCatIdCountMap] :: function starting");
        List<String> priorityOrder = ServiceConfigCache.getInstance().getRedDotPriorityMap();
        Map<String, Integer> catIdToCountMap = new HashMap<>();
        HashMap<String, Integer> categoryMap = new HashMap<>();
        Product product;
        for (SagaFavResponsePOJO sagaFavResponsePOJO : sagaFavResponsePOJOList) {
            if(catIdCountMapCondition(sagaFavResponsePOJO)){
                product = homeReminderResponseHandlerUtil.fetchProductDetails(String.valueOf(sagaFavResponsePOJO.getPid()));

                String categoryId = String.valueOf(product.getCategoryId());
                categoryMap.put(categoryId, categoryMap.getOrDefault(categoryId, 0) + 1);
            }
        }
        Integer limit = ServiceConfigCache.getInstance().getInteger(HP_CATEGORY_LIMIT);
        if (limit == null)
            limit = 3;
        for (String priority : priorityOrder) {
            if (catIdToCountMap.size() == limit)
                break;
            if (categoryMap.containsKey(priority)) {
                catIdToCountMap.put(priority,categoryMap.get(priority));
            }
        }
        return catIdToCountMap;
    }

    private boolean catIdCountMapCondition(SagaFavResponsePOJO sagaFavResponsePOJO){
        if(Objects.nonNull(CVRProductCache.getInstance().getProductDetails(Long.valueOf(sagaFavResponsePOJO.getPid()))) &&
                RecentUtils.getPartialBillEnabledCategories().contains(CVRProductCache.getInstance().getProductDetails(Long.valueOf(sagaFavResponsePOJO.getPid())).getCategoryId().toString()) &&
                (sagaFavResponsePOJO.getEventState().equals(EventState.SMS_CARD.toString()) || sagaFavResponsePOJO.getEventState().equals(EventState.SMS_CARD_NO_AMOUNT.toString())) &&
                Objects.nonNull(sagaFavResponsePOJO.getBill()) && sagaFavResponsePOJO.getBill().getIsBillDue()){
            return true;
        }
        return Objects.nonNull(sagaFavResponsePOJO.getBill()) && Objects.nonNull(sagaFavResponsePOJO.getBill().getBillDueDate());
    }

    public OverrideResponse getPersonalisedIconForHomeReminder(Long customerId, Long overrideViewId, List<SagaFavResponsePOJO> sagaFavResponses, List<SagaRecentResponse> sagaRecentResponses, Boolean isUserAgent)
            throws IOException, ParseException, ExecutionException, InterruptedException {
        log.info("[FavouiteManagerImpl.getPersonalisedIconForHomeReminder] :: function starting");
        Map<Long, String> overrideViewIdToWidgetTypeMap = new HashMap<>();
        overrideViewIdToWidgetTypeMap.put(overrideViewId, RU_OVERIDE_WIDGET);
        Map<String,String> allRequestParam = new HashMap<>();
        return getPersonalisedIconForHomeReminder(customerId, overrideViewIdToWidgetTypeMap, sagaFavResponses, sagaRecentResponses, isUserAgent, allRequestParam);
    }
    public OverrideResponse getPersonalisedIconForHomeReminder(Long customerId, Map<Long, String> overrideViewIdToWidgetTypeMap, List<SagaFavResponsePOJO> sagaFavResponses, List<SagaRecentResponse> sagaRecentResponses, Boolean isUserAgent, Map<String,String> allRequestParam)
            throws IOException, ParseException {

        Boolean enablePersonalisation = ServiceConfigCache.getInstance().getBoolean(ENABLE_PERSONALISATION);
        if(enablePersonalisation == null)
            enablePersonalisation = false;

        Map<String,Integer> serviceToCountMap = getServiceToCountMap(sagaFavResponses);

        Map<String, List<String>> enabledOperatorsWiseIconMap = JsonUtil.convertObjectToMap(ServiceConfigCache.getInstance().get(Constants.OPERATOR_ICON_ENABLED_MAP));
        Map<String, String> billDueServiceOperatorMap = getBillDueServiceOperatorMap(sagaFavResponses, serviceToCountMap, enabledOperatorsWiseIconMap);


        Set<Long> whiteListedCustIds = JsonUtil.converObjectToSet(ServiceConfigCache.getInstance().get(OR_CUSTOMER_IDS));

        Map<Integer,String > customerIdToCohortMap = ServiceConfigCache.getInstance().getCustomerIdToCohortMap();
        String cohortName = null;

        Integer totalBillsDue=0;
        for(Integer value: serviceToCountMap.values()){
            totalBillsDue = totalBillsDue +value;
        }

        if(Boolean.TRUE.equals(enablePersonalisation)){
            Set<Integer> orAllowedCustIds = ServiceConfigCache.getInstance().getSet(OR_ALLOWED_CUST_IDS);
            if (orAllowedCustIds != null && !orAllowedCustIds.contains((int)(customerId % 100)) && !whiteListedCustIds.contains(customerId)) {
                return checkForMyBillsTag(overrideViewIdToWidgetTypeMap,totalBillsDue,customerId,allRequestParam);
            }
        }
        else {
            if(!whiteListedCustIds.contains(customerId)) {
                return checkForMyBillsTag(overrideViewIdToWidgetTypeMap,totalBillsDue,customerId,allRequestParam);
            }
        }

		OverrideResponse overrideResponse = null;
		String version = null;
        Set<Long> isCachedViewIdsSet = new HashSet<>();

		try {
            Record record = agentAeroSpikeClientWrapper.get(customerId);
			if (Objects.nonNull(record)) {
				// log.info("Record already exisists record is -- "+record.bins);
				Map<String, Object> map = record.bins;
				if (Objects.nonNull(map) && !AerospikeUtils.getDeserializedData(map.get(OVERRIDE_RESPONSE)).equals("") && !AerospikeUtils.getDeserializedData(map.get(OVERRIDE_VERSION)).equals("")) {
					version = (String) AerospikeUtils.getDeserializedData(map.get(OVERRIDE_VERSION));
					overrideResponse = (OverrideResponse) AerospikeUtils.getDeserializedData(map.get(OVERRIDE_RESPONSE));

                    if(overrideResponse!=null && overrideResponse.getAdUnitNotifs()!=null){
                        for(Long overrideViewId : overrideViewIdToWidgetTypeMap.keySet()) {
                            if(overrideResponse.getAdUnitNotifs().get(OVERRIDE_PREFIX+overrideViewId)!=null) {
                                isCachedViewIdsSet.add(overrideViewId);
                            }
                        }
                    }
					metricsAgent.incrementEventCount("overrideResponse_found_in_cache");
				} else {
					metricsAgent.incrementEventCount("overrideResponse_not_found_in_cache");
				}
			} else {
				metricsAgent.incrementEventCount("overrideResponse_not_found_in_cache");
			}
		} catch (Exception ex) {
			log.error("Aerospike is down/not responding ", ex);
			metricsAgent.incrementEventCount("overrideResponse_error_in_get_cache");
		}


        Map<Long, PersonalisationResponse> viewIdToPersonalisationResponseMap = new HashMap<>();
        PersonalisationRequest personalisationRequest = new PersonalisationRequest(customerId, sagaRecentResponses, isUserAgent);
        processOverrideViewIdToWidgetTypeMap(overrideViewIdToWidgetTypeMap, viewIdToPersonalisationResponseMap, personalisationRequest, isCachedViewIdsSet, version, overrideResponse, serviceToCountMap);


        if(!CollectionUtils.isEmpty(customerIdToCohortMap) && customerIdToCohortMap.containsKey((int)(customerId % 100))){
            cohortName = customerIdToCohortMap.get((int)(customerId % 100));
        }

        if(!viewIdToPersonalisationResponseMap.isEmpty()) {

            OverrideResponseParams overrideResponseParams = new OverrideResponseParams(viewIdToPersonalisationResponseMap, overrideViewIdToWidgetTypeMap, serviceToCountMap, billDueServiceOperatorMap, totalBillsDue, cohortName);
            overrideResponse = personalisationManager.createPersonalisedResponse(overrideResponse, overrideResponseParams, allRequestParam);

            Optional<PersonalisationResponse> firstViewPersonalisationResponseOptional = viewIdToPersonalisationResponseMap.values().stream().findFirst();
            if(firstViewPersonalisationResponseOptional.isPresent() && !firstViewPersonalisationResponseOptional.get().isBottomNav()) {
                cachePersonalisedIconResponse(overrideResponse, firstViewPersonalisationResponseOptional.get(), customerId);
            }
        }


        OverrideResponse finalOverrideResponse =  null;
        if(overrideResponse == null){
            finalOverrideResponse = new OverrideResponse();
            finalOverrideResponse.setStatus(OVERRIDE_RESPONSE_FAILURE);
            finalOverrideResponse.setError(OVERRIDE_ERROR_MSG);
        }
        else {
            //filter requested override viewId from cached response.
            finalOverrideResponse = createOverrideResponseForRequestedViewId(overrideResponse, overrideViewIdToWidgetTypeMap);

            boolean isAnyOverrideResponseFailed = false;
            for(Long overrideViewId : overrideViewIdToWidgetTypeMap.keySet()) {
                if (finalOverrideResponse.getAdUnitNotifs().get(OVERRIDE_PREFIX + overrideViewId).size() != 8) {
                    log.info("incorrect number of categories found for customerId {} and viewId {}",customerId, overrideViewId);
                    isAnyOverrideResponseFailed = true;
                    break;
                }
            }
            if (isAnyOverrideResponseFailed) {
                finalOverrideResponse.setError(OVERRIDE_ERROR_MSG);
                finalOverrideResponse.setStatus(OVERRIDE_RESPONSE_FAILURE);
            } else {
                finalOverrideResponse.setStatus(OVERRIDE_RESPONSE_SUCCESS);
                finalOverrideResponse.setError("");
            }

        }

        return finalOverrideResponse;
    }

    public void processOverrideViewIdToWidgetTypeMap(Map<Long, String> overrideViewIdToWidgetTypeMap, Map<Long, PersonalisationResponse> viewIdToPersonalisationResponseMap, PersonalisationRequest personalisationRequest, Set<Long> isCachedViewIdsSet, String version, OverrideResponse overrideResponse, Map<String, Integer> serviceToCountMap) {
        Set<Long> bottomNavViewIds = JsonUtil.converObjectToSet(ServiceConfigCache.getInstance().get(NEW_BOTTOM_NAV_VIEW_IDS));

        for (Long viewId : overrideViewIdToWidgetTypeMap.keySet()) {
            if(!isCachedViewIdsSet.contains(viewId) || !org.apache.commons.lang3.StringUtils.equalsIgnoreCase(version , getRuleVersion()) || overrideResponse == null) {

                List<String> segMetrics = new ArrayList<>();
                segMetrics.add(VIEW_ID + viewId);
                metricsAgent.incrementEventCount(HOMEPAGE_PERSONALISE_VIEW_IDS, segMetrics);

                personalisationRequest.setViewId(viewId);
                personalisationRequest.setWidgetType(overrideViewIdToWidgetTypeMap.get(viewId));

                PersonalisationResponse personalisationResponse = null;
                try {

                    if (bottomNavViewIds.contains(viewId)) {
                        personalisationRequest.setHomePageServices(getHomePageServices(overrideResponse));
                        personalisationRequest.setRedDotServices(getRedDotServices(serviceToCountMap));
                        log.debug("personalisation request is {}", personalisationRequest);
                        personalisationResponse = ruleServiceClient.getBottomNavPersonalisation(personalisationRequest, null, null);
                        personalisationResponse.setBottomNav(true);
                    } else {
                        personalisationResponse = ruleServiceClient.getPersonalisation(personalisationRequest, null, null);
                    }
                    if(personalisationResponse != null && !CollectionUtils.isEmpty(personalisationResponse.getServices())) {
                        viewIdToPersonalisationResponseMap.put(viewId, personalisationResponse);
                    }
                }
                catch (Exception e){
                    log.error("[FavouriteManagerImpl.getPersonalisedIconForHomeReminder] Getting error while fetching data from rulesProcessingService for cust id {} viewId {} and exception {}",personalisationRequest.getCustomerId(), viewId, e);
                }
            }

        }

    }

    private List<String> getHomePageServices(OverrideResponse overrideResponse) {
        List<String> homePageServices = new ArrayList<>();
        if (overrideResponse == null)
            return homePageServices;
        Set<Long> homePageViewIds = JsonUtil.converObjectToSet(ServiceConfigCache.getInstance().get(HOME_PAGE_VIEW_IDS));
        Map<String, String> catIdToServiceMap = convertServiceToCatIdMap();



        for (Long overrideViewId : homePageViewIds) {
            List<OverrideResponseObject> overrideHomePageServices = overrideResponse.getAdUnitNotifs().get(OVERRIDE_PREFIX + overrideViewId);
            if (overrideHomePageServices != null) {
                processOverrideHomePageServices(overrideHomePageServices, catIdToServiceMap, homePageServices);
            }
        }
        return homePageServices;
    }

    private Map<String, String> convertServiceToCatIdMap() {
        Map<String, String> serviceToCatIdMap = JsonUtil.convertObjectToMap(ServiceConfigCache.getInstance().get(SERVICE_CAT_ID_MAP));
        Map<String, String> catIdToServiceMap = new HashMap<>();

        for (Map.Entry<String, String> entry : serviceToCatIdMap.entrySet()) {
            catIdToServiceMap.put(entry.getValue(), entry.getKey());
        }
        return catIdToServiceMap;
    }

    private void processOverrideHomePageServices(List<OverrideResponseObject> overrideHomePageServices, Map<String, String> catIdToServiceMap, List<String> homePageServices) {

        Integer count = ServiceConfigCache.getInstance().getInteger(HomePageConstants.HOMEPAGE_SERVICE_EXCLUDE_COUNT);
        if (count == null)
            count = 4;

        for (OverrideResponseObject overrideResponseObject : overrideHomePageServices) {
            if (overrideResponseObject.getCombineKeys() != null && overrideResponseObject.getCombineKeys().getRuCategoryId() != null) {
                String service = catIdToServiceMap.get(overrideResponseObject.getCombineKeys().getRuCategoryId());
                if (service != null && !homePageServices.contains(service) && homePageServices.size() < count) {
                    homePageServices.add(service);
                }
            }
        }
    }
    private List<String> getRedDotServices(Map<String, Integer> serviceToCountMap) {
        List<String> redDotServices = new ArrayList<>();
        if (!CollectionUtils.isEmpty(serviceToCountMap)) {
            for (Map.Entry<String, Integer> entry : serviceToCountMap.entrySet()) {
                if (entry.getValue() > 0) {
                    redDotServices.add(entry.getKey());
                }
            }
        }
        return redDotServices;
    }

    private OverrideResponse createOverrideResponseForRequestedViewId(OverrideResponse cachedOverrideResponse, Map<Long, String> overrideViewIdToWidgetTypeMap) {
        if(cachedOverrideResponse == null || cachedOverrideResponse.getAdUnitNotifs() == null
                || cachedOverrideResponse.getAdUnitNotifs().isEmpty()) {
            return cachedOverrideResponse;
        }
        OverrideResponse overrideResponse = new OverrideResponse();
        overrideResponse.setError(cachedOverrideResponse.getError());
        overrideResponse.setStatus(cachedOverrideResponse.getStatus());
        overrideResponse.setReminderNotifs(cachedOverrideResponse.getReminderNotifs());
        overrideResponse.setViewIdConfig(cachedOverrideResponse.getViewIdConfig());
        Map<String, List<OverrideResponseObject>> adUnitNotifs = cachedOverrideResponse.getAdUnitNotifs();
        Map<String, List<OverrideResponseObject>> filteredAdUnitNotifs = new HashMap<>();

        for(Long overrideViewId : overrideViewIdToWidgetTypeMap.keySet()) {
            String overrideViewIdKey = OVERRIDE_PREFIX + overrideViewId;
            if(adUnitNotifs.containsKey(overrideViewIdKey)) {
                filteredAdUnitNotifs.put(overrideViewIdKey, adUnitNotifs.get(overrideViewIdKey));
            }
        }
        overrideResponse.setAdUnitNotifs(filteredAdUnitNotifs);
        return overrideResponse;
    }

    private void cachePersonalisedIconResponse(OverrideResponse overrideResponse, PersonalisationResponse personalisationResponse, Long customerId) {
        log.info("[FavouriteManagerImpl.cachePersonalisedIconResponse] :: function starting");
        if (overrideResponse != null && personalisationResponse != null) {
            Bin responseBin = AerospikeUtils.getSerialiazedBin(OVERRIDE_RESPONSE, overrideResponse);
            Bin versionBin = AerospikeUtils.getSerialiazedBin(OVERRIDE_VERSION, personalisationResponse.getVersion());
            try {
                Key key = new Key(SMARTREMINDER, null, OR_KEY.concat(String.valueOf(customerId)));
                WritePolicy policy = new WritePolicy(aerospikeClient.writePolicyDefault);
                policy.expiration = personalisationResponse.getTtl();
                aerospikeClient.put(policy, key, responseBin, versionBin);
            } catch (Exception ex) {
                log.error("Aerospike is down/not responding  ex {} ttl {}", ex, personalisationResponse.getTtl());
                metricsAgent.incrementEventCount("override_error_in_save_cache");
            }
        }
    }
    private Map<String,Integer> getServiceToCountMap(List<SagaFavResponsePOJO> sagaRecentResponses){
        Map<String,Integer> serviceToCountMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(sagaRecentResponses)) {
            for (SagaFavResponsePOJO sagaRecentResponse : sagaRecentResponses) {
                if(catIdCountMapCondition(sagaRecentResponse)){
                    Product product = homeReminderResponseHandlerUtil.fetchProductDetails(String.valueOf(sagaRecentResponse.getPid()));
                    if ("mobile".equalsIgnoreCase(product.getServiceKey()))
                        serviceToCountMap.put(product.getServiceKey() + " " + product.getPayTypeKey(), serviceToCountMap.getOrDefault(product.getServiceKey() + " " + product.getPayTypeKey(), 0) + 1);
                    else
                        serviceToCountMap.put(product.getServiceKey(), serviceToCountMap.getOrDefault(product.getServiceKey(), 0) + 1);

                }
            }
        }
        return serviceToCountMap;
    }

    private Map <String, String> getBillDueServiceOperatorMap(List<SagaFavResponsePOJO> sagaRecentResponses, Map<String, Integer> serviceToCountMap, Map<String, List<String>> enableOperatorsWiseIconMap) {
        Map<String, String> serviceToOperatorMap = new HashMap<>();
        
        if (!CollectionUtils.isEmpty(sagaRecentResponses)) {
            for (SagaFavResponsePOJO sagaRecentResponse : sagaRecentResponses) {
                if (catIdCountMapCondition(sagaRecentResponse)) {
                    Product product = homeReminderResponseHandlerUtil.fetchProductDetails(String.valueOf(sagaRecentResponse.getPid()));
                    // log.info("getBillDueServiceOperatorMap :: product :: " + product);
                    String serviceKey = product.getServiceKey();
                    String operator = product.getOperator();
                    // Check if the service has a count of 1 in serviceToCountMap
                    if (serviceToCountMap.containsKey(serviceKey) && serviceToCountMap.get(serviceKey) == 1) {
                        // Check if the service is present in enableOperatorsWiseIconMap and if the operator is in its list
                        if (enableOperatorsWiseIconMap.containsKey(serviceKey) &&
                            enableOperatorsWiseIconMap.get(serviceKey).contains(operator)) {
                            serviceToOperatorMap.put(serviceKey, operator);
                        }
                    }
                }
            }
        }
        return serviceToOperatorMap;
    }

    private String getRuleVersion() {
        Map<String, Object> rulesConfigJson = JsonUtil.convertObjectToMap(ServiceConfigCache.getInstance().get(ServiceConfigConstants.RULES_CONFIG));
        if (rulesConfigJson != null && rulesConfigJson.containsKey(VERSION))
            return rulesConfigJson.get(VERSION).toString();
        else return null;
    }


    private Boolean excludeServicesFromComboReminder(Long recoViewId, String pid){
        Product product = new Product();
        List<String> servicesToBeExcluded=new ArrayList<>();
        String service = null;
        Map<String,Object> viewIdServiceConfig = JsonUtil.convertObjectToMap(ServiceConfigCache.getInstance().get("viewIdServicesToExclude"));
        if(pid!=null)
            product = homeReminderResponseHandlerUtil.fetchProductDetails(pid);
        if(product!=null && product.getServiceKey()!=null)
            service = product.getServiceKey().toLowerCase().replaceAll(" ","");
        if(viewIdServiceConfig==null || (Objects.nonNull(recoViewId)&&!viewIdServiceConfig.containsKey(recoViewId.toString())))
            return false;
        else {
            if(Objects.nonNull(recoViewId))
                servicesToBeExcluded = JsonUtil.convertObjectToList(viewIdServiceConfig.get(recoViewId.toString()));
        }
        return service!=null  && servicesToBeExcluded.contains(service);
    }
    private String getOperatorLogoUrlFromLocalisation(HashMap<String, Object> frequentOrder) {
        if (frequentOrder.containsKey(RECENT_PRODUCT_ID)) {
            Product product = homeReminderResponseHandlerUtil.fetchProductDetails(String.valueOf(frequentOrder.get(RECENT_PRODUCT_ID)));
            String[] locKeys = new String[]{LANGUAGE_ENGLISH, RECO, "ru", "dynamic_operator_logo_url",Utils.toLowerCaseAndRemoveSpace(product.getService()),Utils.toLowerCaseAndRemoveSpace(product.getOperator()),Utils.toLowerCaseAndRemoveSpace(product.getPayType())};
            Map<String, String> payload = new HashMap<>();
            return recentLocalisationManager.getMessage(locKeys, true, payload);
        }
        return null;
    }

    private OverrideResponse checkForMyBillsTag(Map<Long, String> overrideViewIdToWidgetTypeMap, Integer totalBillsDue, Long customerId, Map<String, String> allRequestParam) {
        if (Boolean.TRUE.equals(ServiceConfigCache.getInstance().getBoolean(MY_BILLS_TAB_ENABLE))) {
            Map<String, FeatureConfig> featureConfigs = FeatureConfigCache.getInstance().get(FeatureConfigName.ruMyBillsHeader.name());
            if (featureConfigs != null) {
                try {
                    FeatureConfig featureConfig = featureConfigs.get(FeatureConfigKey.all.name());
                    Map<String, String> myBillsConfigMap = featureConfig.getMyBillsAnimationConfigMap();
                    if (Boolean.TRUE.equals(rechargeHelperService.isLiveOnVersion(allRequestParam.get(VERSION), myBillsConfigMap.get(allRequestParam.get(CLIENT))))) {
                        Map<String, List<OverrideResponseObject>> responseMap = new HashMap<>();
                        for (Long overrideViewId : overrideViewIdToWidgetTypeMap.keySet()) {
                            List<OverrideResponseObject> overrideResponseObjects = new ArrayList<>();
                            overrideResponseObjects.add(personalisationManager.addMyBillsPropertiesIfApplicable(totalBillsDue, overrideViewIdToWidgetTypeMap.get(overrideViewId)));
                            responseMap.put(OVERRIDE_PREFIX + overrideViewId, overrideResponseObjects);
                        }
                        OverrideResponse overrideResponse = new OverrideResponse();
                        overrideResponse.setAdUnitNotifs(responseMap);
                        return overrideResponse;
                    }
                }catch (Exception e){
                    log.error("[FavouriteManagerImpl.checkForMyBillsTag] :: Failed while creating my bills tag response with exception {}",e);
                }
            }
        }
        log.info("Returning null override when flag is true for customer id {}", customerId);
        return null;
    }

    public boolean isNonTransactingUser(HashMap<String, Object> frequentOrder){
        Boolean nonTransactionUsers = ServiceConfigCache.getInstance().getBoolean("enableNonTxnForYellowTagFeature");
        if(Boolean.TRUE.equals(nonTransactionUsers) && frequentOrder.get("txnDate")==null){
            log.info("Non transaction user found");
            return true;
        }
        return false;
    }

    public void pushMyBillsAndClpLogs(ArrayList<HashMap<String, Object>> frequentOrdersResponse, String sourceApi, Long customerId) {
        try {
            LocalDate currentDate = LocalDate.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String formattedDate = currentDate.format(formatter);
            for (HashMap<String, Object> frequentOrder : frequentOrdersResponse) {
                MyBillsClpLogObj myBillsClpLogObj = new MyBillsClpLogObj();
                myBillsClpLogObj.setCustomerId(customerId);
                Map<String, Object> additionalInfoMap = (Map<String, Object>) frequentOrder.get("additionalInfo");

                if (additionalInfoMap != null && ObjectUtils.isNotEmpty(additionalInfoMap.get(RECON_ID))) {
                    String reconId = (String) additionalInfoMap.get(RECON_ID);
                    myBillsClpLogObj.setReconId(reconId);
                }
                myBillsClpLogObj.setPublishedTime(new Date().getTime());
                myBillsClpLogObj.setPublishedDate(formattedDate);
                if (frequentOrder.containsKey("bills") && Objects.nonNull(frequentOrder.get("bills"))) {
                    ArrayList<HashMap<String, Object>> billist;
                    billist = (ArrayList<HashMap<String, Object>>) frequentOrder.get("bills");
                    if (!billist.isEmpty()) {
                        HashMap<String, Object> bill = billist.get(0);
                        if (Objects.nonNull(bill) && (Objects.nonNull(bill.get("bill_date")))) {
                            myBillsClpLogObj.setBillDate(String.valueOf(bill.get("bill_date")));
                        }
                        if (Objects.nonNull(bill) && (Objects.nonNull(bill.get("due_date")))) {
                            myBillsClpLogObj.setDueDate(String.valueOf(bill.get("due_date")));
                        }
                        if (Objects.nonNull(bill) && Objects.nonNull(bill.get("amount"))) {
                            myBillsClpLogObj.setAmount(String.valueOf(bill.get("amount")));
                        }
                        if(Objects.nonNull(bill) && Objects.nonNull(bill.get("billFetchDate"))){
                            Date billDateTimeStamp = DateUtil.stringToDate(String.valueOf(bill.get("billFetchDate")), DateFormat.DATE_TIME_FORMAT_2);
                            if (Objects.nonNull(billDateTimeStamp)){
                                myBillsClpLogObj.setBillFetchDate(billDateTimeStamp.getTime());
                            }

                        }
                    }
                }
                Map<String, Object> productAttributes;
                Map<String, Object> productDetails;
                if (frequentOrder.containsKey(PRODUCT)) {
                    productDetails = (Map<String, Object>) frequentOrder.get(PRODUCT);
                    if (productDetails.containsKey((RECENT_ATTRIBUTES))) {
                        productAttributes = (Map<String, Object>) productDetails.get(RECENT_ATTRIBUTES);
                        if (productAttributes.containsKey("service"))
                            myBillsClpLogObj.setService((String) productAttributes.get("service"));
                        if (productAttributes.containsKey("paytype"))
                            myBillsClpLogObj.setPayType((String) productAttributes.get("paytype"));
                        if (productAttributes.containsKey("operator"))
                            myBillsClpLogObj.setOperator((String) productAttributes.get("operator"));

                    }

                }
                if (frequentOrder.containsKey("eventType")) {
                    myBillsClpLogObj.setEventSource((String) frequentOrder.get("eventType"));
                }
                if (frequentOrder.containsKey("billState")) {
                    myBillsClpLogObj.setBillType((String) frequentOrder.get("billState"));
                }
           /* if (frequentOrder.containsKey("type")) {
                myBillsClpLogObj.setType((String) frequentOrder.get("type"));
            }*/
                if (frequentOrder.containsKey("issuingBankCardVariant")) {
                    myBillsClpLogObj.setCardVariant((String) frequentOrder.get("issuingBankCardVariant"));
                }
                if (frequentOrder.containsKey("mediaAssets")) {
                    ArrayList<Object> mediaAssets = (ArrayList<Object>) frequentOrder.get("mediaAssets");
                    if(Objects.nonNull(mediaAssets)) {
                        for (Object mediaAssetObj : mediaAssets) {
                            JSONObject mediaAssetJson = new JSONObject(mediaAssetObj.toString());
                            Map<String, Object> mediaAssetMap = new HashMap<>();
                            if (mediaAssetJson.has("assetType")) {
                                myBillsClpLogObj.setAssetType(mediaAssetJson.getString("assetType"));
                            }
                            if (mediaAssetJson.has("source")) {
                                myBillsClpLogObj.setCardSource(mediaAssetJson.getString("source"));
                            }
                            // Extract the key "Map" if it exists
                            if (mediaAssetJson.has("profiles")) {
                                Map<String, String> profiles = new HashMap<>();
                                for (String key : mediaAssetJson.keySet()) {
                                    profiles.put(key, mediaAssetJson.getString(key));
                                }
                                if (profiles.containsKey("hdpi")) {
                                    myBillsClpLogObj.setHdpi(profiles.get("hdpi"));
                                }
                                // Process the map as needed
                            }
                        }
                    }
                }
                if (frequentOrder.containsKey("fastagExtraData")) {
                    Map<String, Object> cardInsurance = frequentOrder.get("fastagExtraData") != null ? (Map<String, Object>) frequentOrder.get("fastagExtraData") : new HashMap<>();
                    if(cardInsurance!=null){
                        if(cardInsurance.containsKey("variantId"))
                            myBillsClpLogObj.setVariantId(String.valueOf(cardInsurance.get("variantId")));
                        if(cardInsurance.containsKey("vehicleColor"))
                            myBillsClpLogObj.setVehicleColor(String.valueOf(cardInsurance.get("vehicleColor")));
                        if(cardInsurance.containsKey("make"))
                            myBillsClpLogObj.setMakeId( String.valueOf(cardInsurance.get("make")));
                        if(cardInsurance.containsKey("model"))
                            myBillsClpLogObj.setVehicleModel(String.valueOf(cardInsurance.get("model")));
                    }
                    myBillsClpLogObj.setCardInsurance(JsonUtil.toJson(cardInsurance));
                }

                if (frequentOrder.containsKey("created_desc")) {
                    myBillsClpLogObj.setMessageBody((String) frequentOrder.get("created_desc"));
                }
                /*if(frequentOrder.containsKey("oprBillGenDate") && Objects.nonNull(frequentOrder.get("oprBillGenDate"))){
                    Date billDateTimeStamp = DateUtil.stringToDate(String.valueOf(frequentOrder.get("oprBillGenDate")), DateFormat.DATE_TIME_FORMAT_2);
                    myBillsClpLogObj.setBillFetchDate(billDateTimeStamp.getTime());

                }*/
                myBillsClpLogObj.setSourceApi(sourceApi);
                myBillsClpLogger.info(myBillsClpLogObj);
            }
        } catch (Exception e){
            log.error("Error while pushing logs to MyBillsClpLogObj", e);
            metricsAgent.incrementEventCount(MY_BILLS_CLP_LOG_PUSH_ERROR);
        }

    }

    public void populateChangeUserPlansOperator(ArrayList<HashMap<String, Object>> frequentOrdersResponse, HashMap<String, Object> recent,MutableBoolean isUserPlanCheckedForMobile) {
        HashMap<String, Object> userPlansKeyValue = new HashMap<>();
        userPlansKeyValue.put("userplancronchanges", staticUserPlanOperatorUpdateService.getUserPlansPerOperatorChange());
        if (Objects.nonNull(recent)) {
            recent.putAll(userPlansKeyValue);
            isUserPlanCheckedForMobile.setValue(true);
        } else {
            if (!frequentOrdersResponse.isEmpty()) {
                HashMap<String, Object> existingHashMap = frequentOrdersResponse.get(0);
                existingHashMap.putAll(userPlansKeyValue);

            } else {
                frequentOrdersResponse.add(userPlansKeyValue);
            }
        }
    }

    private boolean isCustomerIdRolloutPercentage(Long customerId){
        int customerIdRolloutPercentage = ServiceConfigCache.getInstance().getInteger(ServiceConfigConstants.CUSTOMER_ID_ROLLOUT_PERCENTAGE);
        if(Objects.isNull(customerIdRolloutPercentage)){
            log.info("Customer id {} is in rollout percentage {}", customerId, customerIdRolloutPercentage);
            return true;
        }
        if (Objects.nonNull(customerId)) {
            int customerIdRollout = (int) (customerId % 100);
            if (customerIdRollout < customerIdRolloutPercentage) {
                log.info("Customer id {} is in rollout percentage {}", customerId, customerIdRolloutPercentage);
                return true;
            }
        }
        log.error("Customer id {} in not valid for rollout percentage ",customerId);
        return false;
    }
}
