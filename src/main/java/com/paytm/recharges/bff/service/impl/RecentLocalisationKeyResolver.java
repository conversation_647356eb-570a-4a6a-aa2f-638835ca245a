package com.paytm.recharges.bff.service.impl;

import com.paytm.recharges.bff.constants.Constants;
import com.paytm.recharges.bff.datalayer.dto.response.SagaRecentResponse;
import com.paytm.recharges.bff.datalayer.model.Product;
import com.paytm.recharges.bff.datalayer.model.RecentWrapper;
import com.paytm.recharges.bff.enums.Widget;
import com.paytm.recharges.bff.monitor.MetricsAgent;
import com.paytm.recharges.bff.service.RecentLocalisationManager;
import com.paytm.recharges.bff.utils.Utils;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Map;

import static com.paytm.recharges.bff.constants.CTAConstants.Constants.CTA_LOCALISATION_LANGUAGE;

@Service
public class RecentLocalisationKeyResolver {

    private static final CustomLogger log = CustomLogManager.getLogger(RecentLocalisationKeyResolver.class);

    @Autowired
    RecentLocalisationManager localisationManager;

    @Autowired
    MetricsAgent metricsAgent;

    public String getRecentLocalisationMessage(String language, String keyConstant, SagaRecentResponse sagaRecentResponse, Product product, Map<String, String> payload) {
        log.info("RecentLocalisationKeyResolver :: getRecentLocalisationMessage :: Parameters are {} , {}, {}", keyConstant, sagaRecentResponse, payload);
        return localisationManager.getMessage(Arrays.stream(new String[]{language, Constants.RECENTS_V2, keyConstant, Utils.toUpperCase(sagaRecentResponse.getEventState()), Utils.toLowerCase(sagaRecentResponse.getBillState()), Utils.toLowerCaseAndRemoveSpace(product.getPayType()), Utils.toLowerCaseAndRemoveSpace(product.getService()), Utils.toLowerCaseAndRemoveSpace(product.getOperator())}).filter(StringUtils::isNotEmpty).toArray(String[]::new), true, payload);
    }


    public String getRecentLocalisationMessage(String keyConstant, SagaRecentResponse sagaRecentResponse, Product product, Map<String, String> payload) {
        return localisationManager.getMessage(Arrays.stream(new String[]{CTA_LOCALISATION_LANGUAGE, Constants.RECENTS_V2, keyConstant, Utils.toUpperCase(sagaRecentResponse.getEventState()), Utils.toLowerCase(sagaRecentResponse.getBillState()), Utils.toLowerCaseAndRemoveSpace(product.getPayType()), Utils.toLowerCaseAndRemoveSpace(product.getService()), Utils.toLowerCaseAndRemoveSpace(product.getOperator())}).filter(StringUtils::isNotEmpty).toArray(String[]::new), true, payload);
    }

    public String getMessageWithoutEventStateInKey(String keyConstant, SagaRecentResponse sagaRecentResponse, Product product, Map<String, String> payload) {
        return localisationManager.getMessage(Arrays.stream(new String[]{CTA_LOCALISATION_LANGUAGE, Constants.RECENTS_V2, keyConstant, Utils.toLowerCase(sagaRecentResponse.getBillState()), Utils.toLowerCaseAndRemoveSpace(product.getPayType()), Utils.toLowerCaseAndRemoveSpace(product.getService()), Utils.toLowerCaseAndRemoveSpace(product.getOperator())}).filter(StringUtils::isNotEmpty).toArray(String[]::new), true, payload);
    }

    public String getLocalizationValue(RecentWrapper recentWrapper, String constant, String label) {

        String[] key = new String[]{recentWrapper.getLocale(), constant, label};
        String localizedMessage = localisationManager.getMessage(key, false, recentWrapper.getPayload());
        log.debug("Retrieved localized message for key {}: {}", key, localizedMessage);

        return localizedMessage;
    }


    public String getMessageByWidgetType(RecentWrapper wrapper, SagaRecentResponse sagaRecentResponse, Product product) {
        String keys[] = Arrays.stream(new String[]{wrapper.getLocale(), getConstantByType(wrapper.getWidget()), wrapper.getLocalisationKeyConstant(), Utils.toUpperCase(sagaRecentResponse.getEventState()), Utils.toLowerCase(sagaRecentResponse.getBillState()), Utils.toLowerCaseAndRemoveSpace(product.getPayType()), Utils.toLowerCaseAndRemoveSpace(product.getService()), Utils.toLowerCaseAndRemoveSpace(product.getOperator())}).filter(StringUtils::isNotEmpty).toArray(String[]::new);
        log.info("getMessageByWidgetType keys {}", Arrays.toString(keys));
        log.info("getMessageByWidgetType payload {}", wrapper.getPayload());
        String message = localisationManager.getMessage(keys, true, wrapper.getPayload());
        if (StringUtils.isEmpty(message)) {
            log.error("getMessageByWidgetType key not found {}", Arrays.toString(keys));
            return getRecentLocalisationMessage(wrapper.getLocale(), wrapper.getLocalisationKeyConstant(), sagaRecentResponse, product, wrapper.getPayload());
        }
        log.info("getMessageByWidgetType message {}", message);
        return message;
    }

    private String getConstantByType(Widget widget) {
        switch (widget) {
            case RECENT:
                return Constants.LocalisationKeyConstants.RECENTS;
            case CIR:
                return Constants.LocalisationKeyConstants.CIR;
        }

        return Constants.LocalisationKeyConstants.RECO;

    }

    public String getMessage(String[] keys, boolean b, Map<String, String> payload) {
        return localisationManager.getMessage(keys, b, payload);
    }
}
