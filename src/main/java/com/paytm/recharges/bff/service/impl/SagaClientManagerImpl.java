package com.paytm.recharges.bff.service.impl;

import com.paytm.recharges.bff.constants.ResponseCode;
import com.paytm.recharges.bff.monitor.MetricsAgent;
import com.paytm.recharges.bff.service.BaseClient;
import com.paytm.recharges.bff.service.PlanMappingService;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.recharges.saga_client.datalayer.dto.request.SagaFavRequest;
import com.paytm.recharges.saga_client.datalayer.dto.request.SagaNickNameRequest;
import com.paytm.recharges.saga_client.datalayer.dto.request.SagaRecentsRequest;
import com.paytm.recharges.saga_client.datalayer.dto.response.CarDetailColourResponseWrapper;
import com.paytm.recharges.saga_client.datalayer.dto.response.CarDetailResponseWrapper;
import com.paytm.recharges.saga_client.datalayer.dto.response.SagaFavResponsePOJOWrapper;
import com.paytm.recharges.saga_client.datalayer.dto.response.SagaFavResponseWrapper;
import com.paytm.recharges.saga_client.datalayer.dto.response.SagaLastTwoRecentsResponseWrapper;
import com.paytm.recharges.saga_client.datalayer.dto.response.SagaNickNameResponse;
import com.paytm.recharges.saga_client.service.SagaClient;
import jakarta.annotation.PostConstruct;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.paytm.recharges.bff.constants.Constants.SERVICE;


@Service
@Configuration
public class SagaClientManagerImpl extends BaseClient {

    private static final CustomLogger log = CustomLogManager.getLogger(SagaClientManagerImpl.class);

    @Value("${recharge.saga.dns:}")
    private String sagaFrequentOrdersUrl;


    private SagaClient sagaHRClient;

    private SagaClient sagaNickNameClient;

    private SagaClient sagaRecentClient;

    @Value("${saga.dns}")
    private String sagaDns;

    @Value("${saga.config.http.connect_timeout}")
    private String sagaTimeOut;

    @Autowired
    private MetricsAgent metricsAgent;

    @Autowired
    private PlanMappingService planMappingService;

    HashMap<String, String> requestor = new HashMap<>();

    @PostConstruct
    public void onStartup() {
        sagaRecentClient = new SagaClient(getConfig());
        sagaHRClient = new SagaClient(getConfig());
        sagaNickNameClient = configRecentClient();
    }

    private SagaClient configRecentClient() {
        HashMap<String, String> config = new HashMap<>();
        config.put("connectionTimeout", sagaTimeOut);
        config.put("sagaNicknameApiUrl", sagaDns);
        return new SagaClient(config);
    }


    public SagaNickNameResponse updateNickNameHelper(SagaNickNameRequest nickNameRequest, HashMap<String, String> allRequestParams) {
        SagaNickNameResponse nickNameResponse;

        try {
            nickNameResponse = sagaNickNameClient.updateNickname(nickNameRequest, allRequestParams);
        } catch (Exception E) {
            log.error("[RecentsManagerService.updateNickName] :: Exception thrown while updating nickname {}", E);
            JSONObject response = new JSONObject();
            response.put("Status code", ResponseCode.INTERNAl_SERVER_ERROR);
            response.put("Display Message", E);
            nickNameResponse = new SagaNickNameResponse(response);
        }
        return nickNameResponse;
    }


    public HashMap<String, String> getConfig() {
        HashMap<String, String> urls = new HashMap<>();
        urls.put("sagaFavApiUrl", sagaFrequentOrdersUrl);
        urls.put("sagaRecentApiUrl",sagaFrequentOrdersUrl);
        urls.put("connectionTimeout", "100");
        //urls.put("connectionTimeoutMs","50");
        urls.put("maxConnections", "4000");
        //urls.put("readTimeoutMs","80");
        log.trace("Saga fav resposne from saga client :: {}", urls);
        return urls;
    }

    public SagaFavResponseWrapper getFrequentOrdersFromSaga(Long customerId, HashMap<String, String> filters, HashMap<String, Object> allRequestParams) {
        SagaFavRequest sagaFavRequest = new SagaFavRequest();
        if (Objects.nonNull(filters) && Objects.nonNull(filters.get(SERVICE))) {
            try {
                ArrayList<String> list = Stream.of(filters.get(SERVICE).split(",")).map(e -> e.trim()).collect(Collectors.toCollection(ArrayList::new));
                sagaFavRequest.setServices(list);
            } catch (Exception ex) {
                log.error("Error parsing array list services ", ex);
            }

            if (filters.get(SERVICE).trim().isEmpty()) sagaFavRequest.setServices(new ArrayList<>());
        }

        try {
            log.info("[SagaClientManagerImpl.getFrequentOrdersFromSaga] :: going to hit saga client");
            SagaFavResponseWrapper sagaFavResponse = sagaHRClient.getFavOrdersV2(customerId, sagaFavRequest, allRequestParams);
            return sagaFavResponse;
        } catch (Exception e) {
            metricsAgent.incrementEventCount("saga_reco_api_exception");
            log.error("getFrequentOrdersFromSaga::Exception thrown  while getting  frequent orders for customer id " + customerId, e);
            return null;
        }

    }

    @Retryable(maxAttempts=3)
    public SagaFavResponsePOJOWrapper getRecentsFromSaga(Long customerId, HashMap<String, Object> allRequestParams, SagaFavRequest sagaFavRequest) {

        try {
            log.info("Hittiing saga = {}",sagaRecentClient);
            return sagaRecentClient.getFavOrdersV3(customerId, sagaFavRequest, allRequestParams);
        } catch (Exception e) {
            metricsAgent.incrementEventCount("fetch_recents_api_exception");
            log.error("getRecentsFromSaga::Exception thrown  while fetching last 2 recents for customer id " + customerId, e);
            return null;
        }

    }

    @Retryable(maxAttempts=3)
    public SagaLastTwoRecentsResponseWrapper getLastTwoRecentsFromSaga(Long customerId, HashMap<String, Object> allRequestParams, SagaRecentsRequest sagaRecentsRequest) {
        log.info("SagaLastTwoRecentsResponseWrapper:: inside the getLastTwoRecentsFromSaga {} {} {}",customerId,allRequestParams,sagaRecentsRequest);
        try {
            return sagaRecentClient.getRecentOrders(customerId, sagaRecentsRequest, allRequestParams);
        } catch (Exception e) {
            metricsAgent.incrementEventCount("saga_recents_v3_api_exception");
            log.error("getRecentsFromSaga::Exception thrown  while getting  frequent orders for customer id " + customerId, e);
            return null;
        }

    }

    @Retryable(maxAttempts=3)
    public CarDetailResponseWrapper getCarDetails(HashMap<String, Object> allRequestParams) {
        log.info("SagaClientManagerImpl:: inside the getCarDetails, allRequestParams: {}",allRequestParams);
        try {
            return sagaRecentClient.getCarDetails(allRequestParams);
        } catch (Exception e) {
            metricsAgent.incrementEventCount("saga_carDetails_api_exception");
            log.error("SagaClientManagerImpl::Exception thrown while getting Car Details ", e);
            return null;
        }

    }

    @Retryable(maxAttempts=3)
    public CarDetailColourResponseWrapper getCarImages(HashMap<String, Object> allRequestParams) {
        log.info("SagaClientManagerImpl:: inside the getCarImages, allRequestParams: {}",allRequestParams);
        try {
            return sagaRecentClient.getCarImages(allRequestParams);
        } catch (Exception e) {
            metricsAgent.incrementEventCount("saga_carImages_api_exception");
            log.error("SagaClientManagerImpl::Exception thrown while getting Car Images ", e);
            return null;
        }

    }

}
