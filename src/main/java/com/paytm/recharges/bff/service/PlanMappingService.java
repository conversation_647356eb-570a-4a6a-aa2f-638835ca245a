package com.paytm.recharges.bff.service;

import com.paytm.recharges.bff.client.AerospikeGenericWrapper;
import com.paytm.recharges.bff.client.DcatRestClient;
import com.paytm.recharges.bff.datalayer.cache.CVRProductCache;
import com.paytm.recharges.bff.datalayer.dto.response.PlanMappingDto;
import com.paytm.recharges.bff.datalayer.model.Product;
import com.paytm.recharges.bff.monitor.MetricsAgent;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.recharges.saga_client.datalayer.dto.response.SagaFavResponsePOJO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.paytm.recharges.bff.constants.Constants.BFF_NAMESPACE;
import static com.paytm.recharges.bff.constants.Constants.DISCONTINUED_PLAN_MAPPING_TYPE;
import static com.paytm.recharges.bff.constants.Constants.MOBILE;
import static com.paytm.recharges.bff.constants.Constants.PLAN_MAPPING;
import static com.paytm.recharges.bff.constants.Constants.PLAN_MAPPING_ERROR_METRIC;
import static com.paytm.recharges.bff.constants.Constants.PLAN_MAPPING_LOCK_KEY;
import static com.paytm.recharges.bff.constants.Constants.PREPAID;

@Service
public class PlanMappingService {

    private static final CustomLogger log = CustomLogManager.getLogger(PlanMappingService.class);

    @Autowired
    private MetricsAgent metricsAgent;

    @Autowired
    private AerospikeGenericWrapper aerospikeGenericWrapper;

    @Autowired
    private DcatRestClient dcatRestClient;

    public Double getUpdatedAmtForDiscontinuedPlan(String operator, String circle, double oldAmt) {
        try {
            String planMappingKey = createPlanMappingKey(operator, circle, oldAmt, DISCONTINUED_PLAN_MAPPING_TYPE);

            PlanMappingDto planMapping = aerospikeGenericWrapper.getData(BFF_NAMESPACE, PLAN_MAPPING, planMappingKey,3000, PlanMappingDto.class);

            log.info("###### 16: getUpdatedAmtForDiscontinuedPlan : planMapping :: {}", planMapping);
            if (Objects.nonNull(planMapping) && planMapping.isValid()) {
                return planMapping.getNewPlanAmt();
            }
        } catch (Exception e) {
            metricsAgent.incrementEventCount(PLAN_MAPPING_ERROR_METRIC, "source:aerospike");
            log.error("getUpdatedAmtForDiscontinuedPlan: Error while fetching plan mapping from aerospike", e);
        }
        log.info("getUpdatedAmtForDiscontinuedPlan: Plan mapping not found in cache for operator: {}, circle: {}, oldAmt: {}. Retuning null", operator, circle, oldAmt);
        return null;
    }

    public boolean acquireLock(){
        try {
            log.info("acquireLock: Trying to acquire lock for plan mapping");

            Map cacheLockData = aerospikeGenericWrapper.getData(BFF_NAMESPACE, PLAN_MAPPING, PLAN_MAPPING_LOCK_KEY,3000, Map.class);

            String lockKey = "isLocked";

            if(Objects.nonNull(cacheLockData) && cacheLockData.get(lockKey) != null && Boolean.TRUE.equals(cacheLockData.get(lockKey))) {
                log.info("acquireLock: Failed to acquire lock for plan mapping");
                return false;
            }

            Map<String, Object> lockMap = new HashMap<>();
            lockMap.put(lockKey, true);

            aerospikeGenericWrapper.putData(BFF_NAMESPACE, PLAN_MAPPING, PLAN_MAPPING_LOCK_KEY, 3540, 3000, lockMap); // 59 mins

            log.info("acquireLock: Successfully acquire lock for plan mapping");
            return true;
        } catch (Exception ex) {
            log.error("acquireLock: Error in acquiring lock for plan mapping in aerospike. lockKey: {}", PLAN_MAPPING_LOCK_KEY, ex);
            throw ex;
        }
    }

    public void putPlanMappingsInCache() {
        try {
            if(!acquireLock()){
                return;
            }

            List<PlanMappingDto> planMappings = dcatRestClient.getPlanMappings();
            if (!planMappings.isEmpty()) {
                planMappings.forEach(planMapping -> {
                    Map<String, Object> planMappingMap = new HashMap<>();
                    planMappingMap.put("newPlanAmt", planMapping.getNewPlanAmt());
                    planMappingMap.put("circleKey", planMapping.getCircleKey());
                    planMappingMap.put("planId", planMapping.getPlanId());
                    planMappingMap.put("type", planMapping.getType());

                    String planMappingKey = null;
                    try {
                        planMappingKey = createPlanMappingKey(planMapping.getOperator(), planMapping.getCircle(), planMapping.getOldPlanAmt(), planMapping.getType());
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                    aerospikeGenericWrapper.putData(BFF_NAMESPACE, PLAN_MAPPING, planMappingKey, 28800, 3000, planMappingMap);// 12 hrs
                });
            }
        } catch (Exception ex) {
            metricsAgent.incrementEventCount(PLAN_MAPPING_ERROR_METRIC, "source:dcat");
            log.error("putPlanMappingsInCache: Error while fetching plan mappings from dcat", ex);
        }
    }

    private String createPlanMappingKey(String operator, String circle, double planAmt, int type) throws Exception {
        if(Objects.isNull(operator) || Objects.isNull(circle) || operator.isEmpty() || circle.isEmpty()) {
            log.error("createPlanMappingKey: Invalid input. operator: {}, circle: {}, planAmt: {}, type: {}", operator, circle, planAmt, type);
            throw new Exception("createPlanMappingKey: Invalid input");
        }
        return  operator.toLowerCase() + "_ "+ circle.toLowerCase() + "_" + planAmt + "_" + type;
    }

    public void updateAmountInSagaResponse(List<SagaFavResponsePOJO> recents) {
        try {
            log.info("updateAmountInSagaResponse: recents {}", recents);

            if(Objects.isNull(recents)) {
                return;
            }
            
            recents.forEach(recent -> {
                if(!recent.isDefaultAmount() || Objects.isNull(recent.getPid()) || Objects.isNull(recent.getBill()) || Objects.isNull(recent.getBill().getBillAmount())) {
                    log.info("updateAmountInSagaResponse: recent does not default amount flag or pid or bill amount is null. recent {}", recent);
                    return;
                }

                Product product = CVRProductCache.getInstance().getProductDetails(Long.parseLong(recent.getPid()));

                log.info("updateAmountInSagaResponse: product {}", product);

                if (Objects.nonNull(product) && MOBILE.equalsIgnoreCase(product.getService()) && PREPAID.equalsIgnoreCase(product.getPayType())) {
                    Double newAmt = getUpdatedAmtForDiscontinuedPlan(product.getOperator().toLowerCase(), product.getCircle().toLowerCase(), recent.getBill().getBillAmount());
                    if(Objects.nonNull(newAmt)) {
                        recent.getBill().setBillAmount((newAmt.floatValue()));
                        recent.setAmount(newAmt);
                        log.info("updateAmountInSagaResponse: Updated amount in saga response. recent {}", recent);
                    }
                }
            });
        } catch (Exception e) {
            log.error("updateAmountInSagaResponse: Error in updating amount in saga response", e);
        }
    }
}
