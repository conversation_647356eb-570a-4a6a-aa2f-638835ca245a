package com.paytm.recharges.bff.service.impl;

import com.aerospike.client.AerospikeClient;
import com.aerospike.client.Key;
import com.aerospike.client.Record;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.paytm.recharges.bff.constants.Constants;
import com.paytm.recharges.bff.datalayer.cache.CVRProductCache;
import com.paytm.recharges.bff.datalayer.cache.FeatureConfigCache;
import com.paytm.recharges.bff.datalayer.cache.ServiceConfigCache;
import com.paytm.recharges.bff.datalayer.dto.request.RechargeVerifyRequest;
import com.paytm.recharges.bff.datalayer.dto.request.ValidateCartItem;
import com.paytm.recharges.bff.datalayer.dto.response.Action;
import com.paytm.recharges.bff.datalayer.dto.response.CheckoutErrorResponse;
import com.paytm.recharges.bff.datalayer.dto.response.EnrichPopup;
import com.paytm.recharges.bff.datalayer.dto.response.FFRValidateResponse;
import com.paytm.recharges.bff.datalayer.dto.response.RechargeItem;
import com.paytm.recharges.bff.datalayer.dto.response.RechargeVerifyResponse;
import com.paytm.recharges.bff.datalayer.dto.response.ResponseStatus;
import com.paytm.recharges.bff.datalayer.model.FeatureConfig;
import com.paytm.recharges.bff.datalayer.model.Product;
import com.paytm.recharges.bff.enums.FeatureConfigKey;
import com.paytm.recharges.bff.enums.RequestType;
import com.paytm.recharges.bff.monitor.MetricsAgent;
import com.paytm.recharges.bff.service.LocalisationManager;
import com.paytm.recharges.bff.utils.JsonUtil;
import com.paytm.recharges.bff.utils.VersionComparable;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytmmall.cart.checkout.dto.PaymentMode;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.paytm.recharges.bff.constants.Constants.BLOCKED_USERS;
import static com.paytm.recharges.bff.constants.Constants.BLOCKED_USERS_SET;
import static com.paytm.recharges.bff.constants.Constants.BLOCKED_USER_ERROR_CODE;
import static com.paytm.recharges.bff.constants.Constants.CATEGORY_NAME_MAPPING;
import static com.paytm.recharges.bff.constants.Constants.CREDIT_CARD;
import static com.paytm.recharges.bff.constants.Constants.SMARTREMINDER;
import static com.paytm.recharges.bff.constants.Constants.SUCCESS;
import static com.paytm.recharges.bff.constants.Constants.USER_FOUND_IN_BLOCKED_LIST_CACHE;
import static com.paytm.recharges.bff.constants.Constants.USER_NOT_FOUND_IN_BLOCKED_LIST_CACHE;
import static com.paytm.recharges.bff.constants.ErrorMessage.ERROR_MESSAGE;

@Service
public class RechargeHelperService {

    private static final CustomLogger log = CustomLogManager.getLogger(RechargeHelperService.class);

    @Autowired
    LocalisationManager localisationManager;
    @Autowired
    private AerospikeClient aerospikeClient;
    @Autowired
    private MetricsAgent metricsAgent;
    @Value("#{'${recharges.recharge_fsids}'.split(',')}")
    private Set<Long> rechargeFsids;

    public void setEnrichPopup(RechargeVerifyResponse verifyResponse, String message) {

        EnrichPopup enrichPopup = JsonUtil.fromJson(message, EnrichPopup.class);

        Map<String, Object> errorInfo = new HashMap<>();
        errorInfo.put("errorPopup", enrichPopup);

        verifyResponse.getCart().setError(enrichPopup.getMessage());
        verifyResponse.getCart().setErrorInfo(JsonUtil.toJson(errorInfo));
    }

    public Set<Long> filterRechargeItem(RechargeVerifyRequest verifyRequest) {
        Set<Long> rechargeProductIds = verifyRequest.getCartItems().stream().filter(
                item -> isPrevalidateEnabled(CVRProductCache.getInstance().getProductDetails(item.getProductId())))
                .map(item -> item.getProductId()).collect(Collectors.toSet());
        return rechargeProductIds;
    }

    public Set<Long> collectPids(RechargeVerifyRequest verifyRequest) {

        if (Objects.isNull(verifyRequest.getCartItems()))
            return new HashSet<>();

        return verifyRequest.getCartItems().stream().map(RechargeItem::getProductId).collect(Collectors.toSet());
    }

    public boolean isPrevalidateEnabled(Product product) {
        log.debug("isPrevalidateEnabled {} rechargeFsids {}", JsonUtil.toJson(product), JsonUtil.toJson(rechargeFsids));
        return product != null && rechargeFsids.contains(product.getFulfillmentService())
                && product.getValidate() != null && product.getValidate() == 1;
    }


    public boolean isFeatureLiveForCategory(Set<Long> categoryIds, String featureName, String client, String version) {

        if (StringUtils.isEmpty(client) || StringUtils.isEmpty(version)) {
            log.info("featureConfigs:: client or version client {} version {}", client, version);
            return false;
        }

        Map<String, FeatureConfig> featureConfigs = FeatureConfigCache.getInstance().get(featureName);
        if (Objects.isNull(featureConfigs)) {
            log.info("featureConfigs:: no config found for featureName {}", featureName);
            return false;
        }
        client = client.toLowerCase();
        if (isLiveOnAll(featureName, featureConfigs)) {
            return true;
        }
        if (isLiveOnCategoryId(categoryIds, null, featureName, client, version, featureConfigs)) {
            return true;
        }
        return false;
    }

    /**

     * Check feature is live or not
     *
     * @param
     * @param featureName
     * @return
     */
    public boolean isFeatureLive(Set<Long> productIds, String featureName, String client, String version) {
        log.info("featureConfigs:: before isFeatureLive");
        if (StringUtils.isEmpty(client) || StringUtils.isEmpty(version)) {
            log.info("featureConfigs:: client or version client {} version {}", client, version);
            return false;
        }
        Map<String, FeatureConfig> featureConfigs = FeatureConfigCache.getInstance().get(featureName);
        if (Objects.isNull(featureConfigs)) {
            log.info("featureConfigs:: no config found for featureName {}", featureName);
            return false;
        }

        log.info("featureConfigs:: featureConfigs {}", featureConfigs);

        client = client.toLowerCase();
        if (isLiveOnAll(featureName, featureConfigs)) {
            return true;
        }
        if (Objects.isNull(productIds) || productIds.isEmpty()) {
            log.info("featureConfigs:: productIds is missing to check live status for featureName {}", featureName);
            return false;
        }
        if (isLiveOnProductId(productIds, featureName, client, version, featureConfigs)) {
            return true;
        }
        if (isLiveOnCategoryId(null,  productIds, featureName, client, version, featureConfigs)) {
            return true;
        }
        log.info("featureConfigs:: feature not live for productIds {} featureName {}", JsonUtil.toJson(productIds), featureName);
        return false;
    }

    private boolean liveOnClientVersion(Map<Long, Map<String, String>> configMap, Set<Long> ids, String client,
                                        String version) {
        for (Long id : ids) {
            log.info("featureConfigs:: inside liveOnClientVersion live for configMap {} ids {} client {} version {}", JsonUtil.toJson(configMap), JsonUtil.toJson(id),client,version  );
            if (Objects.nonNull(configMap.get(id)) && !StringUtils.isEmpty(configMap.get(id).get(client))
                    && isLiveOnVersion(version, configMap.get(id).get(client))) {
                return true;
            }
        }
        return false;
    }

    public boolean isLiveOnVersion(String version, String minVersion) {
        if(version==null || minVersion==null) {
            log.info("version or minVersion null hence returning false");
            return false;
        }
        VersionComparable requestVersion = new VersionComparable(version);
        VersionComparable configMinVersion = new VersionComparable(minVersion);
        return requestVersion.compareTo(configMinVersion) >= 0;
    }

    public List<PaymentMode> disablePaymentMethods(FFRValidateResponse validateResponse) {
        List<PaymentMode> disabledPaymentMethods = new ArrayList<>();

        if (validateResponse == null)
            return disabledPaymentMethods;

        for (ValidateCartItem item : validateResponse.getCartItems()) {
            if (Objects.nonNull(item.getServiceOptions()) && Objects.nonNull(item.getServiceOptions().getActions())) {
                for (Action action : item.getServiceOptions().getActions()) {
                    if (Objects.nonNull(action.getDisabledModes())) {
                        // TODO verify this
                        List<String> disableModes = action.getDisabledModes();
                        for (String mode : disableModes) {
                            PaymentMode pm = new PaymentMode();
                            pm.setMode(mode);
                            disabledPaymentMethods.add(pm);
                        }
                    }
                }
            }
        }
        return disabledPaymentMethods;
    }

    public void handleStringPrice(RechargeItem item) {
        // handle string price & totalamount
        Object price = item.getConfiguration().get("price");
        Object totalAmount = item.getConfiguration().get("totalamount");

        if (Objects.nonNull(price) && (price instanceof String)) {
            String priceStr = (String) price;
            priceStr = priceStr.replaceAll(",", "");
            item.getConfiguration().put("price", Double.parseDouble(priceStr));
        }

        if (Objects.nonNull(totalAmount) && (totalAmount instanceof String)) {
            String totalAmountStr = (String) totalAmount;
            totalAmountStr = totalAmountStr.replaceAll(",", "");
            item.getConfiguration().put("totalamount", Double.parseDouble(totalAmountStr));
        }
    }

    /**
     * Add product info in request
     *
     * @param body
     * @return
     */
    public Object addProductInfo(Object body) {

        Map<Long, Product> productMap = new HashMap<>();

        JSONObject jsonObject = new JSONObject(JsonUtil.toJson(body));

        if(Objects.isNull(jsonObject) || !jsonObject.has("cart_items") || jsonObject.isNull("cart_items")) {
            log.info("addProductInfo:: empty boy or cart_items not found in request");
            return body;
        }

        JSONArray cartItems = (JSONArray) jsonObject.get("cart_items");
        if(cartItems.length() == 0) {
            log.info("addProductInfo:: cart_items not found in request");
            return body;
        }

        for(int i = 0; i < cartItems.length(); i++) {
            JSONObject itemObject = (JSONObject) cartItems.get(i);
            if(itemObject.has("product_id")) {
                Long productId = itemObject.getLong("product_id");
                if(!Objects.isNull(productId)) {
                    productMap.put(productId, CVRProductCache.getInstance().getProductDetails(productId));
                }
            }
        }

        jsonObject.put("product_info", productMap);
        //log.info("addProductInfo:: updated request {}", jsonObject);
        return jsonObject.toMap();
    }


    public boolean isCoftEnable() {

        Map<String, FeatureConfig> featureConfigs = FeatureConfigCache.getInstance().get(Constants.IS_COFT_VIEW);
        if (Objects.isNull(featureConfigs)) {
            log.trace("featureConfigs:: no config found for featureName {}", "isCoft");
            return false;
        }

        if (featureConfigs.containsKey(FeatureConfigKey.all.name())) {
            FeatureConfig featureConfig = featureConfigs.get(FeatureConfigKey.all.name());
            Map<Long, Map<String, String>> configMap = featureConfig.getConfigMap();
            Map<String, String> clientVersionMap = configMap.get(0L);
            if (Objects.nonNull(clientVersionMap) && Objects.nonNull(clientVersionMap.get(Constants.FEATURE_FOR_ALL))
                    && clientVersionMap.get(Constants.FEATURE_FOR_ALL).equals(Constants.ACTIVE_STATUS)) {
                log.info("featureConfigs:: all live for featureName {}", "isCoft");
                return true;
            }
        }
        return false;
    }

    public boolean featureForRollout(String keyForRolloutFeature) {

        Map<String, FeatureConfig> featureConfigs = FeatureConfigCache.getInstance().get(keyForRolloutFeature);
        if (Objects.isNull(featureConfigs)) {
            log.info("featureConfigs:: no config found for featureName {}", keyForRolloutFeature);
            return false;
        }

        if (featureConfigs.containsKey(FeatureConfigKey.all.name())) {
            FeatureConfig featureConfig = featureConfigs.get(FeatureConfigKey.all.name());
            Map<Long, Map<String, String>> configMap = featureConfig.getConfigMap();
            Map<String, String> clientVersionMap = configMap.get(0L);
            if (Objects.nonNull(clientVersionMap) && Objects.nonNull(clientVersionMap.get(Constants.FEATURE_FOR_ALL))
                    && clientVersionMap.get(Constants.FEATURE_FOR_ALL).equals(Constants.ACTIVE_STATUS)) {
                log.info("featureConfigs:: all live for featureName {}",keyForRolloutFeature);
                return true;
            }
        }
        return false;
    }

    public String servicesForSaga(String featureNameKey){
        Map<String,FeatureConfig> featureConfigs = FeatureConfigCache.getInstance().get(featureNameKey);
        if (Objects.isNull(featureConfigs)){
            log.info("featureConfigs:: no config found for featureName {}",featureNameKey);
            return null;
        }
        if(featureConfigs.containsKey(FeatureConfigKey.service.name())){
            FeatureConfig featureConfig = featureConfigs.get(FeatureConfigKey.service.name());
            Map<Long, Map<String, String>> configMap = featureConfig.getConfigMap();
            if(Objects.nonNull(configMap)){
                Map<String, String> clientVersionMap = configMap.get(1L);
                if(Objects.nonNull(clientVersionMap) && Objects.nonNull(clientVersionMap.get("services"))){
                    String services = clientVersionMap.get("services");
                        return services;
                }
            }



        }
        return null;
    }


    public Object addOnDemandFlag(Object body) {

        Map<Long, Product> productMap = new HashMap<>();

        JSONObject jsonObject = new JSONObject(JsonUtil.toJson(body));

        if(Objects.isNull(jsonObject) || !jsonObject.has("cart_items") || jsonObject.isNull("cart_items")) {
            log.info("addProductInfo:: empty boy or cart_items not found in request");
            return body;
        }

        JSONArray cartItems = (JSONArray) jsonObject.get("cart_items");
        if(Objects.isNull(cartItems)||cartItems.length() == 0) {
            log.info("addProductInfo:: cart_items not found in request");
            return body;
        }

        for(int i = 0; i < cartItems.length(); i++) {
            JSONObject itemObject = (JSONObject) cartItems.get(i);
            String locMessage = localisationManager.getMessage(new String[]{Constants.LANGUAGE_ENGLISH, Constants.ON_DEMAND}, true, new HashMap<>());
            if(Objects.nonNull(locMessage)&&Boolean.parseBoolean(locMessage)) {
                itemObject.put("ondemand",true);
            }

        }

        return jsonObject.toMap();
    }

    private boolean isLiveOnAll(String featureName, Map<String, FeatureConfig> featureConfigs) {
        if (featureConfigs.containsKey(FeatureConfigKey.all.name())) {
            FeatureConfig featureConfig = featureConfigs.get(FeatureConfigKey.all.name());
            Map<Long, Map<String, String>> configMap = featureConfig.getConfigMap();
            Map<String, String> clientVersionMap = configMap.get(0L);
            if (Objects.nonNull(clientVersionMap) && Objects.nonNull(clientVersionMap.get("all")) && clientVersionMap.get(Constants.ROLL_OUT).equals("1")) {
                log.info("featureConfigs:: all live for featureName {}", featureName);
                return true;
            }
        }
        return false;

    }

    private boolean isLiveOnProductId(Set<Long> productIds, String featureName, String client, String version, Map<String, FeatureConfig> featureConfigs) {
        if (featureConfigs.containsKey(FeatureConfigKey.productId.name().toLowerCase())) {
            FeatureConfig featureConfig = featureConfigs.get(FeatureConfigKey.productId.name().toLowerCase());

            Map<Long, Map<String, String>> configMap = featureConfig.getConfigMap();

            if (liveOnClientVersion(configMap, productIds, client, version)) {
                log.info("featureConfigs:: product live for productIds {} featureName {} client {}", JsonUtil.toJson(productIds), featureName, client);
                return true;
            }
        }

        return false;

    }

    private boolean isLiveOnCategoryId(Set<Long> categoryIds,Set<Long> productIds ,String featureName, String client, String version, Map<String, FeatureConfig> featureConfigs) {
        if (featureConfigs.containsKey(FeatureConfigKey.categoryId.name().toLowerCase())) {

            log.info("featureConfigs:: category live check  for {} featureName {}", JsonUtil.toJson(categoryIds), featureName);
            FeatureConfig featureConfig = featureConfigs.get(FeatureConfigKey.categoryId.name().toLowerCase());
            if(Objects.isNull(categoryIds) && Objects.isNull(productIds)){
                log.info("null catgeory ids ");
                return false;
            }
            if(Objects.isNull(categoryIds)){
                categoryIds=fetchCategoryIds(productIds);
            }
            Map<Long, Map<String, String>> configMap = featureConfig.getConfigMap();
            log.info("featureConfigs:: category live check  for {} featureName {} && {}", JsonUtil.toJson(featureConfig), featureName, JsonUtil.toJson(configMap));
            if (liveOnClientVersion(configMap, categoryIds, client, version)) {
                log.info("featureConfigs:: category live for {} featureName {}", JsonUtil.toJson(categoryIds), featureName);
                return true;
            }

        }
        return false;
    }

    private Set<Long> fetchCategoryIds(Set<Long> productIds) {
        Set<Long> categoryIds = new HashSet<>();
        for (Long productId : productIds) {
            Product p = CVRProductCache.getInstance().getProductDetails(productId);

            if (Objects.nonNull(p)) {
                categoryIds.add(p.getCategoryId());
            }
        }
        log.info("featureConfigs:: categoryIds {} ", categoryIds);

        return categoryIds;
    }

    public Map<Long, String> getCategoryMap(){
        Map<Long, String> categoryMap = null;
        try {
            categoryMap = JsonUtil.convertObjectToMap(ServiceConfigCache.getInstance().get(CATEGORY_NAME_MAPPING), new TypeReference<Map<Long, String>>() {
            });
        }catch (NullPointerException | IllegalArgumentException | ClassCastException | JsonProcessingException e) {
            log.error("RechargeHelperService::init :: error while getting category mapping" +
                    " from service config {}", e);
        }
        return categoryMap;
    }
    public String getCategoryName(RechargeItem item) {

        Map<Long, String> categoryMap = getCategoryMap();
        Product product = CVRProductCache.getInstance().getProductDetails(item.getProductId());
        if (Objects.nonNull(product) && Objects.nonNull(categoryMap) && categoryMap.containsKey(product.getCategoryId()) ) {
                return categoryMap.get(product.getCategoryId());
        }
        log.info("product not found for pid {}", item.getProductId());
        return null;
    }


    public  boolean isCCBP(Long productId) {
        Product product = CVRProductCache.getInstance().getProductDetails(productId);
        if(Objects.nonNull(product)) {
            return org.apache.commons.lang3.StringUtils.equalsAnyIgnoreCase(product.getPayType(), CREDIT_CARD);
        }
        return false;
    }

    public boolean isBlockedUser(String customerId) {
        Key segmentKey = new Key(SMARTREMINDER, BLOCKED_USERS_SET, customerId);
        Record record = null;
        try {
            record = aerospikeClient.get(null, segmentKey);
        } catch (Exception ex) {
            log.error("[RechargeHelperService.isBlockedUser] :: Aerospike is down/not responding ", ex);
        }

        if (Objects.nonNull(record)) {
            log.debug("[RechargeHelperService.isBlockedUser] :: Record already exisists record is -- " + record.bins);
            Map<String, Object> map = record.bins;
            if (Objects.nonNull(map)) {
                metricsAgent.incrementEventCount(USER_FOUND_IN_BLOCKED_LIST_CACHE);
                if (Objects.isNull(map.get(BLOCKED_USERS)))
                    return false;
                else
                    return true;
            } else {
                metricsAgent.incrementEventCount(USER_NOT_FOUND_IN_BLOCKED_LIST_CACHE);
            }
        }
        return false;
    }
    public String getErrorMessageForBlockedUser() {
        String[] key = {Constants.LANGUAGE_ENGLISH, BLOCKED_USER_ERROR_CODE,Constants.REQUEST_TYPE_VALIDATION};
        String message= localisationManager.getMessage(key, false, new HashMap<>());
        CheckoutErrorResponse checkoutErrorResponse = new CheckoutErrorResponse();
        checkoutErrorResponse.setErrorCode(BLOCKED_USER_ERROR_CODE);
        if(StringUtils.isBlank(message))
            checkoutErrorResponse.setError(ERROR_MESSAGE);
        else
            checkoutErrorResponse.setError(message);
        ResponseStatus responseStatus = new ResponseStatus();
        responseStatus.setResult(SUCCESS);
        checkoutErrorResponse.setStatus(responseStatus);
        return JsonUtil.toJson(checkoutErrorResponse);
    }
    
	public String getErrorMessageForBlockedUserRequest(RequestType requestType) {
		String[] key = { Constants.LANGUAGE_ENGLISH, BLOCKED_USER_ERROR_CODE, requestType.toString(),
				Constants.BLOCKER_USER_REQUEST };
		
		String message = localisationManager.getMessage(key, false, new HashMap<>());
		
		CheckoutErrorResponse checkoutErrorResponse = new CheckoutErrorResponse();
		checkoutErrorResponse.setErrorCode(BLOCKED_USER_ERROR_CODE);

		if (StringUtils.isBlank(message)) {
			checkoutErrorResponse.setError(ERROR_MESSAGE);
		} else {
			checkoutErrorResponse.setError(message);
		}

		ResponseStatus responseStatus = new ResponseStatus();
		responseStatus.setResult(SUCCESS);
		checkoutErrorResponse.setStatus(responseStatus);
		return JsonUtil.toJson(checkoutErrorResponse);
	}
}
