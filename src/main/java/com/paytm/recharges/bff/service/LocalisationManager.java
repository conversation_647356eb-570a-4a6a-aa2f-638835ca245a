package com.paytm.recharges.bff.service;

import com.paytm.recharges.bff.aop.annotations.MethodLatencyMetricsAction;
import com.paytm.recharges.bff.constants.Constants;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import org.apache.commons.text.StringSubstitutor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Map;

@Service
@Scope(ConfigurableBeanFactory.SCOPE_SINGLETON)
public class LocalisationManager {

    private static final CustomLogger log = CustomLogManager.getLogger(LocalisationManager.class);

    @Autowired
    private com.paytm.recharges.bff.monitor.MetricsAgent metricsAgent;
    @Value("${localisation.service:}")
    private String service;
    @Value("${localisation.environment:}")
    private String environment;
    @Value("${localisation.language:}")
    private String language;
    @Value("${localisation.reloadInterval}")
    private Integer reloadInterval;


    public void initialize(){
        com.paytm.localisation.Service.init(service,language,environment, reloadInterval);
    }

    @MethodLatencyMetricsAction(metricsName = "localization_getMessage")
    public String getMessage(String[] keyComponents, Boolean findSimilarMatch, Map<String,String> payload){
        
        log.trace("getMessage::keyComponents {}", keyComponents, " payload {}", payload);

        String localisedMessage = com.paytm.localisation.Service.getMessage(service, keyComponents,findSimilarMatch);

        if(localisedMessage.isEmpty()){
            localisedMessage = this.getDefaultEnglishMessage(keyComponents, findSimilarMatch);
        }
        // After attempting both localised and default English, push metric if still missing
        if(localisedMessage == null || localisedMessage.isEmpty()){
            log.info("LocalisationManager :: Both localised and default English messages are missing for key: {}", java.util.Arrays.toString(keyComponents));
            metricsAgent.incrementEventCount(Constants.MISSING_LOCALISATION_KEY_METRIC, new ArrayList<>(Collections.singletonList("key=" + Arrays.toString(keyComponents))));
        }
        return StringSubstitutor.replace(localisedMessage, payload);
    }

    public String getDefaultEnglishMessage(String[] keyComponents, Boolean findSimilarMatch){
        keyComponents[0] = Constants.LANGUAGE_ENGLISH;
        return com.paytm.localisation.Service.getMessage(service, keyComponents,findSimilarMatch);
    }


}
