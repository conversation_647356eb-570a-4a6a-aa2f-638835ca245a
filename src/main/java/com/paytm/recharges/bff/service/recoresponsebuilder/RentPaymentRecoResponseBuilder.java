package com.paytm.recharges.bff.service.recoresponsebuilder;

import com.paytm.recharges.bff.datalayer.model.Product;
import com.paytm.recharges.bff.service.RecentLocalisationManager;
import com.paytm.recharges.bff.service.RecoResponseBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.paytm.recharges.bff.constants.Constants.*;

/**
 * Formatter for rent payment titles
 */
@Component
@Slf4j
public class RentPaymentRecoResponseBuilder implements RecoResponseBuilder {

    private final RecentLocalisationManager recentLocalisationManager;

    public RentPaymentRecoResponseBuilder(RecentLocalisationManager recentLocalisationManager){
        this.recentLocalisationManager = recentLocalisationManager;
    }

    @Override
    public String format(Product product, HashMap<String, Object> config, Map<String, String> payload) {
        String rechargeNumber3 = (String) config.get(RECHARGE_NUMBER_3);
        log.info("RentPaymentRecoResponseBuilder.format :: rechargeNumber3 :: {}", rechargeNumber3);
        if (Objects.nonNull(rechargeNumber3)) {
            String[] titleKey = new String[]{LANGUAGE_ENGLISH, RECO, RENT_TF_TITLE};
            log.info("RentPaymentRecoResponseBuilder.format :: titleKey :: {}", Arrays.toString(titleKey));
            payload.put(BENEFICIARY_NAME, rechargeNumber3);
//            return recentLocalisationManager.getMessage(titleKey, true, payload);

            String ans = recentLocalisationManager.getMessage(titleKey, true, payload);

            log.info("localization response :: {}", ans);

            return  ans;
        }
        
        // Fallback to default
        return config.containsKey(RECHARGE_NUMBER) ? (String) config.get(RECHARGE_NUMBER) : "";
    }

    @Override
    public boolean canHandle(Product product) {
        return Objects.nonNull(product) && RENT_PAYMENT.equalsIgnoreCase(product.getService());
    }
} 