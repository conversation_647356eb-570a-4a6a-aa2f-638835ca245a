package com.paytm.recharges.bff.service.impl;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.recharges.bff.client.AerospikeGenericWrapper;
import com.paytm.recharges.bff.client.AgentAeroSpikeClientWrapper;
import com.paytm.recharges.bff.constants.Constants;
import com.paytm.recharges.bff.constants.DateFormat;
import com.paytm.recharges.bff.datalayer.cache.ServiceConfigCache;
import com.paytm.recharges.bff.datalayer.dto.response.CarDetailDTO;
import com.paytm.recharges.bff.datalayer.dto.response.RecentResponse;
import com.paytm.recharges.bff.datalayer.dto.response.RecentResponseMixIn;
import com.paytm.recharges.bff.datalayer.dto.response.SagaRecentResponse;
import com.paytm.recharges.bff.datalayer.dto.response.VehicalDetail;
import com.paytm.recharges.bff.enums.Widget;
import com.paytm.recharges.bff.monitor.MetricsAgent;
import com.paytm.recharges.bff.responsehandler.RecentResponseWorker;
import com.paytm.recharges.bff.service.FavouriteService;
import com.paytm.recharges.bff.service.RecentLocalisationManager;
import com.paytm.recharges.bff.utils.DateUtil;
import com.paytm.recharges.bff.utils.EncryptionUtil;
import com.paytm.recharges.bff.utils.RecentUtils;
import com.paytm.recharges.bff.utils.StreamUtils;
import com.paytm.recharges.bff.utils.Utils;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import com.paytm.recharges.favourite_orders_client.datalayer.model.FrequentOrder;
import com.paytm.recharges.saga_client.datalayer.dto.response.SagaFavResponsePOJO;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.stream.Collectors;
import java.util.Arrays;

import static com.paytm.recharges.bff.constants.Constants.AMOUNT;
import static com.paytm.recharges.bff.constants.Constants.BFF_NAMESPACE;
import static com.paytm.recharges.bff.constants.Constants.CATEGORY;
import static com.paytm.recharges.bff.constants.Constants.FLAG_METRIC_TAG;
import static com.paytm.recharges.bff.constants.Constants.GROUP;
import static com.paytm.recharges.bff.constants.Constants.IS_AMOUNT_EDITABLE_FLAG;
import static com.paytm.recharges.bff.constants.Constants.IS_GROUP_DISPLAY_ENABLED_FLAG;
import static com.paytm.recharges.bff.constants.Constants.MOBILE;
import static com.paytm.recharges.bff.constants.Constants.PAYTM_POSTPAID_ROLLOUT_PERCENTAGE;
import static com.paytm.recharges.bff.constants.Constants.PRODUCT_METRIC_TAG;
import static com.paytm.recharges.bff.constants.Constants.RECO_REQUEST_IDENTIFIER;
import static com.paytm.recharges.bff.constants.Constants.TRUE;

public abstract class FavouriteServiceImpl implements FavouriteService {

    private static final CustomLogger log = CustomLogManager.getLogger(FavouriteServiceImpl.class);

    @Autowired
    private AerospikeGenericWrapper aerospikeGenericWrapper;

    @Autowired
    private MetricsAgent metricsAgent;

    @Autowired
    private RecentResponseWorker recentResponseWorker;

    @Autowired
    protected RecentLocalisationManager localisationManager;

    @Autowired
    private AgentAeroSpikeClientWrapper agentAeroSpikeClientWrapper;


    @Override
    public ArrayList<FrequentOrder> prepareFavResponse(List<SagaFavResponsePOJO> sagaRecentResponses, Map<String, String> allRequestParams) throws JsonProcessingException {
        List<RecentResponse> recentResponseList = prepareRecentResponse(sagaRecentResponses, allRequestParams);
        ArrayList<FrequentOrder> frequentOrders = new ArrayList<>();
        for (RecentResponse recentResponse : recentResponseList) {
            if (Objects.nonNull(recentResponse)) {
                recentResponse.setUniqueId(fetchUniqueId(recentResponse));
                if(Constants.PAYTM_POSTPAID_SERVICE.equalsIgnoreCase(recentResponse.getProduct().getService())){
                    boolean isUserEligibleForRecharge=RecentUtils.isPaytmPostpaidCustomerEligible(recentResponse.getCustomerId(),ServiceConfigCache.getInstance().getInteger(PAYTM_POSTPAID_ROLLOUT_PERCENTAGE),allRequestParams.get(Constants.VERSION));

                    if(isUserEligibleForRecharge)recentResponse.setIsUserEligibleForRecharge(isUserEligibleForRecharge);
                }else if(Constants.FASTAG_SERVICE.equalsIgnoreCase(recentResponse.getProduct().getService())){
                    recentResponse.setVehicalDetail(createVehicalDetail(recentResponse));
                }
                ObjectMapper mapper = new ObjectMapper();
                mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);

                if(allRequestParams.containsKey(Constants.API_VERSION_KEY) && allRequestParams.get(Constants.API_VERSION_KEY).equals(Constants.FREQUENT_ORDERS_VERSION_V4)){
                    mapper.addMixIn(RecentResponse.class, RecentResponseMixIn.class);
                    mapper.setSerializationInclusion(JsonInclude.Include.NON_EMPTY);
                }
                if(Objects.nonNull(recentResponse.getMetadata())
                        && recentResponse.getMetadata().containsKey("is_data_exhaust")
                        && (Boolean) recentResponse.getMetadata().get("is_data_exhaust")
                        && !recentResponse.getBills().isEmpty()){
                    recentResponse.getBills().get(0).setDueDate(null);
                    recentResponse.getBills().get(0).setExpiry(null);
                }
                if(Objects.isNull(allRequestParams.get(RECO_REQUEST_IDENTIFIER))) {
                    updateRecentResponseWithAsyncVerifyFlags(recentResponse);
                }
                recentResponse.setCurrentDate(DateUtil.formatDate(new Date(),"yyyy-MM-dd"));
                recentResponse.setRemindLaterDaysConfig(ServiceConfigCache.getInstance().getInteger(Constants.REMIND_LATER_DAYS_CONFIG));
                FrequentOrder frequentOrder=new FrequentOrder(new JSONObject(mapper.writeValueAsString(recentResponse)));
                if(MOBILE.equalsIgnoreCase(recentResponse.getProduct().getService())){
                    metricsAgent.incrementEventCount("MOBILE_OPERATOR_VALIDATED_CHECK");
                    if(recentResponse.getOperatorValidatedAt() !=null){
                        frequentOrder.getOrderData().put("operator_validated_at",recentResponse.getOperatorValidatedAt().getTime());
                        metricsAgent.incrementEventCount("MOBILE_OPERATOR_VALIDATED_AT_FOUND");
                    }else{
                        metricsAgent.incrementEventCount("MOBILE_OPERATOR_VALIDATED_AT_NULL");
                        log.info("[FavouiteServiceImpl.prepareFavResponse] :: returning null OPERATOR_VALIDATED_AT customer {} rechargeNumber {} eventstate {}",recentResponse.getCustomerId(),recentResponse.getRechargeNumberForDisplay(),recentResponse.getAllEventState());
                    }
                }
                frequentOrders.add(frequentOrder);
            }

        }
        return frequentOrders;
    }

    abstract protected String fetchUniqueId(RecentResponse recentResponse);

    abstract protected Widget getWidgetType();

    private VehicalDetail createVehicalDetail(RecentResponse recentResponse){
        VehicalDetail vehicalDetail=new VehicalDetail();
        vehicalDetail.setVehicalNumber((String)recentResponse.getConfiguration().get("recharge_number"));
        String varientId=null;
        String color=null;
        String makeId="";
        String modelId="";
        if(recentResponse.getFastagExtraData()!=null){
            if(recentResponse.getFastagExtraData().containsKey("variantId"))
                varientId = String.valueOf(recentResponse.getFastagExtraData().get("variantId"));
            if(recentResponse.getFastagExtraData().containsKey("vehicleColor"))
                color = String.valueOf(recentResponse.getFastagExtraData().get("vehicleColor"));
            if(recentResponse.getFastagExtraData().containsKey("make"))
                makeId = String.valueOf(recentResponse.getFastagExtraData().get("make"));
            if(recentResponse.getFastagExtraData().containsKey("model"))
                modelId = String.valueOf(recentResponse.getFastagExtraData().get("model"));
        }
        if(Objects.nonNull(varientId)){
            CarDetailDTO carDetailResponse=agentAeroSpikeClientWrapper.getVehicalDetailByVarientId(varientId,color,makeId,modelId);
            if(Objects.nonNull(carDetailResponse)) {
                vehicalDetail.setVehicalName(carDetailResponse.getDisplayName());
                vehicalDetail.setVehicalImage(carDetailResponse.getImageURL());
            }
        }
        return vehicalDetail;
    }


    private List<RecentResponse> prepareRecentResponse(List<SagaFavResponsePOJO> sagaRecentResponses, Map<String, String> allRequestParams) {
        List<SagaRecentResponse> sagaMappedRecentResponses = new ArrayList<>();
        List<RecentResponse> recentResponses = new ArrayList<>();
        ObjectMapper mapper = new ObjectMapper();
        
        // Process each card individually with error handling
        for (SagaFavResponsePOJO sagaRecentResponse : sagaRecentResponses) {
            try {
                SagaRecentResponse recentResponse = new SagaRecentResponse();
                log.debug("the recent response is {}", recentResponse);
                recentResponse=mapper.convertValue(sagaRecentResponse, SagaRecentResponse.class);
                Long customerId = Long.parseLong(allRequestParams.get(Constants.CUSTOMER_ID));
                recentResponse.setCustomerId(customerId);
                sagaRecentResponse.setCustomerId(customerId);
                sagaMappedRecentResponses.add(recentResponse);
            } catch (Exception e) {
                log.error("FavouriteServiceImpl::prepareRecentRespones::Error processing individual card: ", e);
            }
        }
        
        extractAndSetSortedDueDates(sagaMappedRecentResponses, allRequestParams);

        List<Future<RecentResponse>> futureResponses  = StreamUtils.collectionToStream(sagaMappedRecentResponses)
                .map(sagaRecentResponse -> getExecutor()
                        .submit(EncryptionUtil.wrapCallableWithLTC(() -> recentResponseWorker.build(sagaRecentResponse, allRequestParams, getWidgetType()))))
                .collect(Collectors.toList());

        for (Future<RecentResponse> futureResponse : futureResponses) {
            try {
                if (Objects.nonNull(futureResponse)) {
                    RecentResponse recentResponse = futureResponse.get();
                    // skip smart recent according to configuration
                    if(recentResponse.getType().toLowerCase().startsWith("smart_recent") && recentResponse.getProduct() != null && !Utils.isSmartRecentsRecoEnabled(recentResponse.getProduct().getService(), getWidgetType())) {
                        continue;
                    }
                    recentResponse.setUniqueId(fetchUniqueId(recentResponse));
                    recentResponses.add(recentResponse);
                }
            } catch (InterruptedException ex) {
                Thread.currentThread().interrupt();
            } catch (ExecutionException ex) {
                log.error("FavouriteServiceImpl::prepareRecentRespones::InterruptedException while processing recent response {}", ex);
            } catch (Exception ex) {
                log.error("FavouriteServiceImpl::prepareRecentRespones::Error processing individual card: ", ex);
                metricsAgent.incrementEventCount(Constants.RECENT_RESPONSE_PROCESSING_ERROR,
                        Arrays.asList("error=" + ex.getClass().getSimpleName()));
            }
        }

        return recentResponses;
    }

    abstract protected ExecutorService getExecutor();

    private void updateRecentResponseWithAsyncVerifyFlags(RecentResponse recentResponse) {
        String productId = recentResponse.getProductId();
        String categoryId = String.valueOf(recentResponse.getProduct().getCategoryId());
        Map<String, Boolean> flags = getFlagsByProductId(productId, categoryId);
        if(!flags.isEmpty()) {
            if (flags.containsKey(IS_AMOUNT_EDITABLE_FLAG)) {
                recentResponse.setAmountEditable(flags.get(IS_AMOUNT_EDITABLE_FLAG));
            }
            if (flags.containsKey(IS_GROUP_DISPLAY_ENABLED_FLAG)) {
                recentResponse.setGroupDisplayEnabled(flags.get(IS_GROUP_DISPLAY_ENABLED_FLAG));
            }
        }
        if(Objects.isNull(recentResponse.getAmountEditable()) || Objects.isNull(recentResponse.getGroupDisplayEnabled())) {
            List<String> tagsList = getTagList(recentResponse.getAmountEditable(), recentResponse.getGroupDisplayEnabled(), categoryId, productId);
            metricsAgent.incrementEventCount("ASYNC_VERIFY_NULL_FLAGS_FOUND", tagsList);
        }
    }

    private Map<String, Boolean> getFlagsByProductId(String productId, String categoryId) {
        Map<String, Boolean> flags = new HashMap<>();
        try {
            if(Utils.isAsyncVerifyFlagUpdateAllowed(categoryId)) {
                Map<String,Boolean> data = aerospikeGenericWrapper.getData(BFF_NAMESPACE,
                        Constants.ASYNC_VERIFY_FLAGS_KEY, productId, 3000, Map.class);
                if(Objects.nonNull(data)) {
                    if(data.containsKey(AMOUNT))
                        flags.put(IS_AMOUNT_EDITABLE_FLAG, data.get(AMOUNT));
                    if(data.containsKey(GROUP))
                        flags.put(IS_GROUP_DISPLAY_ENABLED_FLAG, data.get(GROUP));
                }
                if(flags.isEmpty()) {
                    List<String> tagsList = new ArrayList<>();
                    tagsList.add(CATEGORY + categoryId);
                    tagsList.add(PRODUCT_METRIC_TAG  + productId);
                    metricsAgent.incrementEventCount("PID_NOT_FOUND_IN_AEROSPIKE", tagsList);
                }
                log.info("###### 13 : data : {}", data);
            }
        } catch (Exception e) {
            log.error("getFlagsByProductId :: AeroSpikeGenericWrapper :: getData is down/not responding", e);
        }
        return flags;
    }

    private List<String> getTagList(Boolean isAmountEditable, Boolean isGroupDisplayEnabled, String categoryId, String productId) {
        String flagTag;
        if(Objects.isNull(isAmountEditable) && Objects.isNull(isGroupDisplayEnabled)) {
            flagTag = "both";
        } else if (Objects.isNull(isAmountEditable)) {
            flagTag = AMOUNT;
        } else {
            flagTag = GROUP;
        }
        List<String> tagsList = new ArrayList<>();
        tagsList.add(CATEGORY + categoryId);
        tagsList.add(PRODUCT_METRIC_TAG + productId);
        tagsList.add(FLAG_METRIC_TAG + flagTag);
        return tagsList;
    }

    public void extractAndSetSortedDueDates(List<SagaRecentResponse> responses, Map<String, String> allRequestParams) {
        Long customerId = Long.parseLong(allRequestParams.get(Constants.CUSTOMER_ID));
        if(Utils.recoPriorityEnabled(customerId) && Objects.nonNull(allRequestParams.get(RECO_REQUEST_IDENTIFIER)) && TRUE.equalsIgnoreCase(allRequestParams.get(RECO_REQUEST_IDENTIFIER))) {
            List<Date> dueDates = new ArrayList<>();
            for (SagaRecentResponse response : responses) {
                if (response.getBill() != null && response.getBill().getBillDueDate() != null) {
                    Date dueDate = DateUtil.stringToDate(response.getBill().getBillDueDate(), DateFormat.DATE_TIME_FORMAT_2);
                    dueDates.add(dueDate);
                }
            }
            Collections.sort(dueDates);
            for (int i = 0; i < responses.size(); i++) {
                if (i < dueDates.size()) {
                    responses.get(i).setSortableDueDate(DateUtil.formatDate(dueDates.get(i), DateFormat.DATE_TIME_FORMAT_2));
                }
            }

        }
    }

}
