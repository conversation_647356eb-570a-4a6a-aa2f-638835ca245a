package com.paytm.recharges.bff.client;

import com.aerospike.client.*;
import com.aerospike.client.Record;
import com.aerospike.client.policy.BatchPolicy;
import com.aerospike.client.policy.Policy;
import com.aerospike.client.policy.WritePolicy;
import com.paytm.recharges.bff.monitor.MetricsAgent;
import com.paytm.recharges.bff.utils.AerospikeUtils;
import com.paytm.recharges.bff.utils.JsonUtil;
import com.paytm.recharges.custom_logger.service.CustomLogManager;
import com.paytm.recharges.custom_logger.service.CustomLogger;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
public class AerospikeGenericWrapper {

	private static final CustomLogger log = CustomLogManager.getLogger(AerospikeGenericWrapper.class);

	@Autowired
	private MetricsAgent metricsAgent;

	@Autowired
	private AerospikeClient aerospikeClient;

	public void putData(String namespace, String setName, String keyName, Integer expireTimeInSec, Integer timeoutInMs,
			Map<String, Object> data) {
		try {
			if(MapUtils.isEmpty(data)) {
				return;
			}

			List<Bin> bins = new LinkedList<>();
			data.forEach((key, val) -> {
				bins.add(AerospikeUtils.getSerialiazedBin(key, val));
			});

			if (CollectionUtils.isEmpty(bins)) {
				return;
			}

			Key key = new Key(namespace, setName, keyName);

			WritePolicy policy = new WritePolicy(aerospikeClient.writePolicyDefault);

			policy.expiration = expireTimeInSec;

			policy.setTimeout(timeoutInMs);

			Long startTime = System.currentTimeMillis();

			aerospikeClient.put(policy, key, bins.toArray(new Bin[] {}));

			metricsAgent.recordExecutionTimeOfEvent("aerospike_put", System.currentTimeMillis() - startTime);
		} catch (Exception ex) {
			log.error("AerospikeGenericWrapper :: putData Aerospike is down/not responding for set: {}, key: {} \n Error: {}", setName, keyName, ex);
			List<String> tagsList = new ArrayList<>();
			tagsList.add("set:" + setName);
			metricsAgent.incrementEventCount("block_req_error_in_save_cache", tagsList);
		}

	}

	public <T> T getData(String namespace, String setName, String keyName, Integer timeoutInMs, Class<T> responseClass) {
		try {
			Key key = new Key(namespace, setName, keyName);

			Policy readPolicy = aerospikeClient.readPolicyDefault;

			readPolicy.setTimeout(timeoutInMs);

			Long startTime = System.currentTimeMillis();

			Record record = aerospikeClient.get(aerospikeClient.readPolicyDefault, key);

			metricsAgent.recordExecutionTimeOfEvent("aerospike_get", System.currentTimeMillis() - startTime);

			if (Objects.nonNull(record)) {
				Map<String, Object> map = record.bins;
				if (MapUtils.isNotEmpty(map)) {
					return JsonUtil.fromObject(AerospikeUtils.getDeserializedBinMap(map), responseClass);
				} else {
					metricsAgent.incrementEventCount("block_req_not_found_in_cache");
				}
			}
			return null;
		} catch (Exception ex) {
			log.error("AerospikeGenericWrapper :: getData Aerospike is down/not responding for set: {}, key: {} \n Error: {}", setName, keyName, ex);
			List<String> tagsList = new ArrayList<>();
			tagsList.add("set:" + setName);
			metricsAgent.incrementEventCount("block_req_error_in_get_cache", tagsList);
		}
		return null;
	}

	/**
	 * Batch get data from Aerospike for multiple keys
	 * @param namespace Aerospike namespace
	 * @param setName Aerospike set name
	 * @param keyNames List of key names to fetch
	 * @param timeoutInMs Timeout in milliseconds
	 * @param responseClass Class type of the response
	 * @return Map of key to response object, empty map if no data found
	 * @param <T> Type of response object
	 */
	public <T> Map<String, T> getBatchData(String namespace, String setName, List<String> keyNames,
			Integer timeoutInMs, Class<T> responseClass) {
		Map<String, T> resultMap = new HashMap<>();
		
		try {
			if (CollectionUtils.isEmpty(keyNames)) {
				return resultMap;
			}

			// Create batch of Keys
			Key[] keys = keyNames.stream()
					.map(keyName -> new Key(namespace, setName, keyName))
					.toArray(Key[]::new);

			// Set batch policy with timeout
			BatchPolicy batchPolicy = new BatchPolicy(aerospikeClient.batchPolicyDefault);
			batchPolicy.setTimeout(timeoutInMs);

			Long startTime = System.currentTimeMillis();

			// Execute batch get
			Record[] records = aerospikeClient.get(batchPolicy, keys);

			metricsAgent.recordExecutionTimeOfEvent("aerospike_batch_get", 
					System.currentTimeMillis() - startTime);

			// Process results
			if (records != null) {
				for (int i = 0; i < records.length; i++) {
					Record record = records[i];
					String keyName = keyNames.get(i);
					
					if (record != null && record.bins != null && !record.bins.isEmpty()) {
						Map<String, Object> map = record.bins;
						if (MapUtils.isNotEmpty(map)) {
							T result = JsonUtil.fromObject(map, responseClass);
							if (result != null) {
								resultMap.put(keyName, result);
							}
						} else {
							metricsAgent.incrementEventCount("aerospike_req_not_found_in_cache");
							log.debug("Empty bins for key: {} in set: {}", keyName, setName);
						}
					}
				}
			}

			return resultMap;

		} catch (Exception ex) {
			String errorType;
			if(ex instanceof AerospikeException.Timeout) {
				errorType = "timeout";
				log.error("Timeout while fetching interstitial data from Aerospike: {}", ex.getMessage(), ex);
			} else {
				errorType = "unexpected";
				log.error("AerospikeGenericWrapper :: batchGetData Unexpected error for set: {}, keys: {} \n Error: {}",
					setName, keyNames, ex);
			}
			List<String> tagsList = new ArrayList<>(Arrays.asList(
					"set:" + setName,
					"error_type:" + errorType
			));
			metricsAgent.incrementEventCount("aerospike_req_error_in_batch_get_cache", tagsList);
			return null;

		}
	}

}
