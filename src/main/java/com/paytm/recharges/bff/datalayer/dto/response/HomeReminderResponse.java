package com.paytm.recharges.bff.datalayer.dto.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class HomeReminderResponse implements Serializable {
    @JsonProperty("id")
    private long id;
    @JsonProperty("filter")
    private String filter;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    @JsonProperty("view_id")
    private long viewId;
    @JsonProperty("bannerId")
    private String bannerId;
    @JsonIgnore
    private transient String reconId;
    @JsonIgnore
    private transient Boolean isBillDue;
    @JsonProperty("unique_id")
    private String uniqueId;
    @JsonProperty("item_key")
    private String itemKey;
    @JsonProperty("item_type")
    private String itemType;
    @JsonProperty("ga_category")
    private String gaCategory;
    @JsonProperty("long_subtitle")
    private String longSubtitle;
    @JsonProperty("subtitle")
    private String subtitle;
    @JsonProperty("sub_title_color")
    private String subTitleColor;
    @JsonProperty("image_url")
    private String operatorLogo;
    @JsonProperty("name")
    private String name;
    @JsonProperty("title")
    private String title;
    @JsonProperty("due_date")
    private String dueDate;
    @JsonProperty("sortable_due_date")
    private String sortableDueDate;
    @JsonProperty("ct_variant_id")
    private String ctVariantId;
    @JsonProperty("cta")
    private Object ctaObjects;
    @JsonProperty("dismiss_actions")
    private Object disMissActions;

    @JsonProperty("url")
    private String url;

    @JsonProperty("url_type")
    private String urlType;

//    @JsonProperty("reason")
//    private String reason;

    @JsonProperty("ct_campaign_id")
    private String ctCampaignId;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonProperty("metadata_source_id")
    private String metadataSourceId;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonProperty("alt_image_url")
    private String altImageUrl;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonProperty("slot_id")
    private String slotId;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonProperty("seourl")
    private String seourl;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonProperty("priority")
    private String priority = "100";

    @JsonProperty("layout")
    private Object itemLayout;
    @JsonIgnore
    private transient String billDate;
    @JsonIgnore
    private transient String service;
    @JsonIgnore
    private transient Double amount;
    @JsonIgnore
    private transient String rechargeNumber;
    @JsonIgnore
    private transient String paytype;
    private Boolean isPrepaid;
    @JsonProperty("subscriberDetails")
    private Object subscriberDetails;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonProperty("custom_properties")
    private Object customProperties;


    @Override
    public String toString() {
        return "HomeReminderResponse{" +
                "uniqueId='" + uniqueId + '\'' +
                '}';
    }
}
