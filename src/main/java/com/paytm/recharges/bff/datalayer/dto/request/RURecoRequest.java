package com.paytm.recharges.bff.datalayer.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class RURecoRequest {
     @JsonProperty("views")
     List<RURecoViewRequest> view;
     @JsonProperty("client")
     String client;
     @JsonProperty("version")
     String version;
     @JsonProperty("locale")
     String locale;
     @JsonProperty("customer_id")
     String customerId;
     @JsonProperty("request_id")
     String requestId;

}
