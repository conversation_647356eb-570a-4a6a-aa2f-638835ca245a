package com.paytm.recharges.bff.datalayer.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.paytm.recharges.bff.datalayer.dto.request.InterstitialRequest;
import lombok.Data;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DwhInterstitialPayload {
    @JsonProperty("interstitial_request")
    private InterstitialRequest interstitialRequest;
    
    @JsonProperty("success")
    private Boolean success;
    
    @JsonProperty("timestamp")
    private Long timestamp;

    @JsonProperty("cache_data")
    private List<CacheData> cacheData;
}

