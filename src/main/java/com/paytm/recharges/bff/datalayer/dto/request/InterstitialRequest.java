package com.paytm.recharges.bff.datalayer.dto.request;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class InterstitialRequest {
    private String rechargeNumber;
    private String operator;
    private Long customerId;
    private String payType;
    private String service;
    private Long productId;
    private String planBucket;
    private Double amount;
    private String circle;
    private String expiry;
    private String dueDate;
    private String referenceId;
    @JsonProperty("retry_count")
    private Integer retryCount;
}
