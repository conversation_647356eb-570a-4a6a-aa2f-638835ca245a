package com.paytm.recharges.bff.datalayer.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.paytm.recharges.bff.datalayer.dto.request.InterstitialRequest;
import lombok.Data;


@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class InterstitialRetryKafkaPayload {
    @JsonProperty("api_url")
    private String apiUrl;

    @JsonProperty("queryParams")
    private InterstitialRequest interstitialRequest;

    @JsonProperty("retry_count")
    private Integer retryCount;
}
