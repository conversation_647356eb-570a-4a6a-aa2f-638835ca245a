package com.paytm.recharges.bff.datalayer.dto.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.paytm.recharges.bff.enums.BillType;
import com.paytm.recharges.bff.enums.EventType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Setter
@Getter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class SagaRecentResponse implements Serializable {
    private static final long serialVersionUID = 7767505352454803884L;
    private String date;
    private String channel;
    private Long customerId;
    private Integer automaticState;
    private String automaticDate;
    private String txnDate;
    private String pid;
    private String categoryId;
    private String consumerName;
    @JsonProperty("cylinder_agency_name")
    private String cylinderAgencyName;
    @JsonProperty("billVisibilityDays")
    private int billVisibilityDays;
    private String nickName;
    private Boolean hasPaymentHistory;
    private Double amount;
    @JsonProperty("recharge_number_1")
    private String rechargeNumber1;
    @JsonProperty("recharge_number_2")
    private String rechargeNumber2;
    @JsonProperty("recharge_number_3")
    private String rechargeNumber3;

    @JsonProperty("recharge_number_4")
    private String rechargeNumber4;

    @JsonProperty("recharge_number_5")
    private String rechargeNumber5;

    @JsonProperty("recharge_number_6")
    private String rechargeNumber6;

    @JsonProperty("recharge_number_7")
    private String rechargeNumber7;

    @JsonProperty("recharge_number_8")
    private String rechargeNumber8;

    private EventType eventType;

    private Map<String, String> additionalInfo;

    private Long orderId;

    @JsonProperty("bill")
    private BillObject bill;

    private List<String> allowedServices;

    private String eventState;
    private String billState;

    private boolean reminderNotificationEnabled;

    private boolean notPaidOnPaytm;

    private boolean trackable;

    private boolean isTxnAutomatic;

    private Double txnAmount;

    private boolean isAutomaticActive;

    private Double lastRechargeAmount;

    private Object operatorRecentData;

    private Object operatorData ;

    private String markAsPaidDate;

    private String BillReminder;
    private String createdAt;

    private Double originalDueAmount;

    private Double automaticAmount;

    private Object rentTfData ;
    private Boolean earlyPayment;
    private Integer automaticSubscriptionId;
    private String issuingBankCardVariant;
    private ArrayList<Object> mediaAssets;
    private Map<String, Object> fastagExtraData;

    private Boolean showBBPSFlag;
    private Integer hasConsent;
    private String consentValidTill;
    private BillType billType;
    private String remindLaterDate;
    private Boolean remindLater;

    private Map<String, Object> ambiguous;
    private Map<String,String> subscriberDetails;

    private Boolean isDwhSmsParsingManual;
    private Boolean isFullBill;
    private Boolean groupDisplayEnabled;
    private Boolean amountEditable;

    private String oprBillGenDate;
    private String sortableDueDate;
    private Date operatorValidatedAt;
    private Integer isPaytmVPA;
    private Boolean isPrepaid;
    private String rentConsent;
    @JsonProperty("isDefaultAmount")
    private boolean isDefaultAmount;

}
