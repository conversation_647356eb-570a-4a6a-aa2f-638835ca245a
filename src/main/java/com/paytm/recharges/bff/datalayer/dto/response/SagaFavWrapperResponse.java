package com.paytm.recharges.bff.datalayer.dto.response;

import com.paytm.recharges.bff.datalayer.dto.InterstitialDataRecord;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SagaFavWrapperResponse {
    private List<HashMap<String, Object>> frequentOrderResponse;
    private Map<String, InterstitialDataRecord> impressionMap;
    private Integer eligibleInterstitial;
}
