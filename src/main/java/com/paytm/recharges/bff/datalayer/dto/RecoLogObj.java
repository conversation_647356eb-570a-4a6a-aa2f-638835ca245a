package com.paytm.recharges.bff.datalayer.dto;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class RecoLogObj {
    private Long customerId;
    private String reconId;
    private Long publishedTime;
    private String publishedDate;
    private String dueDate;
    private String billDate;
    private Double amount;
    private String rechargeNumber;
    private String service;
    private String paytype;
    private String sortableDueDate;
    private Integer interstitialSeenCount;
    private Boolean interstitialShown;

    public RecoLogObj(Long customerId, String reconId, Long publishedTime, String publishedDate,
                      String dueDate, String billDate, Double amount, String rechargeNumber, 
                      String service, String paytype, String sortableDueDate, 
                      Integer interstitialSeenCount, Boolean interstitialShown) {
        this.customerId = customerId;
        this.reconId = reconId;
        this.publishedTime = publishedTime;
        this.publishedDate = publishedDate;
        this.dueDate = dueDate;
        this.billDate = billDate;
        this.amount = amount;
        this.rechargeNumber = rechargeNumber;
        this.service = service;
        this.paytype = paytype;
        this.sortableDueDate = sortableDueDate;
        this.interstitialSeenCount = interstitialSeenCount;
        this.interstitialShown = interstitialShown;
    }

    @Override
    public String toString() {
        return String.join(",", 
            String.valueOf(customerId), 
            reconId, 
            String.valueOf(publishedTime), 
            publishedDate, 
            rechargeNumber, 
            billDate, 
            dueDate, 
            String.valueOf(amount), 
            service, 
            paytype, 
            sortableDueDate,
            String.valueOf(interstitialSeenCount),
            String.valueOf(interstitialShown)
        );
    }

}
