package com.paytm.recharges.bff.datalayer.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
public class InterstitialDataRecord {

    @JsonProperty("seenCount")
    private Integer seenCount;

    public InterstitialDataRecord() {
        this.seenCount = 0;
    }

    @Override
    public String toString() {
        return "InterstitialDataRecord{" +
                "seenCount=" + seenCount +
                '}';
    }
}
