fulfillment:
  recharge:
    #dns: http://internal-billpayments-ffrecharge-alb-**********.ap-south-1.elb.amazonaws.com
    dns : http://ffrblue-beta.prod.paytmdgt.io
    validate: /v1/recharge/validate
    validateConfig:
      max_connection: 3000
      connect_timeout: 10000
      read_timeout: 58000
      request_timeout: 58000
recharge:
  express:
    dns: http://expressinternal.prod.paytmdgt.io
    hostName: expressinternal.prod.paytmdgt.io
    verify: v1/expressrecharge/verify
    checkout: v1/expressrecharge/checkout
    deleteFromAllSystems: v1/expressrecharge/deletefromallsystems
  saga:
    dns: http://rechargesaga-beta.paytmdgt.io
    donotRemindme: /api/dropoffreminderstatus
    markAsPaid: /api/markAsPaid
    removePgToken: /api/pg/removeToken
    hostName: rechargesaga-beta.paytmdgt.io
  reminder:
    dns: http://digitalreminder.prod.paytmdgt.io
    donotRemindme: /v2/bill/notificationStatus
    deleteFromReminder: /v1/deleteRecord
    deleteSmsParsedBills: /v1/bill-nonPaytm/notificationStatus
    setRemindMeLater: /v1/setRemindLater
    hostName: digitalreminder.prod.paytmdgt.io
  ct:
    dns: https://in1-paytm-private.api.clevertap-internal.io
    allowedCatForDisablePayMode: 131865
    endpoint:
      pull: pull
    config:
      thread_timeout: 100
      minPoolSize: 1000
      maxPoolSize: 2000
      queueSize: 10000
      keepAlive: 60
      http:
        max_connection: 1000
        connect_timeout: 500
        read_timeout: 200
        request_timeout: 200

rps:
  dns: http://digitalcatalog-internal.paytmdgt.io
  plans: rps/v1/plans/7166/search
  config:
    minPoolSize: 10
    maxPoolSize: 20
    queueSize: 500
    keepAlive: 60
  http:
    max_connection: 100
    connect_timeout: 500
    read_timeout: 18000
    request_timeout: 500


cart:
  hostUri: http://checkout-internal.paytm.com
  key: recharges
spring:
  profile:
    active: production
  # MySql Database Configuration
  datasource:
    slave:
      url: *********************************************************
      username: fs_appuser
      driver-class-name: com.mysql.cj.jdbc.Driver
      maximum-pool-size: 10
      minimum-idle: 5
      idle-timeout: 30000
      max-lifetime: 600000
      connection-timeout: 20000
    master:
      url: **********************************************************
      username: fs_appuser
      driver-class-name: com.mysql.cj.jdbc.Driver
      maximum-pool-size: 10
      minimum-idle: 5
      idle-timeout: 30000
      max-lifetime: 600000
      connection-timeout: 20000
express:
  ottPid: *********
  rechargesFulfillmentServiceId: 3,6,24,105
  automatic_pid: *********
  ott_service_name: "subscriptions"
  subscription_frequency: 1
  subscription_frequency_unit_onDemand: "ONDEMAND"
  subscription_frequency_unit_day: "DAY"
  subscription_grace_days: "0"
  subscription_retry_count: "2"
  subscription_max_amount: 15000
  subscription_payment_modes_disabled: BALANCE,NET_BANKING,PPBL
urls:
  frequentOrders: http://favourites.prod.paytmdgt.io
  updateRecent: http://recents-crud.prod.paytmdgt.io
  #fulfillmentRecharge: http://internal-billpayments-ffrecharge-alb-**********.ap-south-1.elb.amazonaws.com
  fulfillmentRecharge : http://ffrblue-beta.prod.paytmdgt.io
  removeRecent:  http://favourites.prod.paytmdgt.io
  fetchCvrDataUrl: http://digitalruleengine-internal.prod.paytmdgt.io/v1/offloadcpu/data?key=cvrDataLib&pageSize=1000&pageNumber=
localisation:
  service: DIGITALRECHARGE
  language: all
  environment: production-mb
  reloadInterval: 20
recentLocalisation:
  service: FAVOURITE
  language: all
  environment: production-mb
  reloadInterval: 20
scheduler:
  cvrReloadInterval: PT15M
recharges:
  category_map: '{"207075": "fastag", "18": "dth", "166690": "cylinder", "26": "electricity","17": "mobile", "21": "mobile","75505":"default","68869":"default","64739":"default","78640":"default","37217":"default","262072": "rent","289829": "tutionfee"}'
  display_summary:
    electricity: '[{"keys":["Due Date"], "priority": 0}, {"keys": ["Bill Date"], "priority": 1}, {"keys": ["Consumer Name"], "priority": 2}, {"keys": ["First Name", "Last Name"], "priority": 3, "label": "Consumer Name"}]'
    cylinder: '[{"keys":["Delivery Address"], "priority": 0}, {"keys":["Gas Agency"], "priority": 1}, {"keys":["Agency Name"], "priority": 1}, {"keys":["Booking Date"], "priority": 2}]'
    dth: '[{"keys":["Due Date"], "priority": 0}]'
    fastag: '[{"keys":["Wallet balance"], "priority": 0}, {"keys":["Wallet Balance"], "priority": 0}, {"keys":["wallet balance"], "priority": 0}, {"keys":["WALLET BALANCE"], "priority": 0}]'
  meta_data:
    copy_keys: "Consumer Name"
  express:
    http:
      max_connection: 2000
      connect_timeout: 10000
      read_timeout: 58000
      request_timeout: 58000
      threadTimeout: 60000
    executor:
      verify:
        minPoolSize: 1000
        maxPoolSize: 3000
        queueSize: 5000
        keepAlive: 60
      checkout:
        minPoolSize: 500
        maxPoolSize: 2000
        queueSize: 5000
        keepAlive: 60
  cron:
    http:
      max_connection: 10
      connect_timeout: 10000
      read_timeout: 50000
      request_timeout: 50000
ruleEngine:
  host: http://digitalruleengine-internal.prod.paytmdgt.io
  endpoints:
    offloadcpuData: /v1/offloadcpu/data


saga:
  dns: http://rechargesaga-internal.paytmdgt.io
  endpoint:
    recents: /api/customer/{customer_id}/recents
    smsCards: /api/customer/{customer_id}/newCards
    recentsV2: /api/v2/customer/{customer_id}/recents
    deleteFromAllSystems: api/dropoffreminderstatus
    customerName: /api/customername
    removeRecentsSaga: /smartreminder/removerecent
    deleteCIR: /deleteCIR
    getBankNames: /getBankNames
    removePgToken: /api/pg/removeToken
    createrecents: /create/recent
    setRemindLater: /setRemindLater
  config:
    http:
      max_connection: 500
      connect_timeout: 500
      thread_timeout: 1000
      minPoolSize: 1000
      maxPoolSize: 2000
      keepAlive: 60
      queueSize: 10000
      read_timeout: 300
      request_timeout: 500
  savedCard:
    config:
      http:
        max_connection: 500
        connect_timeout: 500
        read_timeout: 400
        request_timeout: 500
  dropOff:
    config:
      http:
        max_connection: 100
        connect_timeout: 1000
        read_timeout: 1000
        request_timeout: 1000
  remove_recent:
    config:
      http:
        max_connection: 10
        connect_timeout: 1000
        read_timeout: 1000
        request_timeout: 1000
        thread_timeout: 1000
        minPoolSize: 1000
        maxPoolSize: 2000
        keepAlive: 60
        queueSize: 10000
  customername:
    config:
      http:
        max_connection: 500
        connect_timeout: 50
        read_timeout: 15
        request_timeout: 100
        thread_timeout: 150
  removeRecents:
    config:
      thread_timeout: 500
  executor:
    customername:
      minPoolSize: 100
      maxPoolSize: 3000
      queueSize: 5000
      keepAlive: 60
    recent:
      minPoolSize: 400
      maxPoolSize: 600
      queueSize: 50
      keepAlive: 60

subscription:
  dns: http://subscription-internal.paytmdgt.io
  endpoint:
    deleteSubscription: /v1/s2s/subscriber/statusupdate
  config:
    http:
      max_connection: 500
      connect_timeout: 500
      read_timeout: 100
      request_timeout: 1500
      thread_timeout: 1000
      minPoolSize: 1000
      maxPoolSize: 2000
      keepAlive: 60
      queueSize: 10000

biller:
  dns: http://biller-service-internal.paytmdgt.io
  endpoint:
    deleteBiller: /v1/deleteBiller
  config:
    http:
      max_connection: 500
      connect_timeout: 500
      read_timeout: 150
      request_timeout: 1500
      thread_timeout: 1000
      minPoolSize: 1000
      maxPoolSize: 2000
      keepAlive: 60
      queueSize: 10000

smsBills:
  config:
    http:
      max_connection: 500
      connect_timeout: 500
      read_timeout: 150
      request_timeout: 1500
      thread_timeout: 1000
      minPoolSize: 1000
      maxPoolSize: 2000
      keepAlive: 60
      queueSize: 10000

favourite:
  frequentOrder:
    config:
      connection_timeout: 1000
      read_timeout: 1200
      max_connections: 1500
      thread_timeout: 2000
      minPoolSize: 200
      maxPoolSize: 400
      queueSize: 50
      keepAlive: 60
      priority: 1
      BILLS_EXPIRY_DAYS: 3
      prioritys:  '{"RENEW_AUTOMATIC":1,"RECHARGE_CANCEL" : 1,"RECHARGE_FAILURE" : 1,"DROPOFF" : 1,"NEW_ACCOUNT" : 1,"RECHARGE_AUTOMATIC_PENDING" : 1,"RECHARGE_AUTOMATIC_CANCEL" : 1,"RECHARGE_AUTOMATIC_FAILURE" : 1,"DROP_OFF_SUCCESS" : 1,"RECHARGE_PENDING" : 2, "RECENT" : 3, "SMART_RECENT_VALIDATION": 1, "SMART_RECENT_DROPOFF": 1}'
      stateMapping: '{"DROP_OFF_SUCCESS" : "drop_off","RECHARGE_PENDING":"recharge_pending","RECHARGE_SUCCESS" : "recharge_success","RECHARGE_AUTOMATIC_SUCCESS" : "recharge_success","RECHARGE_AUTOMATIC_CANCEL" : "recharge_cancel","RECHARGE_CANCEL" : "recharge_cancel", "RECHARGE_FAILURE" : "recharge_failure",
          "RECHARGE_AUTOMATIC_PENDING":"recharge_pending","RECHARGE_AUTOMATIC_FAILURE":"recharge_failure","NEW_ACCOUNT" : "new_account", "SMS_CARD" : "smsCard", "SMART_RECENT_VALIDATION": "smart_recent_validation", "SMART_RECENT_DROPOFF": "smart_recent_dropoff","SMS_CARD_NO_AMOUNT" : "smsCard","UPI_CARD" : "upiCard","RECHARGE_FAILURE_CONSENT_PENDING" : "recharge_failure_consent_pending","RECHARGE_FAILURE_NO_CONSENT" : "recharge_failure_no_consent","RECHARGE_PENDING_CONSENT_PENDING" : "recharge_pending_consent_pending","RECHARGE_PENDING_NO_CONSENT" : "recharge_pending_no_consent","OLD_BILLER" : "old_biller_recents", "NEW_ACCOUNT_BROWSE_PLAN" : "account_browse_plan"}'
      paytypeMap: '{"postpaid" : "Pay Bill","creditcard" : "Pay Bill","feepayment" : "Pay Now","newregistratiion" : "Pay Now", "prepaid" : "Recharge"}'
    includeOperator_key_services: dth
  recent:
    minPoolSize: 100
    maxPoolSize: 3000
    queueSize: 5000
    keepAlive: 60
  customername:
    minPoolSize: 100
    maxPoolSize: 3000
    queueSize: 5000
    keepAlive: 60
  frequentOrderV1:
    config:
      max_connection: 500
      connect_timeout: 500
      read_timeout: 800
      request_timeout: 1500
      thread_timeout: 1000
      minPoolSize: 1000
      maxPoolSize: 2000
      keepAlive: 60
      queueSize: 10000
  frequentRecentOrder:
    config:
      max_connections: 100
      connection_timeout: 100000
      read_timeout: 100000
      thread_timeout: 200000
      pool_size: 20000
      minPoolSize: 1000
      maxPoolSize: 2000
      keepAlive: 60
      queueSize: 10000
  personalisation:
    thread_timeout: 20000

ccWidget:
  executor:
    minPoolSize: 100
    maxPoolSize: 200
    queueSize: 50
    keepAlive: 60
    summary_thread_timeout: 500
    change_thread_timeout: 10000




favouritedelete:
  frequentOrder:
    config:
      connection_timeout: 1000
      read_timeout: 1000
      max_connections: 1000
      thread_timeout: 2000
      minPoolSize: 1000
      maxPoolSize: 2000
      queueSize: 10000
      keepAlive: 60


rental:
  host: 'https://securegw.paytm.in'
  fetchCardIndexNoPath: /theia/api/v1/fetchCardIndexNo
  issuerKey: 'ts'
  algo: 'HS256'
  categoryIds: 262072,289829,290706,291470
  http:
    max_connection: 10
    connect_timeout: 10000
    read_timeout: 60000
    request_timeout: 60000

risk:
  categoryIds: 262072,289829,291470


homereminders:
  algo: 'HS256'
  issuerKey: 'ts'
sms:
  dns: http://rechargesaga-internal.paytmdgt.io
  endpoint:
    smsCards: /api/customer/{customer_id}/newcards
  config:
    http:
      max_connection: 500
      connect_timeout: 500
      read_timeout: 800
      request_timeout: 1500
  card:
    executor:
      minPoolSize: 100
      maxPoolSize: 200
      queueSize: 50
      keepAlive: 60

bff:
  cloud:
    vault:
      authentication: approle
      uri: https://vault.paytmmall.io
      app-role:
        role-id: 6077c985-35aa-63ac-c810-df17b580fdd3
        secret-id: fe268291-ff11-0d81-4308-edee168b68c6
      kv:
        enabled: true
        backend: kv
        default-context: kv/digital/recharges/recharge-bff/secrets
        application-name: kv/digital/recharges/recharge-bff/secrets
      generic:
        enabled: false
      fail-fast: true
  config:
    blockReq:
      corepoolsize: 10
      maxpoolsize: 100
      queuecapacity: 20


deleteFromAllSystems:
  creditCardCategoryIds: 156705
  pg_thread_timeout: 2000
  recent_thread_timeout: 2000
  reminder_thread_timeout: 2000
  saga_thread_timeout: 2000
  subscription_thread_timeout: 2000
  biller_thread_timeout: 2000
  cir_thread_timeout: 1000

pg:
  dns: https://secure.paytm.in
  endpoint:
    deleteSavedCard: savedcardservice/HANDLER_INTERNAL/DEL_BIN
  config:
    http:
      max_connection: 1000
      connect_timeout: 500
      read_timeout:  1000
      request_timeout: 1000

ct:
  dns: https://in1-paytm-private.api.clevertap-internal.io
  allowedCatForDisablePayMode: 131865
  endpoint:
    pull: pull
  config:
    thread_timeout: 20000
    minPoolSize: 50
    maxPoolSize: 2000
    queueSize: 500
    keepAlive: 60
    http:
      max_connection: 10
      connect_timeout: 5000
      read_timeout: 10000
      request_timeout: 10000

lending:
  dns: https://services-internal.lending.paytm.com
  endpoint:
    bureauReport: lending-bureau/internal/user/bureauDetails
    bureauPull: lending-bureau/service/v1/bureau/credit-report
    cirChange: lending-bureau/service/v1/bureau/cir-changes
    creditSummary: lending-bureau/service/v1/bureau/credit-summary
  config:
    http:
      max_connection: 10
      connect_timeout: 5000
      read_timeout: 10000
      request_timeout: 10000
    thread_timeout: 2000

tnc:
  dns: https://cif-int.paytm.com
  endpoint: /kyc/tnc/user
  executor:
    minPoolSize: 5
    maxPoolSize: 20
    queueSize: 20
    keepAlive: 60
  http:
    max_connection: 10
    connect_timeout: 5000
    read_timeout: 200
    request_timeout: 10000

policyruleengine:
  secretKey : ###
  issuerKey: 'policy engine'
  client: "recharges-bff"
  role: "rule-admin"
  algo: 'HS256'
  dns: http://policy-engine.prod.paytmdgt.io
  endpoint:
    fee: rule/apply/fees
  config:
    thread_timeout: 250
    minPoolSize: 500
    maxPoolSize: 2000
    queueSize: 5000
    keepAlive: 60
    http:
      max_connection: 500
      connect_timeout: 100
      read_timeout: 200
      request_timeout: 210

resilience4j:
  circuitbreaker:
    instances:
      ctPullService:
        registerHealthIndicator: true
        eventConsumerBufferSize: 10
        automaticTransitionFromOpenToHalfOpenEnabled: true
        failureRateThreshold: 50
        minimumNumberOfCalls: 10
        permittedNumberOfCallsInHalfOpenState: 3
        slidingWindowSize: 10
        waitDurationInOpenState: 700ms
        slidingWindowType: COUNT_BASED

      preFeeService:
        registerHealthIndicator: true
        eventConsumerBufferSize: 10
        automaticTransitionFromOpenToHalfOpenEnabled: true
        failureRateThreshold: 80
        minimumNumberOfCalls: 10
        permittedNumberOfCallsInHalfOpenState: 3
        slidingWindowSize: 10
        waitDurationInOpenState: 700ms
        slidingWindowType: COUNT_BASED


management:
  health:
    circuitbreakers:
      enabled: true
  endpoints:
    web:
      exposure:
        include: health
  endpoint:
    health:
      show-details: always
  tracing:
    enabled: true
  defaults:
    metrics:
      export:
        enabled: true

aerospike:
  host: ***********
  port: 3000


resilience4j.circuitbreaker.configs.default.registerHealthIndicator: true
management.endpoints.web.expose: '*'
management.endpoint.health.enabled: true
management.endpoint.metrics.enabled: true
management.metrics.enable.resilience4j.circuitbreaker.calls: true
management.health.circuitbreakers.enabled: true
management.endpoints.web.exposure.include: '*'
management.endpoint.health.show-details: always
management.health.diskspace.enabled: false
management.health.ratelimiters.enabled: false
management.metrics.tags.application: recharges-bff
management.metrics.distribution.percentiles-histogram.http.server.requests: true
management.metrics.distribution.percentiles-histogram.resilience4j.circuitbreaker.calls: true

shortnerapi:
  dns: http://smarturl-internal.paytm.com
  endpoint:
    shorten: /v2/shorten
  config:
    http:
      max_connection: 1000
      connect_timeout: 500
      read_timeout: 15
      request_timeout: 1800

nickname:
  update:
    executor:
      minPoolSize: 100
      maxPoolSize: 200
      queueSize: 50
      keepAlive: 60
recent:
  response:
    executor:
      minPoolSize: 200
      maxPoolSize: 400
      queueSize: 100
      keepAlive: 60

hr:
  response:
    executor:
      minPoolSize: 100
      maxPoolSize: 200
      queueSize: 50
      keepAlive: 60
saved:
  card:
    executor:
      minPoolSize: 100
      maxPoolSize: 200
      queueSize: 50
      keepAlive: 60
etag:
  validate: true


mnp:
  dns: http://mnp-internal.prod.paytmdgt.io
  endpoint:
    fetchCorrectOperatorDetails: v1/mobile/getopcirclebyrange
  config:
    http:
      max_connection: 1000
      connect_timeout: 500
      read_timeout: 1800
      request_timeout: 1800
      thread_timeout: 2000
      minPoolSize: 150
      maxPoolSize: 150
      keepAlive: 60

cir:
  executor:
    minPoolSize: 5
    maxPoolSize: 20
    queueSize: 20
    keepAlive: 60
    get_thread_timeout: 500
    pull_thread_timeout: 10000

homepage:
  executor:
    minPoolSize: 100
    maxPoolSize: 200
    queueSize: 50
    keepAlive: 60

rule:
  service:
    dns: http://recharges-rule-processor.prod.paytmdgt.io
    personalisation: /api/personalization
    bottomNavPersonalisation: /api/bottomNav/personalization
    http:
      max_connection: 100
      connect_timeout: 5000
      read_timeout: 25
      request_timeout: 1000

ruleProcessor:
  executor:
    minPoolSize: 100
    maxPoolSize: 200
    queueSize: 50
    keepAlive: 60

planValidity:
  service:
    dns: http://digital-services.prod.paytmdgt.io
    planValidityUrl: /pds/v1/planvalidity
    http:
      max_connection : 50
      connect_timeout : 500
      read_timeout : 500
      request_timeout : 500
fastag:
  dns: https://mmv-service.paytm.com
  vehicledetails: api/v1/mmv/fetchVehicleDetails
  passcode: d124bda2-b712-4f4e-ad10-a2711dd0f428
  config:
    minPoolSize: 10
    maxPoolSize: 20
    queueSize: 500
    keepAlive: 60
  http:
    max_connection: 100
    connect_timeout: 5000
    read_timeout: 3500
    request_timeout: 3000
  color:
    cutoff: 90
  async:
    config:
      minPoolSize: 30
      maxPoolSize: 60
      queueSize: 500
      keepAlive: 30

remindMeLater:
  config:
    http:
      max_connection: 100
      connect_timeout: 1000
      read_timeout: 1000
      request_timeout: 1000
log:
  rotation:
    script:
      resourceName: log-rotation-prod-script.py

consent:
  kafka:
    producer:
      bootstrap-server: **********:9092,***********:9092,***********:9092
      topic-name: REMINDER_BILL_CONSENT
  executor:
    minPoolSize: 750
    maxPoolSize: 1250
    queueSize: 500
    keepAlive: 60
    thread_timeout: 10000
  service.http:
    max_connection: 1000
    connect_timeout: 5000
    read_timeout: 10000
    request_timeout: 10000
    bulk.request_timeout: 10000
    thread_timeout: 10000
  dns: http://digital-recharges-consent-service.prod.paytmdgt.io
  get.consent.endpoint: /v1/consent/api/getConsent
  create.consent.endpoint: /v1/consent/api/createOrUpdateConsent
  create.bulk.consent.endpoint: /v1/consent/api/createOrUpdateBulkConsent

dcat:
  dns: https://digitalcatalog.paytm.com
  planMapping: dcat/v1/browseplans/plan-mapping
  config:
    minPoolSize: 10
    maxPoolSize: 20
    queueSize: 500
    keepAlive: 60
  http:
    max_connection: 100
    connect_timeout: 500
    read_timeout: 2000
    request_timeout: 500

interstitial:
  executor:
    thread_timeout: 10
    minPoolSize: 25
    maxPoolSize: 2000
    queueSize: 100
    keepAlive: 60
  kafka:
    bootstrapServers: 10.4.33.171:9092,10.4.33.148:9092,10.4.33.39:9092
    topicName15sRetry: interstitial_impression_retry_15s
    topicName30mRetry: interstitial_impression_retry_30min
    topicNameDwh: interstitial_impression_dwh
