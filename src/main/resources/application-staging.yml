fulfillment:
  recharge:
    dns: https://recharge-staging.paytm.com
    validate: /v1/recharge/validate
recharge:
  express:
    dns: http://**********
    hostName: digitalapiproxy-staging.paytm.com
    verify: v1/expressrecharge/verify
    checkout: v1/expressrecharge/checkout
    deleteFromAllSystems: v1/expressrecharge/deletefromallsystems
  saga:
    dns: http://favouritenode.nonprod.onus.paytmdgt.io
    donotRemindme: /api/dropoffreminderstatus
    markAsPaid: /api/markAsPaid
    removePgToken: /api/pg/removeToken
    hostName: **********
  reminder:
    dns: http://***********
    donotRemindme: /v2/bill/notificationStatus
    markAsPaid: /api/markAsPaid
    deleteFromReminder: /v1/deleteRecord
    deleteSmsParsedBills: bills/v1/bill-nonPaytm/notificationStatus
    setRemindMeLater: /v1/setRemindLater
    hostName: **********
  ct:
    dns: https://in1-paytm.api.clevertap.com
    endpoint:
      pull: pull
    config:
      thread_timeout: 100
      minPoolSize: 1000
      maxPoolSize: 2000
      queueSize: 10000
      keepAlive: 60
      http:
        max_connection: 1000
        connect_timeout: 500
        read_timeout: 200
        request_timeout: 200

  policyruleengine:
    secretKey: u7w!z%C*F-JaNdRgUkXp2s5v8y/A?D(G
    issuerKey: 'policy engine'
    client: "recharges-bff"
    role: "rule-admin"
    algo: 'HS256'
    dns: http://policy-rule-enginespringboot.nonprod.onus.paytmdgt.io/rule
    endpoint:
      fee: /apply/fees
    config:
      thread_timeout: 20000
      minPoolSize: 50
      maxPoolSize: 2000
      queueSize: 500
      keepAlive: 60
      http:
        max_connection: 10
        connect_timeout: 5000
        read_timeout: 10000
        request_timeout: 10000

rps:
  dns: http://digitalcatalog-internal.paytmdgt.io
  plans: rps/v1/plans/7166/search
  config:
    minPoolSize: 10
    maxPoolSize: 20
    queueSize: 500
    keepAlive: 60
  http:
    max_connection: 100
    connect_timeout: 500
    read_timeout: 2000
    request_timeout: 500

dcat:
  dns: https://digitalcatalog-staging.paytm.com
  planMapping: dcat/v1/browseplans/plan-mapping
  config:
    minPoolSize: 10
    maxPoolSize: 20
    queueSize: 500
    keepAlive: 60
  http:
    max_connection: 100
    connect_timeout: 500
    read_timeout: 2000
    request_timeout: 500


frecharge:
  express:
    verify: /digitalrecharge/v1/expressrecharge/verify
cart:
  hostUri: https://checkout-staging.paytm.com
  key: recharges
  secret: ###
express:
  ottPid: **********
  rechargesFulfillmentServiceId: 3,6,24,105
  automatic_pid: **********
  ott_service_name: "subscriptions"
  subscription_frequency: 1
  subscription_frequency_unit: "ONDEMAND"
  subscription_grace_days: "0"
  subscription_retry_count: "2"
  subscription_max_amount: 15000
  subscription_frequency_unit_onDemand: "ONDEMAND"
  subscription_frequency_unit_day: "DAY"
  subscription_payment_modes_disabled: BALANCE,NET_BANKING,PPBL
localisation:
  service: DIGITALRECHARGE
  language: all
  environment: staging
recentLocalisation:
  service: FAVOURITE
  language: all
  environment: staging
  reloadInterval: 10
urls:
  fulfillmentRecharge:
  fetchCvrData: http://**********/v1/offloadcpu/data?key=cvrDataLib&pageSize=25000&pageNumber=
  frequentOrders: http://***********
  updateRecent: http://***********
  removeRecent: http://***********

scheduler:
  cvrReloadInterval: PT1800S

timeout:
  frequentOrdersHttpRequest: 60
  
ruleEngine:
  host: https://digitalapiproxy-staging.paytm.com/digitalrecharge/
  endpoints:
    offloadcpuData: v1/offloadcpu/data

ruleEngineActiveInactive:
  host: https://digitalapiproxy-staging.paytm.com/digitalrecharge/

rental:
  host: 'https://securegw-stage.paytm.in'
  fetchCardIndexNoPath: /theia/api/v1/fetchCardIndexNo
  secretKey: ###
  issuerKey: 'ts'
  algo: 'HS256'
  categoryIds: 215903,292581,300048,304004
  http:
      max_connection: 10
      connect_timeout: 10000
      read_timeout: 60000
      request_timeout: 60000

risk:
  categoryIds: 215903,292581,304004

favourite:
  frequentOrder:
    config:
      connection_timeout: 1000
      read_timeout: 1200
      max_connections: 1500
      thread_timeout: 2000
      minPoolSize: 1000
      maxPoolSize: 2000
      queueSize: 10000
      keepAlive: 60
      priority: 1
      pool_size: 20000
      BILLS_EXPIRY_DAYS: 3
      prioritys:  '{"RENEW_AUTOMATIC":1,"RECHARGE_CANCEL" : 1,"RECHARGE_FAILURE" : 1,"DROPOFF" : 1,"RECHARGE_AUTOMATIC_PENDING" : 1,"RECHARGE_AUTOMATIC_CANCEL" : 1,"RECHARGE_AUTOMATIC_FAILURE" : 1,"DROP_OFF_SUCCESS" : 1,"RECHARGE_PENDING" : 2, "RECENT" : 3, "SMART_RECENT_VALIDATION": 1, "SMART_RECENT_DROPOFF": 1}'
      stateMapping: '{"DROP_OFF_SUCCESS" : "drop_off","RECHARGE_SUCCESS" : "recharge_success","RECHARGE_AUTOMATIC_CANCEL" : "recharge_cancel","RECHARGE_AUTOMATIC_SUCCESS" : "recharge_success","RECHARGE_CANCEL" : "recharge_cancel", "RECHARGE_FAILURE" : "recharge_failure",
                "RECHARGE_AUTOMATIC_PENDING":"recharge_pending","RECHARGE_PENDING":"recharge_pending","RECHARGE_AUTOMATIC_FAILURE":"recharge_failure","NEW_ACCOUNT" : "new_account", "SMS_CARD" : "smsCard", "UPI_CARD" : "upiCard", "SMART_RECENT_VALIDATION": "smart_recent_validation", "SMART_RECENT_DROPOFF": "smart_recent_dropoff","SMS_CARD_NO_AMOUNT" : "smsCard","OLD_BILLER" : "old_biller_recents","NEW_ACCOUNT_BROWSE_PLAN" : "account_browse_plan"}'
      paytypeMap: '{"postpaid" : "Pay Bill","creditcard" : "Pay Bill","feepayment" : "Pay Now","newregistratiion" : "Pay Now", "prepaid" : "Recharge"}'
    includeOperator_key_services: dth
  frequentOrderV1:
    config:
      max_connection: 500
      connect_timeout: 500
      read_timeout: 800
      request_timeout: 1500
      thread_timeout: 1000
      minPoolSize: 1000
      maxPoolSize: 2000
      keepAlive: 60
      queueSize: 10000


sms:
  dns: http://poccassandra.nonprod.onus.paytmdgt.io
  endpoint:
    smsCards: /api/customer/{customer_id}/newcards
  config:
    http:
      max_connection: 500
      connect_timeout: 500
      read_timeout: 800
      request_timeout: 1500

favouritedelete:
  frequentOrder:
    config:
      connection_timeout: 1000
      read_timeout: 1000
      max_connections: 1000
      thread_timeout: 2000
      minPoolSize: 1000
      maxPoolSize: 2000
      queueSize: 10000
      keepAlive: 60

saga:
  dns: http://poccassandra.nonprod.onus.paytmdgt.io/
  endpoint:
    recents: /api/customer/{customer_id}/recents
    smsCards: /api/customer/{customer_id}/newCards
    recentsV2: /api/v2/customer/{customer_id}/recents
    deleteFromAllSystems: api/dropoffreminderstatus
    customerName: /api/customername
    removeRecentsSaga: /smartreminder/removerecent
    deleteCIR: /deleteCIR
    getBankNames: /getBankNames
    removePgToken: /api/pg/removeToken
    createrecents: create/recent
  config:
    http:
      max_connection: 500
      connect_timeout: 500
      read_timeout: 800
      request_timeout: 1500
      thread_timeout: 1000
      minPoolSize: 1000
      maxPoolSize: 2000
      keepAlive: 60
      queueSize: 10000
  savedCard:
    config:
      http:
        max_connection: 500
        connect_timeout: 5000
        read_timeout: 80000
        request_timeout: 150000
  remove_recent:
    config:
      http:
        max_connection: 10
        connect_timeout: 1000
        read_timeout: 1000
        request_timeout: 1000
        thread_timeout: 1000
        minPoolSize: 1000
        maxPoolSize: 2000
        keepAlive: 60
        queueSize: 10000
  dropOff:
    config:
      http:
        max_connection: 10
        connect_timeout: 5000
        read_timeout: 10000
        request_timeout: 10000
  removeRecents:
    config:
      thread_timeout: 1000
  executor:
    verify:
      minPoolSize: 100
      maxPoolSize: 3000
      queueSize: 5000
      keepAlive: 60
    checkout:
      minPoolSize: 50
      maxPoolSize: 2000
      queueSize: 5000
      keepAlive: 60

subscription:
  dns: http://subscribenode.nonprod.onus.paytmdgt.io
  endpoint:
    deleteSubscription: /v1/s2s/subscriber/statusupdate
  config:
    http:
      max_connection: 500
      connect_timeout: 500
      read_timeout: 800
      request_timeout: 1500
      thread_timeout: 1000
      minPoolSize: 1000
      maxPoolSize: 2000
      keepAlive: 60
      queueSize: 10000

biller:
  dns: https://digitalapiproxy-staging.paytm.com
  endpoint:
    deleteBiller: /billerservice/auth/v1/deleteBiller
  config:
    http:
      max_connection: 500
      connect_timeout: 500
      read_timeout: 800
      request_timeout: 1500
      thread_timeout: 1000
      minPoolSize: 1000
      maxPoolSize: 2000
      keepAlive: 60
      queueSize: 10000

smsBills:
  config:
    http:
      max_connection: 500
      connect_timeout: 500
      read_timeout: 100
      request_timeout: 1500
      thread_timeout: 1000
      minPoolSize: 1000
      maxPoolSize: 2000
      keepAlive: 60
      queueSize: 10000

resilience4j:
  circuitbreaker:
    instances:
      ctPullService:
        registerHealthIndicator: true
        eventConsumerBufferSize: 5
        automaticTransitionFromOpenToHalfOpenEnabled: true
        failureRateThreshold: 50
        minimumNumberOfCalls: 5
        permittedNumberOfCallsInHalfOpenState: 3
        slidingWindowSize: 5
        waitDurationInOpenState: 20s
        slidingWindowType: COUNT_BASED

      preFeeService:
        registerHealthIndicator: true
        eventConsumerBufferSize: 10
        automaticTransitionFromOpenToHalfOpenEnabled: true
        failureRateThreshold: 50
        minimumNumberOfCalls: 10
        permittedNumberOfCallsInHalfOpenState: 3
        slidingWindowSize: 10
        waitDurationInOpenState: 700ms
        slidingWindowType: COUNT_BASED

management:
  health:
    circuitbreakers:
      enabled: true
  endpoints:
    web:
      exposure:
        include: health
  endpoint:
    health:
      show-details: always
  tracing:
    enabled: true
  defaults:
    metrics:
      export:
        enabled: true


policyruleengine:
  secretKey : ###
  issuerKey: 'policy engine'
  client: "recharges-bff"
  role: "rule-admin"
  algo: 'HS256'
  dns: http://policy-rule-enginespringboot.nonprod.onus.paytmdgt.io/rule
  endpoint:
    fee: rule/apply/fees
  config:
    thread_timeout: 20000
    minPoolSize: 50
    maxPoolSize: 2000
    queueSize: 500
    keepAlive: 60
    http:
      max_connection: 10
      connect_timeout: 5000
      read_timeout: 10000
      request_timeout: 10000


lending:
  dns: https://stage-api.lending.paytm.com/lending/bureau
  endpoint:
    bureauReport: lending-bureau/internal/user/bureauDetails
    bureauPull: lending-bureau/service/v1/bureau/credit-report
    cirChange: lending-bureau/service/v1/bureau/cir-changes
    creditSummary: lending-bureau/service/v1/bureau/credit-summary
  config:
    http:
      max_connection: 10
      connect_timeout: 5000
      read_timeout: 10000
      request_timeout: 10000
    thread_timeout: 2000

tnc:
  dns: https://kyc-ite.paytm.in
  endpoint: /kyc/tnc/user
  executor:
    minPoolSize: 100
    maxPoolSize: 200
    queueSize: 50
    keepAlive: 60
  http:
    max_connection: 10
    connect_timeout: 1000
    read_timeout: 1000
    request_timeout: 1000



deleteFromAllSystems:
  creditCardCategoryIds: 131655
  pg_thread_timeout: 4000
  recent_thread_timeout: 4000
  reminder_thread_timeout: 4000
  saga_thread_timeout: 4000
  subscription_thread_timeout: 4000
  biller_thread_timeout: 4000
  cir_thread_timeout: 1000


pg:
  dns: http://pgp-qa5.paytm.in
  endpoint:
    deleteSavedCard: savedcardservice/HANDLER_INTERNAL/DEL_BIN
  config:
    http:
      max_connection: 1000
      connect_timeout: 500
      read_timeout:  1800
      request_timeout: 1800


ct:
  dns: https://in1-paytm.api.clevertap.com
  endpoint:
    pull: pull
  config:
    thread_timeout: 20000
    minPoolSize: 50
    maxPoolSize: 2000
    queueSize: 500
    keepAlive: 60
    http:
      max_connection: 10
      connect_timeout: 5000
      read_timeout: 10000
      request_timeout: 10000

sagaapi:
  response: '[
    {
        "product": {
            "category_id": 156705,
            "ope_logo_url": "https://assetscdn1.paytm.com/images/catalog/operators/*************.png",
            "schedulable": 0,
            "attributes": {
                "metaDescription": "Bill Payment of ICICI Bank Credit Card online and get exciting cashback offers at Paytm.com",
                "operator": "neft_ICICIBank",
                "schedulable": "0",
                "operator_display_label": "ICICI Bank",
                "merchantId": "1083910",
                "price": "1",
                "short_operator_name": "ICICI Bank",
                "paytype_display_label": "Bill Payment",
                "clientVisibility": "1",
                "image": "https://assetscdn1.paytm.com/images/catalog/product/P/PA/PAYBILL-PAYMENTCRED108391061E54033/2.png",
                "productId": "*********",
                "brandImage": "https://assetscdn1.paytm.com/images/catalog/operators/*************.png",
                "card_network_logo": "https://assetscdn1.paytm.com/images/catalog/product/P/PA/PAYBILL-PAYMENTANJA8860623A702885/22.png",
                "enable_bill_payment": "1",
                "service_label": "Financial Services",
                "gwParams": "{\"payment_modes_enabled\":[\"UPI\"]}",
                "paytype_label": "Credit Card Bill Payment",
                "operator_label": "ICICI Bank",
                "overrideAttrs": "{}",
                "pulse_myorders_event_flow": "1",
                "enable_visa_direct": "0",
                "request_type": "CC_BILL_PAYMENT",
                "card_network": "VISA",
                "payTypeSupported": "{\"cc\":0,\"emi\":0,\"nb\":1,\"ppi\":1,\"cod\":0,\"escrow\":0,\"paytmDigitalCredit\":0,\"dc\":1,\"upi\":1}",
                "displayName": "Bill Payment of ICICI Bank Credit Card",
                "prefetch": "0",
                "catalogProductId": "*********",
                "fulfilmentId": "24",
                "description": "[{\"title\":\"Product Details\",\"description\":\"\",\"attributes\":null}]",
                "operator_min_ios_version": "8.1.1",
                "card_bg": "https://assetscdn1.paytm.com/images/catalog/product/P/PA/PAYBILL-PAYMENTANJA8860623A702885/11.png",
                "operator_min_android_version": "8.1.1",
                "recharge_number_label": "Amount",
                "imageUrl": "https://assetscdn1.paytm.com/images/catalog/product/P/PA/PAYBILL-PAYMENTCRED108391020B403E5/**********.jpg",
                "bank_name_logo": "https://assetscdn1.paytm.com/images/catalog/product/P/PA/PAYBILL-PAYMENTANJA8860623A702885/10.png",
                "disclaimer": "98.3% transactions for this bank settle to card in less than 2 hours",
                "salesforceCaseId": "Default",
                "validate": "1",
                "bank_code": "ICICI",
                "service_display_label": "Credit Card",
                "threshold_turn_around_time": "600",
                "credit_card_length": "16",
                "extnAttrs": "{}",
                "bgCol": "linear-gradient(106.3deg, #AA3E27 0%, #730D12 94.27%)",
                "proceed_directly_to_PG": "1",
                "pulse_category_name": "onus_credit_card",
                "verticalId": "56",
                "productResources": "[{\"resourceId\":**********,\"isDefault\":0,\"type\":\"image\",\"url\":\"https://assetscdn1.paytm.com/images/catalog/product/P/PA/PAYBILL-PAYMENTCRED108391020B403E5/1_th.jpg\"}]",
                "service": "Financial Services",
                "metaTitle": "Bill Payment of ICICI Bank Credit Card | Paytm.com",
                "paytype": "Credit card",
                "chat_channel_id": "e00a3b98-a8b4-467c-b2eb-6432e6d0334a",
                "circle_label": ""
            },
            "brand": "neft_ICICIBank"
        },
        "favLabelId": "4315 81XX XXXX 9005:1157",
        "ope_logo_url": "https://assetscdn1.paytm.com/images/catalog/operators/*************.png",
        "configuration": {
            "recharge_number": "4315 81XX XXXX 9005",
            "price": 1157,
            "price_new": "Pay Bill",
            "recharge_number_2": "CIN_2020063022300bb0b74f62471d4c0c8821109116c9507",
            "recharge_number_3": "2020063022300bb0b74f62471d4c0c8821109116c9507"
        },
        "reminderNotificationEnabled": true,
        "operatorRecentData": {
            "creditCardId": "2020063022300bb0b74f62471d4c0c8821109116c9507"
        },
        "type": "recent",
        "priority": 3,
        "track_delivery": false,
        "cta": {
            "message_color": "#2DC68F",
            "button_text": "pay_bill",
            "message": "Paid on 31 May 2022",
            "collapse_text": "show_payment_details",
            "order_id": ***********
        },
        "operatorData": {
            "creditCardId": "2020063022300bb0b74f62471d4c0c8821109116c9507"
        },
        "updated_at": "2022-05-31T14:58:18.357Z",
        "favLabel": null,
        "created_desc": "Last Paid on 31 May 2022",
        "cta_obj": {
            "logoLabel": "",
            "cta": [
                {
                    "color": "#FFFFFF",
                    "deeplink": "",
                    "additionalInfo": {
                        "gaKey": "Pay Bill"
                    },
                    "label": "Pay Bill",
                    "type": "button"
                },
                {
                    "color": "",
                    "deeplink": "",
                    "label": "",
                    "type": "childCTA",
                    "childCta": [
                        {
                            "color": "#000000",
                            "deeplink": "",
                            "additionalInfo": {},
                            "label": "Delete Card",
                            "type": "deleteCard"
                        }
                    ],
                    "additionalinfo": {}
                },
                {
                    "color": "#00B8F5",
                    "deeplink": "",
                    "additionalInfo": {
                        "order_id": ***********
                    },
                    "label": "See Details",
                    "type": "seeDetails"
                }
            ],
            "heading": {
                "color": "#FFFFFF",
                "isHtml": false,
                "additionalInfo": {},
                "label": "4315 81XX XXXX 9005"
            },
            "heading1": {
                "color": "#FFFFFF",
                "isHtml": false,
                "additionalInfo": {},
                "label": null
            },
            "contact": {},
            "heading2": {
                "color": "#000000",
                "isHtml": false,
                "additionalInfo": {},
                "label": "Last Paid on 31 May 2022"
            },
            "logo": "https://assetscdn1.paytm.com/images/catalog/operators/*************.png"
        },
        "product_id": *********,
        "bills": [],
        "BillReminder": true
    }
]'
  responseHR: '[
  {
    "bannerId": "869658", (from )localisation 
    "id": "869658",(same as banner id)
    "uniqueId": "210211023789-electricity-bharatpur electricity services limited",
    "itemKey": "210211023789-electricity-bharatpur electricity services limited",(iten key same as unique id)
    "item_type": "Recco",(from code hardcoded)
    "fa_category": "Electricity",(serevice)
    "long_subtitle": "Bill Due on Thu, May 19",(heading2+heading 3)(non empty con)
    "subtitle": "Bill Due on Thu, May 19",(same as lon_subtitle)
    "sub_title_color": "#000000",(heading 2 color priority  ,optherwise heading 3 color)
    "operator_logo": "https://assetscdn1.paytm.com/images/catalog/operators/1495087944699.png",(from productinfo logo)
    "name": "Electricity Bill",(from prod Info attributes)//hr response
    "title": "210211023789",(recharge number)
    "due_date": "2022-05-19 0:00:00",(fav response)
    "ct_variant_id": 3,(due date se diffrenece)(can it be minus???)
    "cta": [{
      "deeplink": "paytmmp://utility?url=https://digitalcatalog.paytm.com/v1/mobile/getproductlist/26?$product_id=318747795$recharge_number=210211023789$price=6310.00",(from localisation)
      "label": "Recharge",
      "additional_info":{"url_type": "utility"}
    }],
    "dismiss_actions": [(child cta)
      {
        "label": "Mark As Paid",
        "additional_info":{

"icon_url": "https://assetscdn1.paytm.com/images/catalog/view_item/757719/1614667957218.png" (from localiosation)

},
        "deeplink": ""
      }
    ]
  }
]'
mnp:
  dns: https://digitalapiproxy-staging.paytm.com
  endpoint:
    fetchCorrectOperatorDetails: v1/mobile/getopcirclebyrange
  config:
    http:
      max_connection: 1000
      connect_timeout: 500
      read_timeout:  1800
      request_timeout: 1800
      thread_timeout: 2000
      minPoolSize: 150
      maxPoolSize: 150
      keepAlive: 60


aerospike:
  host: ***********
  port: 3000

shortnerapi:
  dns: http://staging.p-y.tm
  endpoint:
    shorten: /v2/shorten
  config:
    http:
      max_connection: 1000
      connect_timeout: 500
      read_timeout: 15
      request_timeout: 1800

sagacards:
  config:
    http:
      max_connection: 500
      connect_timeout: 500
      thread_timeout: 1000
      minPoolSize: 1000
      maxPoolSize: 2000
      keepAlive: 60
      queueSize: 10000
      read_timeout: 300
      request_timeout: 500

etag:
  validate: true

rule:
  service:
    dns: http://recharges-rule-processorspringboot.nonprod.onus.paytmdgt.io
    personalisation: /api/personalization
    bottomNavPersonalisation: /api/bottomNav/personalization
    http:
      max_connection: 10
      connect_timeout: 5000
      read_timeout: 1000
      request_timeout: 1000

ruleProcessor:
  executor:
    minPoolSize: 100
    maxPoolSize: 200
    queueSize: 50
    keepAlive: 60

cir:
  executor:
    minPoolSize: 100
    maxPoolSize: 200
    queueSize: 50
    keepAlive: 60
    thread_timeout: 4000

homepage:
  executor:
    thread_timeout: 20000
    minPoolSize: 50
    maxPoolSize: 2000
    queueSize: 500
    keepAlive: 60

planValidity:
  service:
    dns : http://digitalservicesnode.nonprod.onus.paytmdgt.io
    planValidityUrl : /pds/v1/planvalidity
    http:
      max_connection : 10
      connect_timeout : 5000
      read_timeout : 1000
      request_timeout : 1000
fastag:
  dns: http://mmv-service-staging.paytm.com
  vehicledetails: api/v1/mmv/fetchVehicleDetails
  passcode: e7ed4f36a8694d11b831123d69bf4a0e
  config:
    minPoolSize: 10
    maxPoolSize: 20
    queueSize: 500
    keepAlive: 60
  http:
    max_connection: 100
    connect_timeout: 10000
    read_timeout: 3000
    request_timeout: 2500
  color:
    cutoff: 50
  async:
    config:
      minPoolSize: 20
      maxPoolSize: 30
      queueSize: 500
      keepAlive: 60

remindMeLater:
  config:
    http:
      max_connection: 100
      connect_timeout: 1000
      read_timeout: 1000
      request_timeout: 1000

log:
  rotation:
    script:
      resourceName: log-rotation-script.py

# MySql Database Configuration
spring:
  datasource:
    slave:
      url: ***********************************
      username: app_recharge
      driver-class-name: com.mysql.cj.jdbc.Driver
      password: dRfvrf3t5yDw@1453
      maximum-pool-size: 10
      minimum-idle: 5
      idle-timeout: 30000
      max-lifetime: 600000
      connection-timeout: 20000
    master:
      url: ***********************************
      username: app_recharge
      driver-class-name: com.mysql.cj.jdbc.Driver
      password: dRfvrf3t5yDw@1453
      maximum-pool-size: 10
      minimum-idle: 5
      idle-timeout: 30000
      max-lifetime: 600000
      connection-timeout: 20000

consent:
  executor:
    minPoolSize: 750
    maxPoolSize: 1250
    queueSize: 500
    keepAlive: 60
    thread_timeout: 10000
  kafka:
    producer:
      bootstrap-server: ***********:9092
      topic-name: REMINDER_BILL_CONSENT
  service.http:
    max_connection: 1000
    connect_timeout: 5000
    read_timeout: 10000
    request_timeout: 10000
    bulk.request_timeout: 10000
    thread_timeout: 10000
  dns: http://recharge-consent-servicespringboot.nonprod.onus.paytmdgt.io
  get.consent.endpoint: /v1/consent/api/getConsent
  create.consent.endpoint: /v1/consent/api/createOrUpdateConsent
  create.bulk.consent.endpoint: /v1/consent/api/createOrUpdateBulkConsent
interstitial:
  executor:
    thread_timeout: 10
    minPoolSize: 25
    maxPoolSize: 2000
    queueSize: 100
    keepAlive: 60
  kafka:
    bootstrapServers: ***********:9092
    topicName15sRetry: interstitial_impression_retry_15s
    topicName30mRetry: interstitial_impression_retry_30min
    topicNameDwh: interstitialDwh
