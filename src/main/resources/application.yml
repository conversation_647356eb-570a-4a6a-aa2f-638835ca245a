server:
  port: "8080"
  tomcat:
    threads:
      max: 10000
  shutdown: graceful

bffServiceName: recharges-bff-beta
monitor:
  application:
    prefix: recharges-bff
  service:
    host: localhost
    queuesize: "10000"
prometheus:
  service:
    port: "8130"
cors:
  allowed:
    methods: GET,POST,DELETE,OPTIONS,PUT
    hosts: "*"
    headers: "*"
fulfillment:
  recharge:
    dns: https://recharge-staging.paytm.com
    validate: /v1/recharge/validate
    validateConfig:
      max_connection: 3000
      connect_timeout: 10000
      read_timeout: 58000
      request_timeout: 58000

recharge:
  express:
    dns: https://digitalproxy-staging.paytm.com
    hostName: digitalproxy-staging.paytm.com
    verify: /digitalrecharge/v1/expressrecharge/verify
    checkout: /digitalrecharge/v1/expressrecharge/checkout
    deleteFromAllSystems: v1/expressrecharge/deletefromallsystems
  bff:
    checkoutUrl: https://digitalapiproxy.paytm.com/digitalrecharge/v1/expressrecharge/checkout
    CIPHER_ALGORITHM : AES
  saga:
    dns: https://run.mocky.io
    hostName: run.mocky.io
    donotRemindme: v3/41d4dcdc-e354-480e-b7aa-f6f40688862f
  reminder:
    dns: https://run.mocky.io
    hostName: run.mocky.io
    donotRemindme: v3/ae87a6ee-872c-4f0b-b306-8fe29095e878
cart:
  hostUri: https://checkout-staging.paytm.com
  key: recharges
  secret: ###
express:
  ottPid: 1201258505
  appInvokeDevice: '3P'
  rechargesFulfillmentServiceId: 3,6,24,105
  automatic_pid: 1234580515
  ott_service_name: "subscriptions"
  subscription_frequency: 1
  subscription_frequency_unit: "ONDEMAND"
  #  subscription_frequency_unit_onDemand: "ONDEMAND"
  #  subscription_frequency_unit_day: "DAY"
  subscription_grace_days: "0"
  subscription_retry_count: "2"
  subscription_max_amount: 15000
urls:
  fulfillmentRecharge: https://recharge-staging.paytm.com
  fetchCvrDataUrl: https://recharge-staging.paytm.com/v1/offloadcpu/data?key=cvrDataLib&pageSize=1000&pageNumber=
localisation:
  service: DIGITALRECHARGE
  language: all
  environment: staging
  reloadInterval: 20
recentLocalisation:
  service: FAVOURITE
  language: all
  environment: staging
scheduler:
  cvrReloadInterval: PT1800S
  featureConfigReloadInterval: PT1M
recharges:
  recharge_fsids: '3,6,24,105'
  category_map: '{"166260": "fastag","18": "dth", "131865": "cylinder", "26": "electricity","17": "mobile", "21": "mobile"}'
  display_summary:
    electricity: '[{"keys":["Due Date"], "priority": 0}, {"keys": ["Bill Date"], "priority": 1}, {"keys": ["Consumer Name"], "priority": 2}, {"keys": ["First Name", "Last Name"], "priority": 3, "label": "Consumer Name"}]'
    cylinder: '[{"keys":["Delivery Address"], "priority": 0}, {"keys":["Gas Agency"], "priority": 1}, {"keys":["Agency Name"], "priority": 1}, {"keys":["Booking Date"], "priority": 2}]'
    dth: '[{"keys":["Due Date"], "priority": 0}]'
    fastag: '[{"keys":["Wallet balance"], "priority": 0}, {"keys":["Wallet Balance"], "priority": 0}, {"keys":["wallet balance"], "priority": 0}, {"keys":["WALLET BALANCE"], "priority": 0}]'
  meta_data:
    copy_keys: "Consumer Name"
  reminder:
    corePoolSize: 50
    http:
      max_connection: 100
      connect_timeout: 500
      read_timeout: 500
      request_timeout: 500
  reminderdelete:
    corePoolSize: 50
    http:
      max_connection: 100
      connect_timeout: 500
      read_timeout: 500
      request_timeout: 500
  express:
    http:
      max_connection: 2000
      connect_timeout: 10000
      read_timeout: 56000
      request_timeout: 56000
      threadTimeout: 60000
    executor:
      verify:
        minPoolSize: 100
        maxPoolSize: 3000
        queueSize: 5000
        keepAlive: 60
      checkout:
        minPoolSize: 50
        maxPoolSize: 2000
        queueSize: 5000
        keepAlive: 60
  cron:
    http:
      max_connection: 10
      connect_timeout: 5000
      read_timeout: 10000
      request_timeout: 10000
  encription:
    key: ###
    iv: ###
ruleEngine:
  host: http://**********/
  endpoints:
    offloadcpuData: v1/offloadcpu/data

rental:
  host: 'https://securegw-stage.paytm.in'
  fetchCardIndexNoPath: /theia/api/v1/fetchCardIndexNo?client=IN&referenceId=
  secretKey: ###
  issuerKey: 'ts'
  algo: 'HS256'
  productId: 1201345018
  categoryIds: 262072
  http:
    max_connection: 10
    connect_timeout: 10000
    read_timeout: 60000
    request_timeout: 60000

risk:
  categoryIds: 262072

favourite:
  frequentOrder:
    config:
      connection_timeout: 10000
      read_timeout: 10000
      max_connections: 1000
      thread_timeout: 20000
      minPoolSize: 50
      maxPoolSize: 2000
      queueSize: 10000
      keepAlive: 60

saga:
  dns: https://run.mocky.io/v3/58cb2c79-7902-46dd-968f-3aaae77c127b
  endpoint:
    recents: /api/customer/{customer_id}/recents
    deleteFromAllSystems: api/dropoffreminderstatus
    customerName: /api/customername
  config:
    http:
      max_connection: 10
      connect_timeout: 5000
      read_timeout: 10000
      request_timeout: 10000
  dropOff:
    config:
      http:
        max_connection: 10
        connect_timeout: 5000
        read_timeout: 10000
        request_timeout: 10000
  executor:
    verify:
      minPoolSize: 100
      maxPoolSize: 3000
      queueSize: 5000
      keepAlive: 60
    checkout:
      minPoolSize: 50
      maxPoolSize: 2000
      queueSize: 5000
      keepAlive: 60

bff:
  cloud:
    vault:
      authentication: approle
      uri: https://vault-staging.paytmmall.io
      app-role:
        role-id: a6e26382-b732-49fa-e075-d6a1390d0c22
        secret-id: 64bf44b9-3b2d-b83e-e87f-f4038d58eec5
      kv:
        enabled: true
        backend: kv
        default-context: kv/digital/recharges/recharge-bff/secrets
        application-name: kv/digital/recharges/recharge-bff/secrets
      generic:
        enabled: false
      fail-fast: true
  config:
    blockReq:
      corepoolsize: 5
      maxpoolsize: 100
      queuecapacity: 50


mnp:
  dns: http://mnp-internal.prod.paytmdgt.io
  endpoint:
    fetchCorrectOperatorDetails: v1/mobile/getopcirclebyrange
  config:
    http:
      max_connection: 1000
      connect_timeout: 500
      read_timeout:  1800
      request_timeout: 1800
      thread_timeout: 100000
      minPoolSize: 150
      maxPoolSize: 150
      keepAlive: 60
